--- Creating the racetrack DB here
\set RACETRAC<PERSON>_USER `echo ${RACETRACK_USER:-racetrack}`
\set RACETRACK_DB `echo ${RACETRACK_DB:-${RACETRACK_USER}}`
\set RACETRACK_PASSWORD `echo ${RACETRACK_PASSWORD:-password}`

CREATE USER :RACETRACK_USER WITH PASSWORD :'RACE<PERSON><PERSON><PERSON>_PASSWORD';
CREATE DATABASE :RACETRACK_DB WITH OWNER :RACETRACK_USER ENCODING 'UTF-8';

\connect :RACETRACK_DB

CREATE EXTENSION IF NOT EXISTS postgis;