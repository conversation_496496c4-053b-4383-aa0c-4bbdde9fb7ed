--- Clean up database for the first use
\set RACETR<PERSON><PERSON>_USER `echo ${RACETRACK_USER:-racetrack}`
\set RACETRACK_DB `echo ${RACETRACK_DB:-${RACETRACK_USER}}`

ALTER USER :RACETRACK_USER CREATEDB;

\connect :RACETR<PERSON>K_DB :RACETR<PERSON>K_USER

truncate django_session;

UPDATE auth_user SET username='admin', password='', first_name='Admin', last_name='User' WHERE id=1;
DELETE FROM login_consumer_remoteuser WHERE local_user_id=1 OR remote_user_id=1;
INSERT INTO login_consumer_remoteuser (server, local_user_id, remote_user_id, callback_token)
    VALUES ('monza', 1, 1, '');

\set OAUTH_KEY `echo ${OAUTH_KEY:-longbeach-key}`
\set OAUTH_SECRET `echo ${OAUTH_SECRET:-longbeach-secret}`
UPDATE oauth_client_server SET key=:'OAUTH_KEY', secret=:'OAUTH_SECRET', created_on=now();

\set MONZA_URL `echo ${MONZA_URL:-https://monza.racetracks.docker}`
update oauth_client_serverlink set uri = regexp_replace(uri, '(https?://[^/]+)', :'MONZA_URL') where server_id = 'monza';

CREATE FUNCTION pg_temp.oauth_clients(
   clients VARCHAR[]
) RETURNS VOID AS $$
DECLARE
    i integer := 0;
    client varchar;
BEGIN
    FOREACH client IN ARRAY clients
    LOOP
        i := i + 1;
        INSERT INTO oauth_server_client VALUES (i, client, '', 't', 1, now(), 1);
        INSERT INTO oauth_server_clientcredentials VALUES (i, i, client || '-key', client || '-secret', 't', 1, now());
    END LOOP;
END;
$$ LANGUAGE plpgsql;

\set OAUTH_CLIENTS `echo ${OAUTH_CLIENTS:-}`
SELECT pg_temp.oauth_clients(string_to_array(:'OAUTH_CLIENTS', ','));