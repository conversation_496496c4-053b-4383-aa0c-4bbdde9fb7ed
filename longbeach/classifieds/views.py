"""API views for classifieds."""

import operator
import re
from datetime import datetime, timedelta
from functools import reduce

from django.db.models import <PERSON><PERSON>an<PERSON>ield, Case, DateField, Q, When
from django.utils.timezone import now
from rest_framework import generics, viewsets
from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response
from rest_framework.serializers import ValidationError
from rest_framework.views import APIView

from longbeach.business.api import BOOLEAN_FALSE_STRINGS
from longbeach.utils.search import term_to_query

from .models import Category, ClassifiedAd
from .serializers import CategorySerializer, ClassifiedAdSerializer

FIRST_AD_DATE = datetime(2010, 1, 1, tzinfo=now().tzinfo)


def clean_string(string: str) -> str:
    """Remove any non-printable characters from a string."""
    return re.sub(r"[^\x20-\x7E]", "", string).strip()


class Pagination(PageNumberPagination):
    """A simple API pagination."""

    page_size = 14  # Default page size
    page_size_query_param = "page_size"  # Query parameter for page size
    max_page_size = 100  # Maximum page size limit


class CategoryViewSet(generics.ListAPIView):
    """
    API endpoint for listing categories.

    If `masthead_id` is given, the response includes the `count` of ads in each
    category.
    """

    parser_classes = ()
    queryset = Category.objects.order_by("order")
    serializer_class = CategorySerializer

    def get_queryset(self):
        queryset = super().get_queryset()

        if show_similar_ads := self.request.GET.get("show_similar_ads"):
            queryset = queryset.filter(
                show_similar_ads=(
                    show_similar_ads.strip().lower()
                    not in BOOLEAN_FALSE_STRINGS
                )
            )

        return queryset


class ClassifiedAdViewSet(viewsets.ReadOnlyModelViewSet):
    """API endpoint for getting classified ads."""

    pagination_class = Pagination
    parser_classes = ()
    queryset = ClassifiedAd.objects.prefetch_related(
        "mastheads",
        "images",
    )
    serializer_class = ClassifiedAdSerializer

    def get_queryset(self):
        """Filter the ads."""
        today = now()
        queryset = (
            super()
            .get_queryset()
            .filter(
                expiration_date__gte=today,
                publication_date__lte=today,
            )
        )

        # If fetching a specific ad, filters do not apply
        if "pk" in self.kwargs:
            return queryset

        masthead_ids = self.request.GET.get("masthead_id")

        if not masthead_ids:
            raise ValidationError("Masthead required")

        # Filter on multiple mastheads with a comma separated list of IDs
        masthead_ids = masthead_ids.split(",")

        # If multiple mastheads are specified, assume the client is fetching
        # ads for a masthead and its cluster mastheads. The first one is the
        # site being viewed and the others are sites in the cluster.
        # Add an extra field that tells whether the ad is meant for the first
        # site (true) or cluster sites (false), then sort them so the site's
        # ads appear before the cluster. This allows pagination to work while
        # giving a field to differentiate the ads from the cluster sites.
        if len(masthead_ids) > 1:
            queryset = queryset.filter(mastheads__site_id__in=masthead_ids)
            queryset = queryset.annotate(
                is_for_masthead=Case(
                    When(mastheads__site_id=masthead_ids[0], then=True),
                    default=False,
                    output_field=BooleanField(),
                ),
            )

            order = self.request.GET.get("order", "cluster_last")

            if order == "cluster_last":
                month_ago = today - timedelta(days=30)
                queryset = (
                    queryset
                    # Only show cluster ads from the past month
                    .filter(
                        publication_date__gte=Case(
                            When(
                                mastheads__site_id=masthead_ids[0],
                                then=FIRST_AD_DATE,
                            ),
                            default=month_ago,
                            output_field=DateField(),
                        ),
                    ).order_by(
                        "-is_for_masthead", *ClassifiedAd._meta.ordering
                    )
                )
            elif order == "date":
                queryset = queryset.order_by(
                    "-publication_date",
                    "-is_for_masthead",
                    "sort_key",
                    "id",
                )
        else:
            queryset = queryset.filter(mastheads__site_id=masthead_ids[0])

        # Filter on multiple categories with a comma separated list of IDs
        if category_ids := self.request.GET.get("category_id"):
            category_ids = category_ids.split(",")
            queryset = queryset.filter(
                reduce(
                    operator.or_,
                    [
                        Q(category=category_id)
                        | Q(category__parent=category_id)
                        for category_id in category_ids
                    ],
                )
            )
        else:
            # Show all ads but exclude some categories
            queryset = queryset.filter(category__show_in_index_page=True)

        # Basic word search
        if query := clean_string(self.request.GET.get("q", "")):
            queryset = queryset.filter(term_to_query(query, {"title"}))

        if location := clean_string(self.request.GET.get("location", "")):
            queryset = queryset.filter(term_to_query(location, {"location"}))

        if date := self.request.GET.get("date", "").strip():
            queryset = queryset.filter(publication_date=date)

        return queryset


class SitemapView(APIView):
    """An API endpoint for sitemap data."""

    def get(self, request):
        """Get all the classifieds for the given year."""
        if not (masthead_id := self.request.GET.get("masthead_id")):
            raise ValidationError("Masthead required")

        if not (year := self.request.GET.get("year")):
            raise ValidationError("Year required")

        today = now()

        return Response(
            ClassifiedAd.objects.filter(
                expiration_date__gte=today,
                mastheads__site_id=masthead_id,
                publication_date__year=year,
            )
            .values(
                "id", "category_id", "title", "location", "publication_date"
            )
            .order_by("id")
            .iterator()
        )
