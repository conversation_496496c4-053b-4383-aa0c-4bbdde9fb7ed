"""Models for classifieds."""

from django.db import models
from valencia_storage import ValenciaStorage

from longbeach.business.models import BusinessMasthead

MAX_LENGTH = 100


class Category(models.Model):
    """A category of classified ads."""

    parent = models.ForeignKey(
        "self",
        blank=True,
        null=True,
        on_delete=models.CASCADE,
        related_name="children",
    )
    name = models.CharField(max_length=MAX_LENGTH)
    slug = models.CharField(max_length=MAX_LENGTH)
    description = models.TextField()
    show_in_navigation = models.BooleanField()
    show_similar_ads = models.BooleanField(default=True)
    show_ads = models.BooleanField()
    show_in_index_page = models.BooleanField()
    show_cluster_ads = models.BooleanField()
    order = models.PositiveSmallIntegerField()
    # Pongrass classification number, null for top level categories, 3 digits
    # for standard categories, letter + 4 digits for subcategories, only in
    # Trades & Services at the moment
    classification = models.Char<PERSON>ield(
        max_length=5,
        unique=True,
        blank=True,
        null=True,
    )
    # Optional OwnLocal category slug used for trades & services redirect URLs
    # if the name differs from Pongrass
    ownlocal_slug = models.Char<PERSON><PERSON>(max_length=MAX_LENGTH, blank=True)

    class Meta:
        """Set the default order."""

        ordering = ("order",)
        verbose_name_plural = "Categories"

    def __str__(self):
        """Get the name."""
        return self.name


class ClassifiedAd(models.Model):
    """A classified ad or obituary."""

    # Unique ID for the specific ad
    original_id = models.CharField(db_index=True, max_length=50)
    # Pongrass booking ID common among ads shared with multiple mastheads
    reference = models.CharField(max_length=50)
    mastheads = models.ManyToManyField(
        BusinessMasthead,
        related_name="classified_ads",
    )
    title = models.CharField(max_length=MAX_LENGTH)
    text = models.TextField(blank=True)
    category = models.ForeignKey(
        Category,
        on_delete=models.CASCADE,
        null=True,
        related_name="ads",
    )
    # Optional category name to override the regular one, used for 'service
    # features'
    category_text = models.CharField(blank=True, max_length=MAX_LENGTH)
    url = models.URLField(blank=True)
    publication_date = models.DateField()
    expiration_date = models.DateField(db_index=True)
    logo = models.ImageField(blank=True, null=True, storage=ValenciaStorage())
    sort_key = models.CharField(blank=True, max_length=MAX_LENGTH)

    location = models.CharField(max_length=MAX_LENGTH, blank=True)
    latitude = models.FloatField(blank=True, null=True)
    longitude = models.FloatField(blank=True, null=True)

    customer_name = models.CharField(max_length=MAX_LENGTH, blank=True)
    customer_phone = models.CharField(max_length=11, blank=True)
    customer_email = models.EmailField(blank=True)
    customer_town = models.CharField(max_length=MAX_LENGTH, blank=True)
    customer_state = models.CharField(max_length=3, blank=True)
    customer_postcode = models.CharField(max_length=4, blank=True)

    # Obituary specific fields
    funeral_home_name = models.CharField(max_length=MAX_LENGTH, blank=True)
    funeral_home_address = models.CharField(max_length=MAX_LENGTH, blank=True)
    funeral_home_city = models.CharField(max_length=MAX_LENGTH, blank=True)
    funeral_home_postcode = models.CharField(max_length=4, blank=True)
    funeral_home_state = models.CharField(max_length=MAX_LENGTH, blank=True)
    funeral_venue_name = models.CharField(max_length=MAX_LENGTH, blank=True)
    funeral_venue_address = models.CharField(max_length=MAX_LENGTH, blank=True)
    funeral_venue_city = models.CharField(max_length=MAX_LENGTH, blank=True)
    funeral_venue_state = models.CharField(max_length=MAX_LENGTH, blank=True)
    funeral_venue_postcode = models.CharField(
        max_length=MAX_LENGTH,
        blank=True,
    )
    funeral_date = models.CharField(max_length=MAX_LENGTH, blank=True)
    funeral_start_time = models.CharField(max_length=MAX_LENGTH, blank=True)
    year_born = models.PositiveSmallIntegerField(blank=True, null=True)
    year_deceased = models.PositiveSmallIntegerField(blank=True, null=True)
    date_born = models.DateField(blank=True, null=True)
    date_deceased = models.DateField(blank=True, null=True)
    quote_text = models.CharField(max_length=MAX_LENGTH, blank=True)
    enable_comments = models.BooleanField(default=True)

    class Meta:
        """Set the default order."""

        # Order by ID as well to ensure consistent sorting across pagination
        ordering = ("-publication_date", "sort_key", "id")

    def __str__(self):
        return self.title


class ClassifiedAdImage(models.Model):
    """An image associated with an ad."""

    classified_ad = models.ForeignKey(ClassifiedAd, models.CASCADE, "images")
    image = models.ImageField(blank=True, storage=ValenciaStorage())
    order = models.PositiveSmallIntegerField()

    class Meta:
        """Set the default order."""

        ordering = ("order",)

    def __str__(self):
        return f"{self.classified_ad_id} image #{self.order}"
