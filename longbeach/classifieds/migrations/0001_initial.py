# Generated by Django 3.1.14 on 2024-06-26 00:49

from django.db import migrations, models
import valencia_storage.storage


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('business', '0036_businessfeaturead_ad_copy_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='ClassifiedAd',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('original_id', models.CharField(db_index=True, max_length=50)),
                ('title', models.CharField(max_length=100)),
                ('text', models.TextField(blank=True)),
                ('category', models.CharField(max_length=100)),
                ('url', models.URLField(blank=True)),
                ('image', models.ImageField(blank=True, storage=valencia_storage.storage.ValenciaStorage(), upload_to='')),
                ('publication_date', models.DateField()),
                ('expiration_date', models.DateField(db_index=True)),
                ('customer_name', models.CharField(blank=True, max_length=100)),
                ('customer_phone', models.CharField(blank=True, max_length=11)),
                ('customer_email', models.EmailField(blank=True, max_length=254)),
                ('customer_address1', models.CharField(blank=True, max_length=100)),
                ('customer_address2', models.CharField(blank=True, max_length=100)),
                ('customer_town', models.CharField(blank=True, max_length=100)),
                ('customer_state', models.CharField(blank=True, max_length=3)),
                ('customer_postcode', models.CharField(blank=True, max_length=4)),
                ('funeral_home_name', models.CharField(blank=True, max_length=100)),
                ('funeral_home_address', models.CharField(blank=True, max_length=100)),
                ('funeral_home_city', models.CharField(blank=True, max_length=100)),
                ('funeral_home_postcode', models.CharField(blank=True, max_length=4)),
                ('funeral_home_state', models.CharField(blank=True, max_length=100)),
                ('year_born', models.PositiveSmallIntegerField(blank=True, null=True)),
                ('year_deceased', models.PositiveSmallIntegerField(blank=True, null=True)),
                ('mastheads', models.ManyToManyField(related_name='classified_ads', to='business.BusinessMasthead')),
            ],
        ),
    ]
