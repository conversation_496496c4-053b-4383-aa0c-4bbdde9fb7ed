# Generated by Django 3.1.14 on 2024-08-09 03:06

from django.db import migrations, models
import django.db.models.deletion
import valencia_storage.storage


def copy_images(apps, schema_editor):
    """Migrate the image URLs to the new related model."""
    ClassifiedAd = apps.get_model("classifieds", "ClassifiedAd")
    ClassifiedAdImage = apps.get_model("classifieds", "ClassifiedAdImage")

    for ad in ClassifiedAd.objects.all().iterator():
        ClassifiedAdImage.objects.create(
            classified_ad=ad,
            image=ad.image,
            order=1,
        )


def reverse_images(apps, schema_editor):
    """Copy the image back to the original model."""
    ClassifiedAdImage = apps.get_model("classifieds", "ClassifiedAdImage")

    for image in ClassifiedAdImage.objects.select_related(
        "classified_ad",
    ).iterator():
        image.classified_ad.image = image.image
        image.classified_ad.save()


class Migration(migrations.Migration):
    dependencies = [
        ("classifieds", "0003_reference"),
    ]

    operations = [
        migrations.AddField(
            model_name="classifiedad",
            name="logo",
            field=models.ImageField(
                blank=True,
                null=True,
                storage=valencia_storage.storage.ValenciaStorage(),
                upload_to="",
            ),
        ),
        migrations.CreateModel(
            name="ClassifiedAdImage",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "image",
                    models.ImageField(
                        blank=True,
                        storage=valencia_storage.storage.ValenciaStorage(),
                        upload_to="",
                    ),
                ),
                ("order", models.PositiveSmallIntegerField()),
                (
                    "classified_ad",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="images",
                        to="classifieds.classifiedad",
                    ),
                ),
            ],
            options={
                "ordering": ("order",),
            },
        ),
        migrations.RunPython(copy_images, reverse_images),
        migrations.RemoveField(
            model_name="classifiedad",
            name="image",
        ),
    ]
