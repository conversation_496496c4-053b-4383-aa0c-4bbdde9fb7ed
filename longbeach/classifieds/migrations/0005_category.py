# Generated by Django 3.1.14 on 2024-08-29 05:04

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("classifieds", "0004_images"),
    ]

    operations = [
        migrations.RenameField(
            model_name="classifiedad",
            old_name="category",
            new_name="category_text",
        ),
        migrations.CreateModel(
            name="Category",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("slug", models.Char<PERSON>ield(max_length=100)),
                ("description", models.TextField()),
                ("show_in_navigation", models.BooleanField()),
                ("show_ads", models.BooleanField()),
                ("show_in_index_page", models.BooleanField()),
                ("show_cluster_ads", models.BooleanField()),
                ("order", models.PositiveSmallIntegerField()),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="children",
                        to="classifieds.category",
                    ),
                ),
                (
                    "classification",
                    models.PositiveSmallIntegerField(
                        unique=True, blank=True, null=True
                    ),
                ),
            ],
            options={"ordering": ("order",), "verbose_name_plural": "Categories"},
        ),
        migrations.AddField(
            model_name="classifiedad",
            name="category",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="ads",
                to="classifieds.category",
            ),
        ),
        migrations.AlterModelOptions(
            name="classifiedad",
            options={"ordering": ("-publication_date",)},
        ),
        migrations.AlterField(
            model_name="classifiedad",
            name="latitude",
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="classifiedad",
            name="longitude",
            field=models.FloatField(blank=True, null=True),
        ),
    ]
