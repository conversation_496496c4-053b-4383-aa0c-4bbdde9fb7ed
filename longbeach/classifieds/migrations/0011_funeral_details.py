# Generated by Django 3.1.14 on 2025-01-28 05:31

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("classifieds", "0010_add_show_cluster_ads"),
    ]

    operations = [
        migrations.AddField(
            model_name="classifiedad",
            name="funeral_date",
            field=models.Char<PERSON>ield(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name="classifiedad",
            name="funeral_start_time",
            field=models.Char<PERSON>ield(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name="classifiedad",
            name="funeral_venue_address",
            field=models.Char<PERSON><PERSON>(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name="classifiedad",
            name="funeral_venue_city",
            field=models.Char<PERSON>ield(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name="classifiedad",
            name="funeral_venue_name",
            field=models.Char<PERSON><PERSON>(blank=True, max_length=100),
        ),
        migrations.Add<PERSON>ield(
            model_name="classifiedad",
            name="funeral_venue_postcode",
            field=models.<PERSON>r<PERSON><PERSON>(blank=True, max_length=100),
        ),
        migrations.Add<PERSON>ield(
            model_name="classifiedad",
            name="funeral_venue_state",
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name="classifiedad",
            name="quote_text",
            field=models.CharField(blank=True, max_length=100),
        ),
    ]
