import datetime
import json
import logging
import re
import urllib.parse
from enum import Enum
from typing import Optional, Union

import requests
from bleach import clean
from django.conf import settings
from django.core.files.base import ContentFile
from requests.exceptions import HTTPError, RequestException

from longbeach.business.models import BusinessMasthead

from .models import Category, ClassifiedAd, ClassifiedAdImage

logger = logging.getLogger()


class FujiFeedDocIterator:
    def __init__(self, source, limit=None, offset=None, session=None):
        self.source = source
        self._limit = limit or settings.FUJI_CLASSIFIEDS_LIMIT
        self._offset = offset or 0
        self._index = self._offset
        self._start = self._offset
        self._session = session
        self._total_objects = 0
        self._objects = self._refresh_objects()

    def _refresh_objects(self):
        path = f"/api/v1/feeddocument/?ignore=False&feed__source={self.source}&limit={self._limit}&offset={self._offset}&order_by=created_on"

        feed_doc_url = urllib.parse.urljoin(settings.FUJI_HOST, path)

        try:
            resp = self._session.get(feed_doc_url)
            resp.raise_for_status()
        except (AttributeError, RequestException, HTTPError) as e:
            logger.error("%s" % e.message)
            return []
        data = json.loads(resp.content)

        self._total_objects = data["meta"]["total_count"]
        logger.info(
            "[_refresh_objects] _limit: %s, _offset: %s, _total_objects: %s"
            % (self._limit, self._offset, self._total_objects)
        )
        self._offset += self._limit

        # there might a bug with order_by so make sure ads are sorted
        data["objects"].sort(key=lambda ad: ad["created_on"])
        return data["objects"]

    def __iter__(self):
        i = 0

        while (self._total_objects > 0) and (
            self._index < self._total_objects
        ):
            try:
                yield self._objects[i]
                self._index += 1
                i += 1
            except IndexError:
                i = 0
                self._objects = self._refresh_objects()
        logger.info(
            "%s objects processed. (_index: %s, _start: %s, _limit: %s, _total_objects: %s)"
            % (
                (self._index - self._start),
                self._index,
                self._start,
                self._limit,
                self._total_objects,
            )
        )


class Result(Enum):
    """Fuji import result types."""

    AD_UPDATED = 0
    IMAGE_SAVED = 1
    IMAGE_NOT_SAVED = 2
    NO_DATA = 3
    NO_AD = 4
    NO_MASTHEAD = 5
    NO_CATEGORY = 6


def parse_date(date: str) -> Optional[datetime.date]:
    """Parse a date string in Pongrass' format."""
    try:
        return datetime.datetime.strptime(date, "%d-%m-%Y").date()
    except ValueError:
        return None


def process_feed_doc(feed_doc: dict) -> Union[Result, dict]:
    try:
        data = feed_doc["data"]
    except KeyError:
        logger.error("No data:\n%s" % feed_doc)
        return Result.NO_DATA

    type_ = data.get("type")

    if type_ and type_ != "story":
        return process_image_doc(feed_doc, type_)

    site_id = data.get("site_id")

    try:
        masthead = BusinessMasthead.objects.get(site_id=site_id)
    except BusinessMasthead.DoesNotExist:
        logger.error(
            "masthead not found for %s, %s"
            % (site_id, feed_doc.get("resource_uri", ""))
        )
        return Result.NO_MASTHEAD

    class_id = data.get("subclass_id", "") or data.get("class_id", "")

    try:
        category = Category.objects.get(classification=class_id)
    except Category.DoesNotExist:
        return Result.NO_CATEGORY

    try:
        publication_date = datetime.datetime.strptime(
            data.get("pubdate"), "%Y-%m-%dT%H:%M:%S"
        )
    except (TypeError, ValueError):
        logger.error("Invalid pubdate: %s" % feed_doc.get("resource_uri", ""))
        publication_date = datetime.datetime.today()

    try:
        expiration_date = datetime.datetime.strptime(
            data.get("stopdate"), "%Y-%m-%dT%H:%M:%S"
        )
    except (TypeError, ValueError):
        logger.error("Invalid stopdate: %s" % feed_doc.get("resource_uri", ""))
        expiration_date = datetime.datetime(1900, 1, 1)

    try:
        latitude = float(data.get("latitude"))
    except (TypeError, ValueError):
        latitude = None

    try:
        longitude = float(data.get("longitude"))
    except (TypeError, ValueError):
        longitude = None

    date_born = parse_date(data.get("date_born", ""))
    date_deceased = parse_date(data.get("date_deceased", ""))

    original_id = feed_doc.get("original_id")

    title_length = ClassifiedAd._meta.get_field("title").max_length
    # For tributes, build the title from the deceased's name, or fall back to
    # the title field
    title = f"{data.get('lastname', '')}, {data.get('firstname', '')}".strip(
        ", ",
    ) or data.get("title", "")

    category_text = data.get("subcategory", "") or data.get("categories", "")
    if category_text == category.name:
        category_text = ""

    text = clean(
        data.get("html_body", "") or data.get("body", ""),
        tags={"b", "em", "i", "li", "ol", "strong", "ul"},
        strip=True,
    )

    ad_data = {
        "original_id": original_id,
        "reference": data.get("ad_ref", ""),
        "category": category,
        # Service features have custom category names
        "category_text": category_text,
        "title": title[:title_length],
        "text": text,
        "url": data.get("embed_URL", "").strip(),
        "publication_date": publication_date,
        "expiration_date": expiration_date,
        "latitude": latitude,
        "longitude": longitude,
        "location": data.get("location", ""),
        "sort_key": data.get("sortwords", ""),
        "customer_name": data.get("customer_name", ""),
        "customer_phone": re.sub(r"\D+", "", data.get("customer_phone", "")),
        "customer_email": data.get("customer_email", ""),
        "customer_town": data.get("customer_town", ""),
        "customer_state": data.get("customer_state", ""),
        "customer_postcode": data.get("customer_postcode", ""),
        "funeral_home_name": data.get("funeral_home_name", ""),
        "funeral_home_address": data.get("funeral_home_address", ""),
        "funeral_home_city": data.get("funeral_home_city", ""),
        "funeral_home_postcode": data.get("funeral_home_postcode", ""),
        "funeral_home_state": data.get("funeral_home_state", ""),
        "funeral_venue_name": data.get("funeral_venue_name", ""),
        "funeral_venue_address": data.get("funeral_venue_address", ""),
        "funeral_venue_city": data.get("funeral_venue_city", ""),
        "funeral_venue_state": data.get("funeral_venue_state", ""),
        "funeral_venue_postcode": data.get("funeral_venue_postcode", ""),
        "funeral_date": data.get("funeral_date", ""),
        "funeral_start_time": data.get("funeral_start_time", ""),
        "quote_text": data.get("quote_text", ""),
        "year_born": data.get("year_born"),
        "year_deceased": data.get("year_deceased"),
        "date_born": date_born,
        "date_deceased": date_deceased,
        "enable_comments": data.get("hide_comments", "false") == "false",
    }

    if original_id and (
        ads := ClassifiedAd.objects.filter(original_id=original_id)
    ):
        ads.update(**ad_data)

        for ad in ads:
            if masthead not in ad.mastheads.all():
                ad.mastheads.add(masthead)
            logger.warning(
                "existing ad updated: %s %s %s, %s"
                % (
                    ad.id,
                    ad.title,
                    [m.site_id for m in ad.mastheads.all()],
                    ad_data,
                )
            )
        return Result.AD_UPDATED
    ad_data["masthead"] = masthead
    return ad_data


def process_image_doc(feed_doc, type_):
    """Process images and logos."""
    try:
        img_url = urllib.parse.urljoin(
            settings.FUJI_HOST, feed_doc.get("data").get("url")
        )
    except AttributeError as e:
        logger.error(
            "Can't get image url: %s - %s",
            feed_doc.get("resource_uri"),
            e,
        )
        return Result.IMAGE_NOT_SAVED

    try:
        resp = requests.get(img_url, verify=settings.SSL_VERIFY_CERTIFICATE)
        resp.raise_for_status()
    except (AttributeError, RequestException, HTTPError) as e:
        logger.exception(e)
        return Result.IMAGE_NOT_SAVED

    try:
        ads = ClassifiedAd.objects.filter(
            original_id=feed_doc.get("related_id"),
        )
    except Exception as e:
        logger.error(
            "Could not retrieve ads for image %s: %s",
            feed_doc,
            e.message,
        )
        return Result.NO_AD

    if not ads:
        logger.warning("No ads for image %s" % feed_doc)
        return Result.NO_AD

    # Get the order from the file name, e.g. /url/foo_1.jpg -> 1
    try:
        order = int(img_url.split("/").pop().split(".")[0].split("_")[-1])
    except (IndexError, ValueError):
        # Most likely the print preview
        order = 0

    for ad in ads.iterator():
        if type_ == "logo":
            image_instance = None
            image = ad.logo
        else:
            image_instance, _ = ClassifiedAdImage.objects.get_or_create(
                classified_ad=ad,
                order=order,
            )
            image = image_instance.image

        try:
            # This also saves the model instance
            image.save(
                feed_doc.get("original_id"),
                ContentFile(resp.content),
            )
        except Exception as e:
            logger.error(
                "Could not save image %s for ad %s: %s",
                feed_doc.get("resource_uri"),
                ad.id,
                e,
            )

            if image_instance:
                image_instance.delete()

            return Result.IMAGE_NOT_SAVED
        else:
            logger.info(
                "ad image saved for %s, %s, %s",
                ad.id,
                feed_doc.get("related_id"),
                feed_doc.get("resource_uri"),
            )

    return Result.IMAGE_SAVED
