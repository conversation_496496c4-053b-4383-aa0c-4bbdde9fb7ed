import logging
import time
from urllib.parse import urljoin

from django.conf import settings
from django.core.exceptions import ObjectDoesNotExist
from django.core.management.base import BaseCommand
from oauthsome.oauth_client.requests import oauth_session

from longbeach.classifieds.models import ClassifiedAd
from longbeach.classifieds.utils import (
    FujiFeedDocIterator,
    Result,
    process_feed_doc,
)

logger = logging.getLogger()


class Command(BaseCommand):
    help = "Process classifieds feeds from the specified Fuji source ID and create ads."

    def add_arguments(self, parser):
        parser.add_argument("source", type=int, help="Fuji Source ID")

        parser.add_argument(
            "--no-delete",
            action="store_true",
            dest="nodelete",
            default=False,
            help="Don't delete docs from the feed as they are processed. Docs are deleted by default.",
        )

        parser.add_argument(
            "--doc-limit",
            action="store",
            dest="doc_limit",
            default=settings.FUJI_CLASSIFIEDS_LIMIT,
            help="Limit the number of documents we process each time, default is %s"
            % settings.FUJI_CLASSIFIEDS_LIMIT,
        )

    def handle(self, *args, **options):
        source = options.get("source", settings.FUJI_CLASSIFIEDS_SOURCE_ID)
        delete = not options.get("nodelete")
        doc_limit = (
            int(options.get("doc_limit")) if options.get("doc_limit") else None
        )

        try:
            session = oauth_session("fuji")
        except ObjectDoesNotExist as e:
            logger.error("Could not create oauth_session for Fuji: %s" % e)
            return

        feed_doc_iterator = FujiFeedDocIterator(source=source, session=session)

        processed_docs = 0

        for feed_doc in feed_doc_iterator:
            processed_docs += 1

            try:
                ad_data = process_feed_doc(feed_doc)
            except Exception as e:
                logger.error(
                    "Failed to process feed_doc %s: %s",
                    feed_doc.get("original_id", ""),
                    e,
                )
                continue

            if isinstance(ad_data, dict):
                masthead = ad_data.pop("masthead", None)
                try:
                    ad = ClassifiedAd.objects.create(**ad_data)
                except Exception as e:
                    logger.error(
                        "Failed to create ClassifiedAd for feed_doc %s: %s",
                        feed_doc.get("original_id", ""),
                        e,
                    )
                    continue

                if masthead:
                    ad.mastheads.add(masthead)
                else:
                    logger.info(
                        "Missing masthead for ad %s %s " % (ad.id, ad.title)
                    )

                logger.info("CREATED ad %s %s " % (ad.id, ad.title))

                # it seems on staging sometimes an image is processed before the ad detail is finished being created,
                # ie saved to the db, causing it not to get linked to the ad, so wait a little bit, OPS-432
                time.sleep(0.5)

            url = urljoin(settings.FUJI_HOST, feed_doc["resource_uri"])

            if delete and (
                isinstance(ad_data, dict)
                or ad_data == Result.IMAGE_SAVED
                or ad_data == Result.AD_UPDATED
                or ad_data == Result.NO_AD
                or ad_data == Result.NO_MASTHEAD
            ):
                try:
                    session.delete(url)
                except Exception as e:
                    logger.error("Could not delete feed_doc: %s" % e)
            else:
                logger.info("Not deleting %s" % url)

            if doc_limit and processed_docs >= doc_limit:
                logger.info("Document limit %s reached. Ending." % doc_limit)
                break
