"""URLs for classifieds."""

from django.urls import re_path
from rest_framework.routers import DefaultRouter

from . import views

router = DefaultRouter()

router.register(
    r"api/classifieds",
    views.ClassifiedAdViewSet,
    basename="classifieds",
)
urlpatterns = [
    re_path(
        r"api/classifieds/categories/$",
        views.CategoryViewSet.as_view(),
    ),
    re_path(
        r"api/classifieds/sitemap/$",
        views.SitemapView.as_view(),
    ),
] + router.urls
