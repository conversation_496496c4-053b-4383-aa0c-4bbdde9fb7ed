"""Admin configuration for classifieds."""

from django.contrib import admin

from .models import Category, ClassifiedAd, ClassifiedAdImage


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    """Management of ad categories."""

    list_display = (
        "name",
        "order",
        "parent",
    )
    list_editable = ("order",)
    list_filter = ("parent",)
    search_fields = ("name",)

    def get_queryset(self, request):
        """Optimize the query."""
        return super().get_queryset(request).select_related("parent")


class ClassifiedAdImageInline(admin.StackedInline):
    """Management of ad images."""

    model = ClassifiedAdImage
    extra = 0


@admin.register(ClassifiedAd)
class ClassifiedAdAdmin(admin.ModelAdmin):
    """Basic management of classified ads."""

    inlines = (ClassifiedAdImageInline,)
    search_fields = (
        "reference",
        "title",
        "text",
    )
