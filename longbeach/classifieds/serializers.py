"""API serializers for classified ads."""

from rest_framework import serializers

from .models import Category, ClassifiedAd


class CategorySerializer(serializers.ModelSerializer):
    """Serialize a category."""

    class Meta:
        """Set the fields to display."""

        fields = (
            "id",
            "parent",
            "name",
            "slug",
            "description",
            "show_in_navigation",
            "show_ads",
            "show_in_index_page",
            "show_cluster_ads",
            "show_similar_ads",
            "order",
            "ownlocal_slug",
        )
        model = Category


class ImageUrlField(serializers.RelatedField):
    """Convert a URL field into a URL."""

    def to_representation(self, instance):
        """Get the image URL."""
        try:
            return instance.image.url
        except ValueError:
            return None


class ClassifiedAdSerializer(serializers.ModelSerializer):
    """Serialize an ad."""

    images = ImageUrlField(many=True, read_only=True)
    mastheads = serializers.SlugRelatedField(
        many=True, read_only=True, slug_field="site_id"
    )
    is_for_masthead = serializers.BooleanField(required=False)

    class Meta:
        """Set the fields to display."""

        fields = (
            "id",
            "reference",
            "mastheads",
            "title",
            "text",
            "category_id",
            "category_text",
            "url",
            "publication_date",
            "logo",
            "location",
            "latitude",
            "longitude",
            "images",
            "customer_name",
            "customer_phone",
            "customer_email",
            "customer_town",
            "customer_state",
            "customer_postcode",
            "funeral_home_name",
            "funeral_home_address",
            "funeral_home_city",
            "funeral_home_postcode",
            "funeral_home_state",
            "funeral_venue_name",
            "funeral_venue_address",
            "funeral_venue_city",
            "funeral_venue_state",
            "funeral_venue_postcode",
            "funeral_date",
            "funeral_start_time",
            "quote_text",
            "year_born",
            "year_deceased",
            "date_born",
            "date_deceased",
            "enable_comments",
            "is_for_masthead",
        )
        model = ClassifiedAd
