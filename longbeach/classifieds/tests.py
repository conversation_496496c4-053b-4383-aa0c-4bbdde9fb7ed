import datetime
import json
from unittest.mock import Mock

from django.contrib.auth.models import User
from django.test import TestCase
from freezegun import freeze_time
from rest_framework.test import APIClient

from longbeach.business.models import BusinessMasthead

from .models import Category, ClassifiedAd
from .test_data.responses import fuji_classifieds_responses
from .utils import (
    FujiFeedDocIterator,
    Result,
    process_feed_doc,
)


class FujiFeedDocIteratorTest(TestCase):
    maxDiff = None

    def setUp(self):
        self.masthead = BusinessMasthead.objects.create(site_id=1)
        self.user = User.objects.create_user(
            "<EMAIL>", "<EMAIL>", "<EMAIL>"
        )
        self.category = Category.objects.create(
            classification=938,
            name="Trades & Services",
            order=1,
            show_ads=True,
            show_cluster_ads=True,
            show_in_index_page=True,
            show_in_navigation=True,
        )

        resp0 = Mock()
        resp0.content = json.dumps(fuji_classifieds_responses[0])
        resp0.raise_for_status = lambda: None

        resp1 = Mock()
        resp1.content = json.dumps(fuji_classifieds_responses[1])
        resp1.raise_for_status = lambda: None

        session = Mock()
        session.get = Mock(side_effect=[resp0, resp1])

        self.feed_doc_iterator = FujiFeedDocIterator(151, 3, 0, session)

    def test_FujiFeedDocIterator(self):
        feed_docs = []
        for feed_doc in self.feed_doc_iterator:
            feed_docs.append(feed_doc)

        test_data = (
            fuji_classifieds_responses[0]["objects"]
            + fuji_classifieds_responses[1]["objects"]
        )
        self.assertEqual(feed_docs, test_data)

    def test_process_feed_doc(self):
        category = Category.objects.create(
            classification="T0029",
            name="Bricklayer",
            order=1,
            show_ads=True,
            show_cluster_ads=True,
            show_in_index_page=True,
            show_in_navigation=True,
        )

        test_ad = {
            "text": "ALL types of brick &amp; block work New houses Fences retaining walls etc. Ph: 0416 025 896",
            "title": "",
            "expiration_date": datetime.datetime(2019, 2, 20),
            "masthead": self.masthead,
            "publication_date": datetime.datetime(2019, 2, 13),
            "category": category,
            "category_text": "",
            "latitude": None,
            "longitude": None,
            "location": "",
            "reference": "RM6508263",
            "sort_key": "",
            "url": "",
            "year_born": None,
            "year_deceased": None,
            "customer_email": "",
            "customer_name": "",
            "customer_phone": "",
            "customer_postcode": "",
            "customer_state": "",
            "customer_town": "",
            "enable_comments": True,
            "funeral_date": "",
            "funeral_home_address": "",
            "funeral_home_city": "",
            "funeral_home_name": "",
            "funeral_home_postcode": "",
            "funeral_home_state": "",
            "funeral_start_time": "",
            "funeral_venue_address": "",
            "funeral_venue_city": "",
            "funeral_venue_name": "",
            "funeral_venue_postcode": "",
            "funeral_venue_state": "",
            "quote_text": "",
            "date_born": None,
            "date_deceased": None,
            "original_id": "82776508263",
        }

        ad_data = process_feed_doc(fuji_classifieds_responses[0]["objects"][0])

        self.assertEqual(test_ad, ad_data)

    def _get_fuji_ad(self, **kwargs):
        """Generate test input data from Fuji."""
        return {
            "data": {
                "ad_ref": "RM6519860",
                "body": "Plumber available",
                "categories": "Trades & Services",
                "class_id": str(self.category.classification),
                "created": "2019-02-15T00:00:00",
                "original_id": "82776519860",
                "pubdate": "2019-02-15T00:00:00",
                "publication": "Cowra Guardian",
                "site_id": str(self.masthead.site_id),
                "stopdate": "2019-02-22T00:00:00",
                "subcategory": "",
                "subclass_id": None,
                "type": "story",
                **kwargs,
            },
        }

    def test_category_not_found(self):
        """Test missing categories result in an error."""
        fuji_data = self._get_fuji_ad(class_id="123")
        result = process_feed_doc(fuji_data)
        self.assertEqual(result, Result.NO_CATEGORY)

    def test_service_feature_name(self):
        """Test service feature name is saved."""
        fuji_data = self._get_fuji_ad(categories="Special")
        result = process_feed_doc(fuji_data)
        self.assertTrue(isinstance(result, dict))
        self.assertEqual(result["category_text"], "Special")

    def test_no_service_feature(self):
        """Test ads with no custom service feature name."""
        fuji_data = self._get_fuji_ad()
        result = process_feed_doc(fuji_data)
        self.assertTrue(isinstance(result, dict))
        self.assertEqual(result["category_text"], "")

    def test_first_last_name(self):
        """Test titles are generated from the first and last name."""
        fuji_data = self._get_fuji_ad(firstname="First", lastname="Last")
        result = process_feed_doc(fuji_data)
        self.assertTrue(isinstance(result, dict))
        self.assertEqual(result["title"], "Last, First")

    def test_first_name_only(self):
        """Test titles are generated from the first name."""
        fuji_data = self._get_fuji_ad(firstname="First", lastname="")
        result = process_feed_doc(fuji_data)
        self.assertTrue(isinstance(result, dict))
        self.assertEqual(result["title"], "First")

    def test_last_name_only(self):
        """Test titles are generated from the last name."""
        fuji_data = self._get_fuji_ad(firstname="", lastname="Last")
        result = process_feed_doc(fuji_data)
        self.assertTrue(isinstance(result, dict))
        self.assertEqual(result["title"], "Last")


class ClassifiedsAPITestCase(TestCase):
    maxDiff = None

    @freeze_time("2024-09-23")
    def test_get(self):
        masthead = BusinessMasthead.objects.create(site_id=1)
        category = Category.objects.create(
            classification=938,
            name="Trades & Services",
            order=1,
            show_ads=True,
            show_cluster_ads=True,
            show_in_index_page=True,
            show_in_navigation=True,
        )

        ad00 = ClassifiedAd.objects.create(
            publication_date=datetime.datetime.today(),
            expiration_date=datetime.datetime.today()
            + datetime.timedelta(days=14),
            title="ad 00:ad 00:ad 00",
            text="this is ad 00",
            category=category,
        )
        ad00.mastheads.add(masthead)

        ad01 = ClassifiedAd.objects.create(
            publication_date=datetime.datetime.today(),
            expiration_date=datetime.datetime.today()
            + datetime.timedelta(days=14),
            title="ad 01:ad 01:ad 01",
            text="this is ad 01",
            category=category,
        )
        ad01.mastheads.add(masthead)

        ad02 = ClassifiedAd.objects.create(
            publication_date=datetime.datetime.today(),
            expiration_date=datetime.datetime.today()
            + datetime.timedelta(days=14),
            title="ad 02:ad 02:ad 02",
            text="this is ad 02",
            category=category,
        )
        ad02.mastheads.add(masthead)

        client = APIClient()
        data = json.loads(
            client.get(
                "/api/classifieds/", {"masthead_id": masthead.site_id}
            ).content
        )
        self.assertEqual(
            data,
            {
                "count": 3,
                "next": None,
                "previous": None,
                "results": [
                    {
                        "id": ad00.pk,
                        "reference": "",
                        "mastheads": [1],
                        "title": "ad 00:ad 00:ad 00",
                        "text": "this is ad 00",
                        "category_id": category.pk,
                        "category_text": "",
                        "url": "",
                        "publication_date": "2024-09-23",
                        "logo": None,
                        "location": "",
                        "latitude": None,
                        "longitude": None,
                        "images": [],
                        "customer_name": "",
                        "customer_phone": "",
                        "customer_email": "",
                        "customer_town": "",
                        "customer_state": "",
                        "customer_postcode": "",
                        "funeral_date": "",
                        "funeral_home_address": "",
                        "funeral_home_city": "",
                        "funeral_home_name": "",
                        "funeral_home_postcode": "",
                        "funeral_home_state": "",
                        "funeral_start_time": "",
                        "funeral_venue_address": "",
                        "funeral_venue_city": "",
                        "funeral_venue_name": "",
                        "funeral_venue_postcode": "",
                        "funeral_venue_state": "",
                        "quote_text": "",
                        "year_born": None,
                        "year_deceased": None,
                        "date_born": None,
                        "date_deceased": None,
                        "enable_comments": True,
                    },
                    {
                        "id": ad01.pk,
                        "reference": "",
                        "mastheads": [1],
                        "title": "ad 01:ad 01:ad 01",
                        "text": "this is ad 01",
                        "category_id": category.pk,
                        "category_text": "",
                        "url": "",
                        "publication_date": "2024-09-23",
                        "logo": None,
                        "location": "",
                        "latitude": None,
                        "longitude": None,
                        "images": [],
                        "customer_name": "",
                        "customer_phone": "",
                        "customer_email": "",
                        "customer_town": "",
                        "customer_state": "",
                        "customer_postcode": "",
                        "funeral_date": "",
                        "funeral_home_address": "",
                        "funeral_home_city": "",
                        "funeral_home_name": "",
                        "funeral_home_postcode": "",
                        "funeral_home_state": "",
                        "funeral_start_time": "",
                        "funeral_venue_address": "",
                        "funeral_venue_city": "",
                        "funeral_venue_name": "",
                        "funeral_venue_postcode": "",
                        "funeral_venue_state": "",
                        "quote_text": "",
                        "year_born": None,
                        "year_deceased": None,
                        "date_born": None,
                        "date_deceased": None,
                        "enable_comments": True,
                    },
                    {
                        "id": ad02.pk,
                        "reference": "",
                        "mastheads": [1],
                        "title": "ad 02:ad 02:ad 02",
                        "text": "this is ad 02",
                        "category_id": category.pk,
                        "category_text": "",
                        "url": "",
                        "publication_date": "2024-09-23",
                        "logo": None,
                        "location": "",
                        "latitude": None,
                        "longitude": None,
                        "images": [],
                        "customer_name": "",
                        "customer_phone": "",
                        "customer_email": "",
                        "customer_town": "",
                        "customer_state": "",
                        "customer_postcode": "",
                        "funeral_date": "",
                        "funeral_home_address": "",
                        "funeral_home_city": "",
                        "funeral_home_name": "",
                        "funeral_home_postcode": "",
                        "funeral_home_state": "",
                        "funeral_start_time": "",
                        "funeral_venue_address": "",
                        "funeral_venue_city": "",
                        "funeral_venue_name": "",
                        "funeral_venue_postcode": "",
                        "funeral_venue_state": "",
                        "quote_text": "",
                        "year_born": None,
                        "year_deceased": None,
                        "date_born": None,
                        "date_deceased": None,
                        "enable_comments": True,
                    },
                ],
            },
        )

    def test_get_categories(self):
        categories = [
            Category.objects.create(
                name="Trades & Services",
                order=1,
                show_ads=True,
                show_cluster_ads=True,
                show_in_index_page=True,
                show_in_navigation=True,
            )
        ]
        categories.append(
            Category.objects.create(
                classification=100,
                name="Trades",
                order=1,
                parent=categories[-1],
                show_ads=True,
                show_cluster_ads=True,
                show_in_index_page=True,
                show_in_navigation=True,
            )
        )
        categories.append(
            Category.objects.create(
                classification=110,
                name="Real Estate",
                order=2,
                show_ads=True,
                show_cluster_ads=True,
                show_in_index_page=True,
                show_in_navigation=True,
            )
        )
        categories.append(
            Category.objects.create(
                classification=120,
                name="Notices",
                order=3,
                show_ads=True,
                show_cluster_ads=True,
                show_in_index_page=True,
                show_in_navigation=True,
            )
        )

        client = APIClient()
        data = json.loads(client.get("/api/classifieds/categories/").content)
        self.assertEqual(
            data,
            [
                {
                    "id": categories[0].pk,
                    "parent": None,
                    "name": "Trades & Services",
                    "slug": "",
                    "description": "",
                    "show_in_navigation": True,
                    "show_ads": True,
                    "show_in_index_page": True,
                    "show_cluster_ads": True,
                    "show_similar_ads": True,
                    "order": 1,
                    "ownlocal_slug": "",
                },
                {
                    "id": categories[1].pk,
                    "parent": categories[0].pk,
                    "name": "Trades",
                    "slug": "",
                    "description": "",
                    "show_in_navigation": True,
                    "show_ads": True,
                    "show_in_index_page": True,
                    "show_cluster_ads": True,
                    "show_similar_ads": True,
                    "order": 1,
                    "ownlocal_slug": "",
                },
                {
                    "id": categories[2].pk,
                    "parent": None,
                    "name": "Real Estate",
                    "slug": "",
                    "description": "",
                    "show_in_navigation": True,
                    "show_ads": True,
                    "show_in_index_page": True,
                    "show_cluster_ads": True,
                    "show_similar_ads": True,
                    "order": 2,
                    "ownlocal_slug": "",
                },
                {
                    "id": categories[3].pk,
                    "parent": None,
                    "name": "Notices",
                    "slug": "",
                    "description": "",
                    "show_in_navigation": True,
                    "show_ads": True,
                    "show_in_index_page": True,
                    "show_cluster_ads": True,
                    "show_similar_ads": True,
                    "order": 3,
                    "ownlocal_slug": "",
                },
            ],
        )
