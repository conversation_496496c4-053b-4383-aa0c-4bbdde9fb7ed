{"name": "tiptap", "version": "1.0.0", "description": "Custom tiptap editor", "engines": {"node": "^22.6.0", "npm": "^10.8.2"}, "private": true, "type": "module", "scripts": {"build": "NODE_ENV=production rollup -c", "test": "echo \"Error: no test specified\" && exit 1"}, "license": "UNLICENSED", "dependencies": {"@tiptap/core": "^2.11.0", "@tiptap/extension-bold": "^2.11.0", "@tiptap/extension-bullet-list": "^2.11.0", "@tiptap/extension-character-count": "^2.11.0", "@tiptap/extension-document": "^2.11.0", "@tiptap/extension-history": "^2.11.0", "@tiptap/extension-italic": "^2.11.0", "@tiptap/extension-link": "^2.11.0", "@tiptap/extension-list-item": "^2.11.0", "@tiptap/extension-paragraph": "^2.11.0", "@tiptap/extension-text": "^2.11.0"}, "devDependencies": {"@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-terser": "^0.4.4", "rollup": "^4.30.0"}}