import os

from django import template
from django.conf import settings

register = template.Library()


@register.simple_tag(takes_context=True)
def transform_host(context):
    transform_host = ""
    akamai_transform_url = getattr(settings, "AKAMAI_TRANSFORM_URL", None)
    if akamai_transform_url:
        transform_host = akamai_transform_url
    else:
        url = getattr(settings, "VALENCIA_TRANSFORM_HOST", "")
        if url:
            _, rest = url.rstrip("/").split("://")
            transform_host = "https://%s/" % rest
    return transform_host


@register.simple_tag(takes_context=True)
def transform_url(context, *args, **kwargs):
    """
    Generates an appropriate transform URL based on valencia
    bucket/filename provided. Takes width and height params.

    NOTE: Currently only supports generating transform resize urls.
    """
    image = kwargs.get("image")
    width = kwargs.get("width")
    height = kwargs.get("height")
    if image and width and height:
        filename, file_extension = os.path.splitext(str(image))
        if not file_extension:
            file_extension = ".jpg"
        transform_type = "_fcrop"
        size = (
            "/w"
            + str(width)
            + "_"
            + "h"
            + str(height)
            + transform_type
            + file_extension.lower()
        )
        return (
            transform_host(context)
            + "transform/v1/resize/frm"
            + str(image)
            + size
        )
    return ""
