from django.contrib.auth.decorators import login_required
from django.urls import re_path

from longbeach.localads.views import (
    SuzukaSites,
    selfservice_free_signup,
    sponsored_jobs_api,
    website_content_esov_api,
)

urlpatterns = [
    re_path(r"api/sites/$", login_required(SuzukaSites), name="suzuka-sites"),
    re_path(r"api/sponsored_jobs/$", sponsored_jobs_api),
    re_path(r"api/website_content_esov/$", website_content_esov_api),
    re_path(r"api/selfservice_free_signup/$", selfservice_free_signup),
]
