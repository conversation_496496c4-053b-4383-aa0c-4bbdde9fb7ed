from operator import itemgetter

from django import forms
from django.conf import settings
from django.utils.datastructures import MultiValueDict

from longbeach.business.manage.utils import get_sites


class ArrayFieldSiteSelectMultiple(forms.SelectMultiple):
    """
    This is a Form Widget for suzuka sites for use with a Postgres ArrayField. It implements
    a multi-select interface that can be given a set of `choices` or will retrieve the choices
    if none provided.
    """

    def __init__(self, *args, **kwargs):
        self.delimiter = kwargs.pop("delimiter", ",")
        super().__init__(*args, **kwargs)

    def value_from_datadict(self, data, files, name):
        if isinstance(data, MultiValueDict):
            return self.delimiter.join(data.getlist(name))
        return data.get(name, None)

    def format_value(self, value):
        return value.split(",")

    def sites(self):
        """
        Return a list of sites from Suzuka.

        """
        if getattr(settings, "DISABLE_SUZUKA_SITES_FILTER", False):
            # Return an empty list of sites.
            return []

        # n.b. only visible sites are returned
        sites = get_sites()
        key = itemgetter(1)

        site_names = [(pk, site["name"]) for pk, site in list(sites.items())]
        site_names.sort(key=key)

        return site_names

    def sites_domain(self):
        """
        Get a list of site id's mapped to their domain.
        """
        if getattr(settings, "DISABLE_SUZUKA_SITES_FILTER", False):
            # Return an empty list of sites.
            return []

        # n.b. only visible sites are returned
        key = itemgetter(1)
        sites = get_sites()
        site_domains = [
            (pk, site["domain"]) for pk, site in list(sites.items())
        ]
        site_domains.sort(key=key)

        return site_domains
