import json
from unittest import mock

from django.contrib.auth.models import User
from django.core.cache import cache
from django.test import TestCase
from valencia_storage import ValenciaStorage

from longbeach.business.models import Business, BusinessCategory


def fakeSave(self, name, content):
    return "faked_file_%s" % name


class BusinessListingESOVAPITest(TestCase):
    @mock.patch.object(ValenciaStorage, "_save", fakeSave)
    def setUp(self):
        self.api_url = "/api/website_content_esov/"
        self.site_id = 1
        self.test_org_1 = 1
        self.test_org_2 = 2
        self.username, self.password = "test", "test"
        self.user = User.objects.create_user(self.username, "", self.password)

        self.cat1 = BusinessCategory.objects.create(name="cat 1")
        self.cat2 = BusinessCategory.objects.create(name="cat 2")
        self.cat3 = BusinessCategory.objects.create(name="cat 3")
        biz_args = {
            "created_by": self.user,
            "updated_by": self.user,
            "organization": self.test_org_1,
            "mastheads": [self.site_id],
            "account_type": 2,
        }
        cache.clear()
        self.biz1 = Business.objects.create(
            name="biz 1", category=self.cat1, **biz_args
        )
        self.biz2 = Business.objects.create(
            name="biz 2", category=self.cat2, **biz_args
        )

    def tearDown(self):
        super().tearDown()

        BusinessCategory.objects.all().delete()
        User.objects.all().delete()
        Business.objects.all().delete()

    def test_esov_page(self):
        response = self.client.get(
            self.api_url + f"?limit=2&site_id={self.site_id}",
            format="json",
        )
        self.assertEqual(
            response.status_code,
            200,
            msg=f"Response returned incorrect error code: {response.status_code} data: {response.__dict__}",
        )
        data = json.loads(response.content)
        self.assertEqual(len(data), 2)

    def test_esov_page_limits_correctly(self):
        biz_args = {
            "created_by": self.user,
            "updated_by": self.user,
            "organization": self.test_org_1,
            "mastheads": [self.site_id],
            "account_type": 2,
        }
        self.biz3 = Business.objects.create(
            name="biz 3", category=self.cat3, **biz_args
        )

        response = self.client.get(
            self.api_url + f"?limit=2&site_id={self.site_id}",
            format="json",
        )
        self.assertEqual(
            response.status_code,
            200,
            msg=f"Response returned incorrect error code: {response.status_code} data: {response.__dict__}",
        )
        data = json.loads(response.content)
        self.assertEqual(len(data), 2)
