# -*- coding: utf-8 -*-


from django.db import migrations, models
import array_field_select.fields
import recurrence.fields


class Migration(migrations.Migration):

    dependencies = [
        ('localads', '0002_localad_booking_state'),
    ]

    operations = [
        migrations.AddField(
            model_name='localad',
            name='page_targets',
            field=array_field_select.fields.ArrayField(default=['HOMEPAGE'], base_field=models.CharField(max_length=50, choices=[('HOMEPAGE', 'Homepage'), ('STORY', 'Story')]), size=None),
        ),
        migrations.AddField(
            model_name='localad',
            name='recurrences',
            field=recurrence.fields.RecurrenceField(default='', verbose_name='Dates'),
        ),
        migrations.AlterField(
            model_name='localad',
            name='display_end',
            field=models.DateTimeField(db_index=True, null=True, blank=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='localad',
            name='display_start',
            field=models.DateTimeField(db_index=True),
        ),
    ]
