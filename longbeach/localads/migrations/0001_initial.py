# -*- coding: utf-8 -*-


from django.db import migrations, models
import django.contrib.postgres.fields
from django.conf import settings
import valencia_storage.storage
import shared_orgs_client.utils


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='LocalAd',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('organization', shared_orgs_client.utils.OrgField(max_length=255, db_index=True)),
                ('title', models.CharField(help_text='Max Characters allowed is 24.', max_length=24)),
                ('copy', models.TextField(help_text='Max Characters allowed is 62.', max_length=62, verbose_name='Body Copy')),
                ('image', models.ImageField(storage=valencia_storage.storage.ValenciaStorage(), upload_to='ad_images')),
                ('redirect_url', models.URLField(blank=True)),
                ('sites', django.contrib.postgres.fields.ArrayField(base_field=models.IntegerField(), size=None)),
                ('is_active', models.BooleanField(default=True)),
                ('display_start', models.DateField()),
                ('display_end', models.DateField()),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('business', models.ForeignKey(on_delete=models.deletion.CASCADE, related_name='local_ads', to='business.Business')),
                ('created_by', models.ForeignKey(on_delete=models.deletion.CASCADE, related_name='local_ad', editable=False, to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(on_delete=models.deletion.CASCADE, related_name='local_ad_updated', editable=False, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
