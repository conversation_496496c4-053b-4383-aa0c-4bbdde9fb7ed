import json
import random
import traceback

from django.conf import settings
from django.contrib.auth.models import User
from django.core.cache import cache
from django.core.mail import send_mail
from django.http import HttpResponse
from django.template.loader import get_template
from django.views.decorators.cache import cache_page
from oauthsome.oauth_client.requests import oauth_session
from requests import RequestException
from rest_framework.decorators import (
    api_view,
    authentication_classes,
    permission_classes,
)
from rest_framework.response import Response

from longbeach.business.models import (
    Business,
    BusinessCategory,
    BusinessMasthead,
)
from longbeach.localads.models import SponsoredJob


@api_view(["POST"])
@authentication_classes([])
@permission_classes([])
def selfservice_free_signup(request):
    try:
        req = json.loads(request.body)
        # logic to create an unapproved business
        longbot = User.objects.get(username="longbot")
        category = BusinessCategory.objects.get(id=req["category_id"])
        if Business.objects.filter(name=req["business_name"]):
            return Response(
                {"Error": "Business name already exists", "HTTP_CODE": 400}
            )
        masthead = BusinessMasthead.objects.get(site_id=req["site_id"])
        kwargs = {
            "created_by": longbot,
            "updated_by": longbot,
            "account_type": Business.ACCOUNT_UNAPPROVED,
            "organization": 211,  # Default organisation id
            "address": req["business_address"],
            "category": category,
            "email": req["email"],
            "telephone": req["business_number"],
            "name": req["business_name"],
            "contact_name": req["contact_name"],
        }
        b = Business.objects.create(**kwargs)
        b.listing_mastheads.add(masthead)

        # logic to send email
        html_message = get_template(
            "emails/selfservice_free_thank_you.html"
        ).render({"ctxt": req})
        text_message = get_template(
            "emails/selfservice_free_thank_you_text.html"
        ).render({"ctxt": req})

        mail_from = settings.NOREPLY_NEWSNOW_FROM_EMAIL
        mail_to = (req["email"],)
        send_mail(
            "Thank you",
            text_message,
            mail_from,
            mail_to,
            fail_silently=True,
            html_message=html_message,
        )
        return Response(
            {
                "Success": "Business got created and welcome email sent",
                "HTTP_CODE": 200,
            }
        )
    except BusinessCategory.DoesNotExist:
        return Response({"Error": "Invalid Category Id", "HTTP_CODE": 400})
    except BusinessMasthead.DoesNotExist:
        return Response({"Error": "Invalid Site Id", "HTTP_CODE": 400})
    except AttributeError:
        return Response(
            {
                "Error": "Invalid Address, Unable to verify against Google Maps",
                "HTTP_CODE": 1000,
            }
        )
    except Exception:
        import traceback as tb

        return Response({"Exception": tb.format_exc(), "HTTP_CODE": 409})


def get_bump_counter(key, delta, block_size):
    """
    Atomic get and increment of counter in memcache.
    Ref: https://www.programcreek.com/python/example/91443/django.core.cache.cache.incr
    """
    try:
        # Will return atomically inc'd value or throw if
        # key not existing
        counter = cache.incr(key, delta)
    except ValueError:
        # To be fair'ish, init to random position in block
        counter = random.randint(0, block_size - 1)
        # Only add counter key if not already set
        # If set in between here and incr call,
        cache.add(key, counter, None)
    return counter


@api_view(["GET"])
def website_content_esov_api(request):
    """
    :param HTTP request:
    :return:
    JSON of Business objects with limit=6 with the below considerations
        1). Impact business accounts (account_type=2)
        2). Must have the current masthead (site_id) listed as a website content ad option
        3). Goes after ESOV Tracker and gives preference for the ones which have the oldest 'last_displayed',
            In that way that every ad will have cyclical priority.
        4). Updates the ESOV Tracker on the field 'last_displayed' with the current timestamp.

    """
    try:
        limit = int(request.GET.get("limit"))
        site_id = int(request.GET.get("site_id"))

        site_biz_list_key = "site_%d_biz_list" % site_id
        site_biz_cursor_key = "site_%d_biz_cursor" % site_id

        site_biz_list = cache.get(site_biz_list_key, None)

        if site_biz_list is None:
            # Get id's of all site businesses
            site_biz_list = list(
                Business.objects.filter(
                    account_type=2, mastheads__contains=[site_id]
                ).values_list("id", flat=True)
            )
            cache.set(site_biz_list_key, site_biz_list)

        if not site_biz_list:
            return Response([], status=200)
        if limit >= len(site_biz_list):
            limit = 0
        # Get our business list cursor in memcache (atomic)
        biz_list_len = len(site_biz_list)
        site_biz_cursor = (
            get_bump_counter(site_biz_cursor_key, limit, biz_list_len) - limit
        )

        # Extract block of biz ids based on cursor
        block_start = site_biz_cursor % biz_list_len
        block_end = (site_biz_cursor + limit) % biz_list_len
        if limit == 0:
            target_biz_ids = site_biz_list
        elif block_start > block_end:
            target_biz_ids = (
                site_biz_list[block_start:] + site_biz_list[:block_end]
            )
        else:
            target_biz_ids = site_biz_list[block_start:block_end]

        # Get target businesses from DB
        target_businesses = Business.objects.filter(
            id__in=target_biz_ids
        ).select_related("category")
        res = list(map(Business.esov_response, target_businesses))
        return Response(res, status=200)
    except TypeError:
        return Response(
            {"_error": "required parameters are missing"}, status=400
        )


@cache_page(60 * 60 * 8)
@api_view(["GET"])
def sponsored_jobs_api(request):
    try:
        jobs = SponsoredJob.objects.all()

        location = request.GET.get("location")
        if location:
            jobs = jobs.filter(location=location)

        ids = jobs.values_list("pk", flat=True)

        if len(ids) < 2:
            ids = SponsoredJob.objects.all().values_list("pk", flat=True)

        ids = random.sample(ids, 2)  # Randomly choose 2 results
        res = list(
            map(SponsoredJob.dictify, SponsoredJob.objects.filter(pk__in=ids))
        )
        return Response(res, status=200)
    except Exception:
        return Response(
            {"_exception": traceback.format_exc().splitlines()[-1]}, status=500
        )


def SuzukaSites(request):
    """
    API view to get a list of suzuka sites.
    """

    def handle_error():
        return HttpResponse(
            "API Request Failed", status=503, content_type="application/json"
        )

    try:
        resp = oauth_session("suzuka").get(
            "%s/site/?limit=0&fields=name,id,visible"
            % settings.SUZUKA_API_HOST
        )
    except (TypeError, AttributeError):
        handle_error()

    try:
        resp.raise_for_status()
    except RequestException:
        handle_error()

    try:
        json_resp = resp.json
        if callable(json_resp):
            json_resp = json_resp()
    except (TypeError, AttributeError):
        handle_error()

    return HttpResponse(
        json.dumps(json_resp), status=200, content_type="application/json"
    )
