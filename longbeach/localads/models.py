from random import randint

from django.db import models
from django.db.models import Count


class Sponsored<PERSON><PERSON><PERSON><PERSON><PERSON>(models.Manager):
    def random(self):  # https://stackoverflow.com/a/2118712
        count = self.aggregate(ids=Count("id"))["ids"]
        random_index = randint(0, count - 1)
        return self.all()[random_index]


class SponsoredJob(models.Model):
    title = models.CharField(null=False, max_length=500)
    description = models.TextField()
    url = models.URLField(max_length=300)
    image = models.URLField()
    location = models.TextField(null=True, max_length=100)

    objects = SponsoredJobManager()

    def dictify(self):
        return {
            "title": self.title,
            "description": self.description,
            "url": self.url,
            "location": self.location,
            "image": self.image,
        }
