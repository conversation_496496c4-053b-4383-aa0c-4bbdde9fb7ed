var Tiptap=function(t){"use strict";function e(t){this.content=t}function n(t,e,r){for(let o=0;;o++){if(o==t.childCount||o==e.childCount)return t.childCount==e.childCount?null:r;let i=t.child(o),s=e.child(o);if(i!=s){if(!i.sameMarkup(s))return r;if(i.isText&&i.text!=s.text){for(let t=0;i.text[t]==s.text[t];t++)r++;return r}if(i.content.size||s.content.size){let t=n(i.content,s.content,r+1);if(null!=t)return t}r+=i.nodeSize}else r+=i.nodeSize}}function r(t,e,n,o){for(let i=t.childCount,s=e.childCount;;){if(0==i||0==s)return i==s?null:{a:n,b:o};let l=t.child(--i),a=e.child(--s),c=l.nodeSize;if(l!=a){if(!l.sameMarkup(a))return{a:n,b:o};if(l.isText&&l.text!=a.text){let t=0,e=Math.min(l.text.length,a.text.length);for(;t<e&&l.text[l.text.length-t-1]==a.text[a.text.length-t-1];)t++,n--,o--;return{a:n,b:o}}if(l.content.size||a.content.size){let t=r(l.content,a.content,n-1,o-1);if(t)return t}n-=c,o-=c}else n-=c,o-=c}}e.prototype={constructor:e,find:function(t){for(var e=0;e<this.content.length;e+=2)if(this.content[e]===t)return e;return-1},get:function(t){var e=this.find(t);return-1==e?void 0:this.content[e+1]},update:function(t,n,r){var o=r&&r!=t?this.remove(r):this,i=o.find(t),s=o.content.slice();return-1==i?s.push(r||t,n):(s[i+1]=n,r&&(s[i]=r)),new e(s)},remove:function(t){var n=this.find(t);if(-1==n)return this;var r=this.content.slice();return r.splice(n,2),new e(r)},addToStart:function(t,n){return new e([t,n].concat(this.remove(t).content))},addToEnd:function(t,n){var r=this.remove(t).content.slice();return r.push(t,n),new e(r)},addBefore:function(t,n,r){var o=this.remove(n),i=o.content.slice(),s=o.find(t);return i.splice(-1==s?i.length:s,0,n,r),new e(i)},forEach:function(t){for(var e=0;e<this.content.length;e+=2)t(this.content[e],this.content[e+1])},prepend:function(t){return(t=e.from(t)).size?new e(t.content.concat(this.subtract(t).content)):this},append:function(t){return(t=e.from(t)).size?new e(this.subtract(t).content.concat(t.content)):this},subtract:function(t){var n=this;t=e.from(t);for(var r=0;r<t.content.length;r+=2)n=n.remove(t.content[r]);return n},toObject:function(){var t={};return this.forEach((function(e,n){t[e]=n})),t},get size(){return this.content.length>>1}},e.from=function(t){if(t instanceof e)return t;var n=[];if(t)for(var r in t)n.push(r,t[r]);return new e(n)};class o{constructor(t,e){if(this.content=t,this.size=e||0,null==e)for(let e=0;e<t.length;e++)this.size+=t[e].nodeSize}nodesBetween(t,e,n,r=0,o){for(let i=0,s=0;s<e;i++){let l=this.content[i],a=s+l.nodeSize;if(a>t&&!1!==n(l,r+s,o||null,i)&&l.content.size){let o=s+1;l.nodesBetween(Math.max(0,t-o),Math.min(l.content.size,e-o),n,r+o)}s=a}}descendants(t){this.nodesBetween(0,this.size,t)}textBetween(t,e,n,r){let o="",i=!0;return this.nodesBetween(t,e,((s,l)=>{let a=s.isText?s.text.slice(Math.max(t,l)-l,e-l):s.isLeaf?r?"function"==typeof r?r(s):r:s.type.spec.leafText?s.type.spec.leafText(s):"":"";s.isBlock&&(s.isLeaf&&a||s.isTextblock)&&n&&(i?i=!1:o+=n),o+=a}),0),o}append(t){if(!t.size)return this;if(!this.size)return t;let e=this.lastChild,n=t.firstChild,r=this.content.slice(),i=0;for(e.isText&&e.sameMarkup(n)&&(r[r.length-1]=e.withText(e.text+n.text),i=1);i<t.content.length;i++)r.push(t.content[i]);return new o(r,this.size+t.size)}cut(t,e=this.size){if(0==t&&e==this.size)return this;let n=[],r=0;if(e>t)for(let o=0,i=0;i<e;o++){let s=this.content[o],l=i+s.nodeSize;l>t&&((i<t||l>e)&&(s=s.isText?s.cut(Math.max(0,t-i),Math.min(s.text.length,e-i)):s.cut(Math.max(0,t-i-1),Math.min(s.content.size,e-i-1))),n.push(s),r+=s.nodeSize),i=l}return new o(n,r)}cutByIndex(t,e){return t==e?o.empty:0==t&&e==this.content.length?this:new o(this.content.slice(t,e))}replaceChild(t,e){let n=this.content[t];if(n==e)return this;let r=this.content.slice(),i=this.size+e.nodeSize-n.nodeSize;return r[t]=e,new o(r,i)}addToStart(t){return new o([t].concat(this.content),this.size+t.nodeSize)}addToEnd(t){return new o(this.content.concat(t),this.size+t.nodeSize)}eq(t){if(this.content.length!=t.content.length)return!1;for(let e=0;e<this.content.length;e++)if(!this.content[e].eq(t.content[e]))return!1;return!0}get firstChild(){return this.content.length?this.content[0]:null}get lastChild(){return this.content.length?this.content[this.content.length-1]:null}get childCount(){return this.content.length}child(t){let e=this.content[t];if(!e)throw new RangeError("Index "+t+" out of range for "+this);return e}maybeChild(t){return this.content[t]||null}forEach(t){for(let e=0,n=0;e<this.content.length;e++){let r=this.content[e];t(r,n,e),n+=r.nodeSize}}findDiffStart(t,e=0){return n(this,t,e)}findDiffEnd(t,e=this.size,n=t.size){return r(this,t,e,n)}findIndex(t,e=-1){if(0==t)return s(0,t);if(t==this.size)return s(this.content.length,t);if(t>this.size||t<0)throw new RangeError(`Position ${t} outside of fragment (${this})`);for(let n=0,r=0;;n++){let o=r+this.child(n).nodeSize;if(o>=t)return o==t||e>0?s(n+1,o):s(n,r);r=o}}toString(){return"<"+this.toStringInner()+">"}toStringInner(){return this.content.join(", ")}toJSON(){return this.content.length?this.content.map((t=>t.toJSON())):null}static fromJSON(t,e){if(!e)return o.empty;if(!Array.isArray(e))throw new RangeError("Invalid input for Fragment.fromJSON");return new o(e.map(t.nodeFromJSON))}static fromArray(t){if(!t.length)return o.empty;let e,n=0;for(let r=0;r<t.length;r++){let o=t[r];n+=o.nodeSize,r&&o.isText&&t[r-1].sameMarkup(o)?(e||(e=t.slice(0,r)),e[e.length-1]=o.withText(e[e.length-1].text+o.text)):e&&e.push(o)}return new o(e||t,n)}static from(t){if(!t)return o.empty;if(t instanceof o)return t;if(Array.isArray(t))return this.fromArray(t);if(t.attrs)return new o([t],t.nodeSize);throw new RangeError("Can not convert "+t+" to a Fragment"+(t.nodesBetween?" (looks like multiple versions of prosemirror-model were loaded)":""))}}o.empty=new o([],0);const i={index:0,offset:0};function s(t,e){return i.index=t,i.offset=e,i}function l(t,e){if(t===e)return!0;if(!t||"object"!=typeof t||!e||"object"!=typeof e)return!1;let n=Array.isArray(t);if(Array.isArray(e)!=n)return!1;if(n){if(t.length!=e.length)return!1;for(let n=0;n<t.length;n++)if(!l(t[n],e[n]))return!1}else{for(let n in t)if(!(n in e)||!l(t[n],e[n]))return!1;for(let n in e)if(!(n in t))return!1}return!0}let a=class t{constructor(t,e){this.type=t,this.attrs=e}addToSet(t){let e,n=!1;for(let r=0;r<t.length;r++){let o=t[r];if(this.eq(o))return t;if(this.type.excludes(o.type))e||(e=t.slice(0,r));else{if(o.type.excludes(this.type))return t;!n&&o.type.rank>this.type.rank&&(e||(e=t.slice(0,r)),e.push(this),n=!0),e&&e.push(o)}}return e||(e=t.slice()),n||e.push(this),e}removeFromSet(t){for(let e=0;e<t.length;e++)if(this.eq(t[e]))return t.slice(0,e).concat(t.slice(e+1));return t}isInSet(t){for(let e=0;e<t.length;e++)if(this.eq(t[e]))return!0;return!1}eq(t){return this==t||this.type==t.type&&l(this.attrs,t.attrs)}toJSON(){let t={type:this.type.name};for(let e in this.attrs){t.attrs=this.attrs;break}return t}static fromJSON(t,e){if(!e)throw new RangeError("Invalid input for Mark.fromJSON");let n=t.marks[e.type];if(!n)throw new RangeError(`There is no mark type ${e.type} in this schema`);let r=n.create(e.attrs);return n.checkAttrs(r.attrs),r}static sameSet(t,e){if(t==e)return!0;if(t.length!=e.length)return!1;for(let n=0;n<t.length;n++)if(!t[n].eq(e[n]))return!1;return!0}static setFrom(e){if(!e||Array.isArray(e)&&0==e.length)return t.none;if(e instanceof t)return[e];let n=e.slice();return n.sort(((t,e)=>t.type.rank-e.type.rank)),n}};a.none=[];class c extends Error{}class h{constructor(t,e,n){this.content=t,this.openStart=e,this.openEnd=n}get size(){return this.content.size-this.openStart-this.openEnd}insertAt(t,e){let n=p(this.content,t+this.openStart,e);return n&&new h(n,this.openStart,this.openEnd)}removeBetween(t,e){return new h(d(this.content,t+this.openStart,e+this.openStart),this.openStart,this.openEnd)}eq(t){return this.content.eq(t.content)&&this.openStart==t.openStart&&this.openEnd==t.openEnd}toString(){return this.content+"("+this.openStart+","+this.openEnd+")"}toJSON(){if(!this.content.size)return null;let t={content:this.content.toJSON()};return this.openStart>0&&(t.openStart=this.openStart),this.openEnd>0&&(t.openEnd=this.openEnd),t}static fromJSON(t,e){if(!e)return h.empty;let n=e.openStart||0,r=e.openEnd||0;if("number"!=typeof n||"number"!=typeof r)throw new RangeError("Invalid input for Slice.fromJSON");return new h(o.fromJSON(t,e.content),n,r)}static maxOpen(t,e=!0){let n=0,r=0;for(let r=t.firstChild;r&&!r.isLeaf&&(e||!r.type.spec.isolating);r=r.firstChild)n++;for(let n=t.lastChild;n&&!n.isLeaf&&(e||!n.type.spec.isolating);n=n.lastChild)r++;return new h(t,n,r)}}function d(t,e,n){let{index:r,offset:o}=t.findIndex(e),i=t.maybeChild(r),{index:s,offset:l}=t.findIndex(n);if(o==e||i.isText){if(l!=n&&!t.child(s).isText)throw new RangeError("Removing non-flat range");return t.cut(0,e).append(t.cut(n))}if(r!=s)throw new RangeError("Removing non-flat range");return t.replaceChild(r,i.copy(d(i.content,e-o-1,n-o-1)))}function p(t,e,n,r){let{index:o,offset:i}=t.findIndex(e),s=t.maybeChild(o);if(i==e||s.isText)return t.cut(0,e).append(n).append(t.cut(e));let l=p(s.content,e-i-1,n);return l&&t.replaceChild(o,s.copy(l))}function u(t,e,n){if(n.openStart>t.depth)throw new c("Inserted content deeper than insertion position");if(t.depth-n.openStart!=e.depth-n.openEnd)throw new c("Inconsistent open depths");return f(t,e,n,0)}function f(t,e,n,r){let i=t.index(r),s=t.node(r);if(i==e.index(r)&&r<t.depth-n.openStart){let o=f(t,e,n,r+1);return s.copy(s.content.replaceChild(i,o))}if(n.content.size){if(n.openStart||n.openEnd||t.depth!=r||e.depth!=r){let{start:i,end:l}=function(t,e){let n=e.depth-t.openStart,r=e.node(n).copy(t.content);for(let t=n-1;t>=0;t--)r=e.node(t).copy(o.from(r));return{start:r.resolveNoCache(t.openStart+n),end:r.resolveNoCache(r.content.size-t.openEnd-n)}}(n,t);return v(s,k(t,i,l,e,r))}{let r=t.parent,o=r.content;return v(r,o.cut(0,t.parentOffset).append(n.content).append(o.cut(e.parentOffset)))}}return v(s,b(t,e,r))}function m(t,e){if(!e.type.compatibleContent(t.type))throw new c("Cannot join "+e.type.name+" onto "+t.type.name)}function g(t,e,n){let r=t.node(n);return m(r,e.node(n)),r}function y(t,e){let n=e.length-1;n>=0&&t.isText&&t.sameMarkup(e[n])?e[n]=t.withText(e[n].text+t.text):e.push(t)}function w(t,e,n,r){let o=(e||t).node(n),i=0,s=e?e.index(n):o.childCount;t&&(i=t.index(n),t.depth>n?i++:t.textOffset&&(y(t.nodeAfter,r),i++));for(let t=i;t<s;t++)y(o.child(t),r);e&&e.depth==n&&e.textOffset&&y(e.nodeBefore,r)}function v(t,e){return t.type.checkContent(e),t.copy(e)}function k(t,e,n,r,i){let s=t.depth>i&&g(t,e,i+1),l=r.depth>i&&g(n,r,i+1),a=[];return w(null,t,i,a),s&&l&&e.index(i)==n.index(i)?(m(s,l),y(v(s,k(t,e,n,r,i+1)),a)):(s&&y(v(s,b(t,e,i+1)),a),w(e,n,i,a),l&&y(v(l,b(n,r,i+1)),a)),w(r,null,i,a),new o(a)}function b(t,e,n){let r=[];if(w(null,t,n,r),t.depth>n){y(v(g(t,e,n+1),b(t,e,n+1)),r)}return w(e,null,n,r),new o(r)}h.empty=new h(o.empty,0,0);class x{constructor(t,e,n){this.pos=t,this.path=e,this.parentOffset=n,this.depth=e.length/3-1}resolveDepth(t){return null==t?this.depth:t<0?this.depth+t:t}get parent(){return this.node(this.depth)}get doc(){return this.node(0)}node(t){return this.path[3*this.resolveDepth(t)]}index(t){return this.path[3*this.resolveDepth(t)+1]}indexAfter(t){return t=this.resolveDepth(t),this.index(t)+(t!=this.depth||this.textOffset?1:0)}start(t){return 0==(t=this.resolveDepth(t))?0:this.path[3*t-1]+1}end(t){return t=this.resolveDepth(t),this.start(t)+this.node(t).content.size}before(t){if(!(t=this.resolveDepth(t)))throw new RangeError("There is no position before the top-level node");return t==this.depth+1?this.pos:this.path[3*t-1]}after(t){if(!(t=this.resolveDepth(t)))throw new RangeError("There is no position after the top-level node");return t==this.depth+1?this.pos:this.path[3*t-1]+this.path[3*t].nodeSize}get textOffset(){return this.pos-this.path[this.path.length-1]}get nodeAfter(){let t=this.parent,e=this.index(this.depth);if(e==t.childCount)return null;let n=this.pos-this.path[this.path.length-1],r=t.child(e);return n?t.child(e).cut(n):r}get nodeBefore(){let t=this.index(this.depth),e=this.pos-this.path[this.path.length-1];return e?this.parent.child(t).cut(0,e):0==t?null:this.parent.child(t-1)}posAtIndex(t,e){e=this.resolveDepth(e);let n=this.path[3*e],r=0==e?0:this.path[3*e-1]+1;for(let e=0;e<t;e++)r+=n.child(e).nodeSize;return r}marks(){let t=this.parent,e=this.index();if(0==t.content.size)return a.none;if(this.textOffset)return t.child(e).marks;let n=t.maybeChild(e-1),r=t.maybeChild(e);if(!n){let t=n;n=r,r=t}let o=n.marks;for(var i=0;i<o.length;i++)!1!==o[i].type.spec.inclusive||r&&o[i].isInSet(r.marks)||(o=o[i--].removeFromSet(o));return o}marksAcross(t){let e=this.parent.maybeChild(this.index());if(!e||!e.isInline)return null;let n=e.marks,r=t.parent.maybeChild(t.index());for(var o=0;o<n.length;o++)!1!==n[o].type.spec.inclusive||r&&n[o].isInSet(r.marks)||(n=n[o--].removeFromSet(n));return n}sharedDepth(t){for(let e=this.depth;e>0;e--)if(this.start(e)<=t&&this.end(e)>=t)return e;return 0}blockRange(t=this,e){if(t.pos<this.pos)return t.blockRange(this);for(let n=this.depth-(this.parent.inlineContent||this.pos==t.pos?1:0);n>=0;n--)if(t.pos<=this.end(n)&&(!e||e(this.node(n))))return new O(this,t,n);return null}sameParent(t){return this.pos-this.parentOffset==t.pos-t.parentOffset}max(t){return t.pos>this.pos?t:this}min(t){return t.pos<this.pos?t:this}toString(){let t="";for(let e=1;e<=this.depth;e++)t+=(t?"/":"")+this.node(e).type.name+"_"+this.index(e-1);return t+":"+this.parentOffset}static resolve(t,e){if(!(e>=0&&e<=t.content.size))throw new RangeError("Position "+e+" out of range");let n=[],r=0,o=e;for(let e=t;;){let{index:t,offset:i}=e.content.findIndex(o),s=o-i;if(n.push(e,t,r+i),!s)break;if(e=e.child(t),e.isText)break;o=s-1,r+=i+1}return new x(e,n,o)}static resolveCached(t,e){let n=C.get(t);if(n)for(let t=0;t<n.elts.length;t++){let r=n.elts[t];if(r.pos==e)return r}else C.set(t,n=new S);let r=n.elts[n.i]=x.resolve(t,e);return n.i=(n.i+1)%M,r}}class S{constructor(){this.elts=[],this.i=0}}const M=12,C=new WeakMap;class O{constructor(t,e,n){this.$from=t,this.$to=e,this.depth=n}get start(){return this.$from.before(this.depth+1)}get end(){return this.$to.after(this.depth+1)}get parent(){return this.$from.node(this.depth)}get startIndex(){return this.$from.index(this.depth)}get endIndex(){return this.$to.indexAfter(this.depth)}}const T=Object.create(null);let E=class t{constructor(t,e,n,r=a.none){this.type=t,this.attrs=e,this.marks=r,this.content=n||o.empty}get children(){return this.content.content}get nodeSize(){return this.isLeaf?1:2+this.content.size}get childCount(){return this.content.childCount}child(t){return this.content.child(t)}maybeChild(t){return this.content.maybeChild(t)}forEach(t){this.content.forEach(t)}nodesBetween(t,e,n,r=0){this.content.nodesBetween(t,e,n,r,this)}descendants(t){this.nodesBetween(0,this.content.size,t)}get textContent(){return this.isLeaf&&this.type.spec.leafText?this.type.spec.leafText(this):this.textBetween(0,this.content.size,"")}textBetween(t,e,n,r){return this.content.textBetween(t,e,n,r)}get firstChild(){return this.content.firstChild}get lastChild(){return this.content.lastChild}eq(t){return this==t||this.sameMarkup(t)&&this.content.eq(t.content)}sameMarkup(t){return this.hasMarkup(t.type,t.attrs,t.marks)}hasMarkup(t,e,n){return this.type==t&&l(this.attrs,e||t.defaultAttrs||T)&&a.sameSet(this.marks,n||a.none)}copy(e=null){return e==this.content?this:new t(this.type,this.attrs,e,this.marks)}mark(e){return e==this.marks?this:new t(this.type,this.attrs,this.content,e)}cut(t,e=this.content.size){return 0==t&&e==this.content.size?this:this.copy(this.content.cut(t,e))}slice(t,e=this.content.size,n=!1){if(t==e)return h.empty;let r=this.resolve(t),o=this.resolve(e),i=n?0:r.sharedDepth(e),s=r.start(i),l=r.node(i).content.cut(r.pos-s,o.pos-s);return new h(l,r.depth-i,o.depth-i)}replace(t,e,n){return u(this.resolve(t),this.resolve(e),n)}nodeAt(t){for(let e=this;;){let{index:n,offset:r}=e.content.findIndex(t);if(e=e.maybeChild(n),!e)return null;if(r==t||e.isText)return e;t-=r+1}}childAfter(t){let{index:e,offset:n}=this.content.findIndex(t);return{node:this.content.maybeChild(e),index:e,offset:n}}childBefore(t){if(0==t)return{node:null,index:0,offset:0};let{index:e,offset:n}=this.content.findIndex(t);if(n<t)return{node:this.content.child(e),index:e,offset:n};let r=this.content.child(e-1);return{node:r,index:e-1,offset:n-r.nodeSize}}resolve(t){return x.resolveCached(this,t)}resolveNoCache(t){return x.resolve(this,t)}rangeHasMark(t,e,n){let r=!1;return e>t&&this.nodesBetween(t,e,(t=>(n.isInSet(t.marks)&&(r=!0),!r))),r}get isBlock(){return this.type.isBlock}get isTextblock(){return this.type.isTextblock}get inlineContent(){return this.type.inlineContent}get isInline(){return this.type.isInline}get isText(){return this.type.isText}get isLeaf(){return this.type.isLeaf}get isAtom(){return this.type.isAtom}toString(){if(this.type.spec.toDebugString)return this.type.spec.toDebugString(this);let t=this.type.name;return this.content.size&&(t+="("+this.content.toStringInner()+")"),A(this.marks,t)}contentMatchAt(t){let e=this.type.contentMatch.matchFragment(this.content,0,t);if(!e)throw new Error("Called contentMatchAt on a node with invalid content");return e}canReplace(t,e,n=o.empty,r=0,i=n.childCount){let s=this.contentMatchAt(t).matchFragment(n,r,i),l=s&&s.matchFragment(this.content,e);if(!l||!l.validEnd)return!1;for(let t=r;t<i;t++)if(!this.type.allowsMarks(n.child(t).marks))return!1;return!0}canReplaceWith(t,e,n,r){if(r&&!this.type.allowsMarks(r))return!1;let o=this.contentMatchAt(t).matchType(n),i=o&&o.matchFragment(this.content,e);return!!i&&i.validEnd}canAppend(t){return t.content.size?this.canReplace(this.childCount,this.childCount,t.content):this.type.compatibleContent(t.type)}check(){this.type.checkContent(this.content),this.type.checkAttrs(this.attrs);let t=a.none;for(let e=0;e<this.marks.length;e++){let n=this.marks[e];n.type.checkAttrs(n.attrs),t=n.addToSet(t)}if(!a.sameSet(t,this.marks))throw new RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map((t=>t.type.name))}`);this.content.forEach((t=>t.check()))}toJSON(){let t={type:this.type.name};for(let e in this.attrs){t.attrs=this.attrs;break}return this.content.size&&(t.content=this.content.toJSON()),this.marks.length&&(t.marks=this.marks.map((t=>t.toJSON()))),t}static fromJSON(t,e){if(!e)throw new RangeError("Invalid input for Node.fromJSON");let n;if(e.marks){if(!Array.isArray(e.marks))throw new RangeError("Invalid mark data for Node.fromJSON");n=e.marks.map(t.markFromJSON)}if("text"==e.type){if("string"!=typeof e.text)throw new RangeError("Invalid text node in JSON");return t.text(e.text,n)}let r=o.fromJSON(t,e.content),i=t.nodeType(e.type).create(e.attrs,r,n);return i.type.checkAttrs(i.attrs),i}};E.prototype.text=void 0;class N extends E{constructor(t,e,n,r){if(super(t,e,null,r),!n)throw new RangeError("Empty text nodes are not allowed");this.text=n}toString(){return this.type.spec.toDebugString?this.type.spec.toDebugString(this):A(this.marks,JSON.stringify(this.text))}get textContent(){return this.text}textBetween(t,e){return this.text.slice(t,e)}get nodeSize(){return this.text.length}mark(t){return t==this.marks?this:new N(this.type,this.attrs,this.text,t)}withText(t){return t==this.text?this:new N(this.type,this.attrs,t,this.marks)}cut(t=0,e=this.text.length){return 0==t&&e==this.text.length?this:this.withText(this.text.slice(t,e))}eq(t){return this.sameMarkup(t)&&this.text==t.text}toJSON(){let t=super.toJSON();return t.text=this.text,t}}function A(t,e){for(let n=t.length-1;n>=0;n--)e=t[n].type.name+"("+e+")";return e}class D{constructor(t){this.validEnd=t,this.next=[],this.wrapCache=[]}static parse(t,e){let n=new R(t,e);if(null==n.next)return D.empty;let r=I(n);n.next&&n.err("Unexpected trailing text");let o=function(t){let e=Object.create(null);return n(F(t,0));function n(r){let o=[];r.forEach((e=>{t[e].forEach((({term:e,to:n})=>{if(!e)return;let r;for(let t=0;t<o.length;t++)o[t][0]==e&&(r=o[t][1]);F(t,n).forEach((t=>{r||o.push([e,r=[]]),-1==r.indexOf(t)&&r.push(t)}))}))}));let i=e[r.join(",")]=new D(r.indexOf(t.length-1)>-1);for(let t=0;t<o.length;t++){let r=o[t][1].sort(B);i.next.push({type:o[t][0],next:e[r.join(",")]||n(r)})}return i}}(function(t){let e=[[]];return o(i(t,0),n()),e;function n(){return e.push([])-1}function r(t,n,r){let o={term:r,to:n};return e[t].push(o),o}function o(t,e){t.forEach((t=>t.to=e))}function i(t,e){if("choice"==t.type)return t.exprs.reduce(((t,n)=>t.concat(i(n,e))),[]);if("seq"!=t.type){if("star"==t.type){let s=n();return r(e,s),o(i(t.expr,s),s),[r(s)]}if("plus"==t.type){let s=n();return o(i(t.expr,e),s),o(i(t.expr,s),s),[r(s)]}if("opt"==t.type)return[r(e)].concat(i(t.expr,e));if("range"==t.type){let s=e;for(let e=0;e<t.min;e++){let e=n();o(i(t.expr,s),e),s=e}if(-1==t.max)o(i(t.expr,s),s);else for(let e=t.min;e<t.max;e++){let e=n();r(s,e),o(i(t.expr,s),e),s=e}return[r(s)]}if("name"==t.type)return[r(e,void 0,t.value)];throw new Error("Unknown expr type")}for(let r=0;;r++){let s=i(t.exprs[r],e);if(r==t.exprs.length-1)return s;o(s,e=n())}}}(r));return function(t,e){for(let n=0,r=[t];n<r.length;n++){let t=r[n],o=!t.validEnd,i=[];for(let e=0;e<t.next.length;e++){let{type:n,next:s}=t.next[e];i.push(n.name),!o||n.isText||n.hasRequiredAttrs()||(o=!1),-1==r.indexOf(s)&&r.push(s)}o&&e.err("Only non-generatable nodes ("+i.join(", ")+") in a required position (see https://prosemirror.net/docs/guide/#generatable)")}}(o,n),o}matchType(t){for(let e=0;e<this.next.length;e++)if(this.next[e].type==t)return this.next[e].next;return null}matchFragment(t,e=0,n=t.childCount){let r=this;for(let o=e;r&&o<n;o++)r=r.matchType(t.child(o).type);return r}get inlineContent(){return 0!=this.next.length&&this.next[0].type.isInline}get defaultType(){for(let t=0;t<this.next.length;t++){let{type:e}=this.next[t];if(!e.isText&&!e.hasRequiredAttrs())return e}return null}compatible(t){for(let e=0;e<this.next.length;e++)for(let n=0;n<t.next.length;n++)if(this.next[e].type==t.next[n].type)return!0;return!1}fillBefore(t,e=!1,n=0){let r=[this];return function i(s,l){let a=s.matchFragment(t,n);if(a&&(!e||a.validEnd))return o.from(l.map((t=>t.createAndFill())));for(let t=0;t<s.next.length;t++){let{type:e,next:n}=s.next[t];if(!e.isText&&!e.hasRequiredAttrs()&&-1==r.indexOf(n)){r.push(n);let t=i(n,l.concat(e));if(t)return t}}return null}(this,[])}findWrapping(t){for(let e=0;e<this.wrapCache.length;e+=2)if(this.wrapCache[e]==t)return this.wrapCache[e+1];let e=this.computeWrapping(t);return this.wrapCache.push(t,e),e}computeWrapping(t){let e=Object.create(null),n=[{match:this,type:null,via:null}];for(;n.length;){let r=n.shift(),o=r.match;if(o.matchType(t)){let t=[];for(let e=r;e.type;e=e.via)t.push(e.type);return t.reverse()}for(let t=0;t<o.next.length;t++){let{type:i,next:s}=o.next[t];i.isLeaf||i.hasRequiredAttrs()||i.name in e||r.type&&!s.validEnd||(n.push({match:i.contentMatch,type:i,via:r}),e[i.name]=!0)}}return null}get edgeCount(){return this.next.length}edge(t){if(t>=this.next.length)throw new RangeError(`There's no ${t}th edge in this content match`);return this.next[t]}toString(){let t=[];return function e(n){t.push(n);for(let r=0;r<n.next.length;r++)-1==t.indexOf(n.next[r].next)&&e(n.next[r].next)}(this),t.map(((e,n)=>{let r=n+(e.validEnd?"*":" ")+" ";for(let n=0;n<e.next.length;n++)r+=(n?", ":"")+e.next[n].type.name+"->"+t.indexOf(e.next[n].next);return r})).join("\n")}}D.empty=new D(!0);class R{constructor(t,e){this.string=t,this.nodeTypes=e,this.inline=null,this.pos=0,this.tokens=t.split(/\s*(?=\b|\W|$)/),""==this.tokens[this.tokens.length-1]&&this.tokens.pop(),""==this.tokens[0]&&this.tokens.shift()}get next(){return this.tokens[this.pos]}eat(t){return this.next==t&&(this.pos++||!0)}err(t){throw new SyntaxError(t+" (in content expression '"+this.string+"')")}}function I(t){let e=[];do{e.push(P(t))}while(t.eat("|"));return 1==e.length?e[0]:{type:"choice",exprs:e}}function P(t){let e=[];do{e.push(z(t))}while(t.next&&")"!=t.next&&"|"!=t.next);return 1==e.length?e[0]:{type:"seq",exprs:e}}function z(t){let e=function(t){if(t.eat("(")){let e=I(t);return t.eat(")")||t.err("Missing closing paren"),e}if(!/\W/.test(t.next)){let e=function(t,e){let n=t.nodeTypes,r=n[e];if(r)return[r];let o=[];for(let t in n){let r=n[t];r.isInGroup(e)&&o.push(r)}0==o.length&&t.err("No node type or group '"+e+"' found");return o}(t,t.next).map((e=>(null==t.inline?t.inline=e.isInline:t.inline!=e.isInline&&t.err("Mixing inline and block content"),{type:"name",value:e})));return t.pos++,1==e.length?e[0]:{type:"choice",exprs:e}}t.err("Unexpected token '"+t.next+"'")}(t);for(;;)if(t.eat("+"))e={type:"plus",expr:e};else if(t.eat("*"))e={type:"star",expr:e};else if(t.eat("?"))e={type:"opt",expr:e};else{if(!t.eat("{"))break;e=$(t,e)}return e}function L(t){/\D/.test(t.next)&&t.err("Expected number, got '"+t.next+"'");let e=Number(t.next);return t.pos++,e}function $(t,e){let n=L(t),r=n;return t.eat(",")&&(r="}"!=t.next?L(t):-1),t.eat("}")||t.err("Unclosed braced range"),{type:"range",min:n,max:r,expr:e}}function B(t,e){return e-t}function F(t,e){let n=[];return function e(r){let o=t[r];if(1==o.length&&!o[0].term)return e(o[0].to);n.push(r);for(let t=0;t<o.length;t++){let{term:r,to:i}=o[t];r||-1!=n.indexOf(i)||e(i)}}(e),n.sort(B)}function V(t){let e=Object.create(null);for(let n in t){let r=t[n];if(!r.hasDefault)return null;e[n]=r.default}return e}function j(t,e){let n=Object.create(null);for(let r in t){let o=e&&e[r];if(void 0===o){let e=t[r];if(!e.hasDefault)throw new RangeError("No value supplied for attribute "+r);o=e.default}n[r]=o}return n}function H(t,e,n,r){for(let r in e)if(!(r in t))throw new RangeError(`Unsupported attribute ${r} for ${n} of type ${r}`);for(let n in t){let r=t[n];r.validate&&r.validate(e[n])}}function W(t,e){let n=Object.create(null);if(e)for(let r in e)n[r]=new J(t,r,e[r]);return n}let q=class t{constructor(t,e,n){this.name=t,this.schema=e,this.spec=n,this.markSet=null,this.groups=n.group?n.group.split(" "):[],this.attrs=W(t,n.attrs),this.defaultAttrs=V(this.attrs),this.contentMatch=null,this.inlineContent=null,this.isBlock=!(n.inline||"text"==t),this.isText="text"==t}get isInline(){return!this.isBlock}get isTextblock(){return this.isBlock&&this.inlineContent}get isLeaf(){return this.contentMatch==D.empty}get isAtom(){return this.isLeaf||!!this.spec.atom}isInGroup(t){return this.groups.indexOf(t)>-1}get whitespace(){return this.spec.whitespace||(this.spec.code?"pre":"normal")}hasRequiredAttrs(){for(let t in this.attrs)if(this.attrs[t].isRequired)return!0;return!1}compatibleContent(t){return this==t||this.contentMatch.compatible(t.contentMatch)}computeAttrs(t){return!t&&this.defaultAttrs?this.defaultAttrs:j(this.attrs,t)}create(t=null,e,n){if(this.isText)throw new Error("NodeType.create can't construct text nodes");return new E(this,this.computeAttrs(t),o.from(e),a.setFrom(n))}createChecked(t=null,e,n){return e=o.from(e),this.checkContent(e),new E(this,this.computeAttrs(t),e,a.setFrom(n))}createAndFill(t=null,e,n){if(t=this.computeAttrs(t),(e=o.from(e)).size){let t=this.contentMatch.fillBefore(e);if(!t)return null;e=t.append(e)}let r=this.contentMatch.matchFragment(e),i=r&&r.fillBefore(o.empty,!0);return i?new E(this,t,e.append(i),a.setFrom(n)):null}validContent(t){let e=this.contentMatch.matchFragment(t);if(!e||!e.validEnd)return!1;for(let e=0;e<t.childCount;e++)if(!this.allowsMarks(t.child(e).marks))return!1;return!0}checkContent(t){if(!this.validContent(t))throw new RangeError(`Invalid content for node ${this.name}: ${t.toString().slice(0,50)}`)}checkAttrs(t){H(this.attrs,t,"node",this.name)}allowsMarkType(t){return null==this.markSet||this.markSet.indexOf(t)>-1}allowsMarks(t){if(null==this.markSet)return!0;for(let e=0;e<t.length;e++)if(!this.allowsMarkType(t[e].type))return!1;return!0}allowedMarks(t){if(null==this.markSet)return t;let e;for(let n=0;n<t.length;n++)this.allowsMarkType(t[n].type)?e&&e.push(t[n]):e||(e=t.slice(0,n));return e?e.length?e:a.none:t}static compile(e,n){let r=Object.create(null);e.forEach(((e,o)=>r[e]=new t(e,n,o)));let o=n.spec.topNode||"doc";if(!r[o])throw new RangeError("Schema is missing its top node type ('"+o+"')");if(!r.text)throw new RangeError("Every schema needs a 'text' type");for(let t in r.text.attrs)throw new RangeError("The text node type should not have attributes");return r}};class J{constructor(t,e,n){this.hasDefault=Object.prototype.hasOwnProperty.call(n,"default"),this.default=n.default,this.validate="string"==typeof n.validate?function(t,e,n){let r=n.split("|");return n=>{let o=null===n?"null":typeof n;if(r.indexOf(o)<0)throw new RangeError(`Expected value of type ${r} for attribute ${e} on type ${t}, got ${o}`)}}(t,e,n.validate):n.validate}get isRequired(){return!this.hasDefault}}class K{constructor(t,e,n,r){this.name=t,this.rank=e,this.schema=n,this.spec=r,this.attrs=W(t,r.attrs),this.excluded=null;let o=V(this.attrs);this.instance=o?new a(this,o):null}create(t=null){return!t&&this.instance?this.instance:new a(this,j(this.attrs,t))}static compile(t,e){let n=Object.create(null),r=0;return t.forEach(((t,o)=>n[t]=new K(t,r++,e,o))),n}removeFromSet(t){for(var e=0;e<t.length;e++)t[e].type==this&&(t=t.slice(0,e).concat(t.slice(e+1)),e--);return t}isInSet(t){for(let e=0;e<t.length;e++)if(t[e].type==this)return t[e]}checkAttrs(t){H(this.attrs,t,"mark",this.name)}excludes(t){return this.excluded.indexOf(t)>-1}}class _{constructor(t){this.linebreakReplacement=null,this.cached=Object.create(null);let n=this.spec={};for(let e in t)n[e]=t[e];n.nodes=e.from(t.nodes),n.marks=e.from(t.marks||{}),this.nodes=q.compile(this.spec.nodes,this),this.marks=K.compile(this.spec.marks,this);let r=Object.create(null);for(let t in this.nodes){if(t in this.marks)throw new RangeError(t+" can not be both a node and a mark");let e=this.nodes[t],n=e.spec.content||"",o=e.spec.marks;if(e.contentMatch=r[n]||(r[n]=D.parse(n,this.nodes)),e.inlineContent=e.contentMatch.inlineContent,e.spec.linebreakReplacement){if(this.linebreakReplacement)throw new RangeError("Multiple linebreak nodes defined");if(!e.isInline||!e.isLeaf)throw new RangeError("Linebreak replacement nodes must be inline leaf nodes");this.linebreakReplacement=e}e.markSet="_"==o?null:o?U(this,o.split(" ")):""!=o&&e.inlineContent?null:[]}for(let t in this.marks){let e=this.marks[t],n=e.spec.excludes;e.excluded=null==n?[e]:""==n?[]:U(this,n.split(" "))}this.nodeFromJSON=this.nodeFromJSON.bind(this),this.markFromJSON=this.markFromJSON.bind(this),this.topNodeType=this.nodes[this.spec.topNode||"doc"],this.cached.wrappings=Object.create(null)}node(t,e=null,n,r){if("string"==typeof t)t=this.nodeType(t);else{if(!(t instanceof q))throw new RangeError("Invalid node type: "+t);if(t.schema!=this)throw new RangeError("Node type from different schema used ("+t.name+")")}return t.createChecked(e,n,r)}text(t,e){let n=this.nodes.text;return new N(n,n.defaultAttrs,t,a.setFrom(e))}mark(t,e){return"string"==typeof t&&(t=this.marks[t]),t.create(e)}nodeFromJSON(t){return E.fromJSON(this,t)}markFromJSON(t){return a.fromJSON(this,t)}nodeType(t){let e=this.nodes[t];if(!e)throw new RangeError("Unknown node type: "+t);return e}}function U(t,e){let n=[];for(let r=0;r<e.length;r++){let o=e[r],i=t.marks[o],s=i;if(i)n.push(i);else for(let e in t.marks){let r=t.marks[e];("_"==o||r.spec.group&&r.spec.group.split(" ").indexOf(o)>-1)&&n.push(s=r)}if(!s)throw new SyntaxError("Unknown mark type: '"+e[r]+"'")}return n}class G{constructor(t,e){this.schema=t,this.rules=e,this.tags=[],this.styles=[];let n=this.matchedStyles=[];e.forEach((t=>{if(function(t){return null!=t.tag}(t))this.tags.push(t);else if(function(t){return null!=t.style}(t)){let e=/[^=]*/.exec(t.style)[0];n.indexOf(e)<0&&n.push(e),this.styles.push(t)}})),this.normalizeLists=!this.tags.some((e=>{if(!/^(ul|ol)\b/.test(e.tag)||!e.node)return!1;let n=t.nodes[e.node];return n.contentMatch.matchType(n)}))}parse(t,e={}){let n=new et(this,e,!1);return n.addAll(t,a.none,e.from,e.to),n.finish()}parseSlice(t,e={}){let n=new et(this,e,!0);return n.addAll(t,a.none,e.from,e.to),h.maxOpen(n.finish())}matchTag(t,e,n){for(let r=n?this.tags.indexOf(n)+1:0;r<this.tags.length;r++){let n=this.tags[r];if(nt(t,n.tag)&&(void 0===n.namespace||t.namespaceURI==n.namespace)&&(!n.context||e.matchesContext(n.context))){if(n.getAttrs){let e=n.getAttrs(t);if(!1===e)continue;n.attrs=e||void 0}return n}}}matchStyle(t,e,n,r){for(let o=r?this.styles.indexOf(r)+1:0;o<this.styles.length;o++){let r=this.styles[o],i=r.style;if(!(0!=i.indexOf(t)||r.context&&!n.matchesContext(r.context)||i.length>t.length&&(61!=i.charCodeAt(t.length)||i.slice(t.length+1)!=e))){if(r.getAttrs){let t=r.getAttrs(e);if(!1===t)continue;r.attrs=t||void 0}return r}}}static schemaRules(t){let e=[];function n(t){let n=null==t.priority?50:t.priority,r=0;for(;r<e.length;r++){let t=e[r];if((null==t.priority?50:t.priority)<n)break}e.splice(r,0,t)}for(let e in t.marks){let r=t.marks[e].spec.parseDOM;r&&r.forEach((t=>{n(t=rt(t)),t.mark||t.ignore||t.clearMark||(t.mark=e)}))}for(let e in t.nodes){let r=t.nodes[e].spec.parseDOM;r&&r.forEach((t=>{n(t=rt(t)),t.node||t.ignore||t.mark||(t.node=e)}))}return e}static fromSchema(t){return t.cached.domParser||(t.cached.domParser=new G(t,G.schemaRules(t)))}}const Q={address:!0,article:!0,aside:!0,blockquote:!0,canvas:!0,dd:!0,div:!0,dl:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,li:!0,noscript:!0,ol:!0,output:!0,p:!0,pre:!0,section:!0,table:!0,tfoot:!0,ul:!0},Y={head:!0,noscript:!0,object:!0,script:!0,style:!0,title:!0},X={ol:!0,ul:!0};function Z(t,e,n){return null!=e?(e?1:0)|("full"===e?2:0):t&&"pre"==t.whitespace?3:-5&n}class tt{constructor(t,e,n,r,o,i){this.type=t,this.attrs=e,this.marks=n,this.solid=r,this.options=i,this.content=[],this.activeMarks=a.none,this.match=o||(4&i?null:t.contentMatch)}findWrapping(t){if(!this.match){if(!this.type)return[];let e=this.type.contentMatch.fillBefore(o.from(t));if(!e){let e,n=this.type.contentMatch;return(e=n.findWrapping(t.type))?(this.match=n,e):null}this.match=this.type.contentMatch.matchFragment(e)}return this.match.findWrapping(t.type)}finish(t){if(!(1&this.options)){let t,e=this.content[this.content.length-1];if(e&&e.isText&&(t=/[ \t\r\n\u000c]+$/.exec(e.text))){let n=e;e.text.length==t[0].length?this.content.pop():this.content[this.content.length-1]=n.withText(n.text.slice(0,n.text.length-t[0].length))}}let e=o.from(this.content);return!t&&this.match&&(e=e.append(this.match.fillBefore(o.empty,!0))),this.type?this.type.create(this.attrs,e,this.marks):e}inlineContext(t){return this.type?this.type.inlineContent:this.content.length?this.content[0].isInline:t.parentNode&&!Q.hasOwnProperty(t.parentNode.nodeName.toLowerCase())}}class et{constructor(t,e,n){this.parser=t,this.options=e,this.isOpen=n,this.open=0,this.localPreserveWS=!1;let r,o=e.topNode,i=Z(null,e.preserveWhitespace,0)|(n?4:0);r=o?new tt(o.type,o.attrs,a.none,!0,e.topMatch||o.type.contentMatch,i):new tt(n?null:t.schema.topNodeType,null,a.none,!0,null,i),this.nodes=[r],this.find=e.findPositions,this.needsBlock=!1}get top(){return this.nodes[this.open]}addDOM(t,e){3==t.nodeType?this.addTextNode(t,e):1==t.nodeType&&this.addElement(t,e)}addTextNode(t,e){let n=t.nodeValue,r=this.top,o=2&r.options?"full":this.localPreserveWS||(1&r.options)>0;if("full"===o||r.inlineContext(t)||/[^ \t\r\n\u000c]/.test(n)){if(o)n="full"!==o?n.replace(/\r?\n|\r/g," "):n.replace(/\r\n?/g,"\n");else if(n=n.replace(/[ \t\r\n\u000c]+/g," "),/^[ \t\r\n\u000c]/.test(n)&&this.open==this.nodes.length-1){let e=r.content[r.content.length-1],o=t.previousSibling;(!e||o&&"BR"==o.nodeName||e.isText&&/[ \t\r\n\u000c]$/.test(e.text))&&(n=n.slice(1))}n&&this.insertNode(this.parser.schema.text(n),e),this.findInText(t)}else this.findInside(t)}addElement(t,e,n){let r=this.localPreserveWS,o=this.top;("PRE"==t.tagName||/pre/.test(t.style&&t.style.whiteSpace))&&(this.localPreserveWS=!0);let i,s=t.nodeName.toLowerCase();X.hasOwnProperty(s)&&this.parser.normalizeLists&&function(t){for(let e=t.firstChild,n=null;e;e=e.nextSibling){let t=1==e.nodeType?e.nodeName.toLowerCase():null;t&&X.hasOwnProperty(t)&&n?(n.appendChild(e),e=n):"li"==t?n=e:t&&(n=null)}}(t);let l=this.options.ruleFromNode&&this.options.ruleFromNode(t)||(i=this.parser.matchTag(t,this,n));t:if(l?l.ignore:Y.hasOwnProperty(s))this.findInside(t),this.ignoreFallback(t,e);else if(!l||l.skip||l.closeParent){l&&l.closeParent?this.open=Math.max(0,this.open-1):l&&l.skip.nodeType&&(t=l.skip);let n,r=this.needsBlock;if(Q.hasOwnProperty(s))o.content.length&&o.content[0].isInline&&this.open&&(this.open--,o=this.top),n=!0,o.type||(this.needsBlock=!0);else if(!t.firstChild){this.leafFallback(t,e);break t}let i=l&&l.skip?e:this.readStyles(t,e);i&&this.addAll(t,i),n&&this.sync(o),this.needsBlock=r}else{let n=this.readStyles(t,e);n&&this.addElementByRule(t,l,n,!1===l.consuming?i:void 0)}this.localPreserveWS=r}leafFallback(t,e){"BR"==t.nodeName&&this.top.type&&this.top.type.inlineContent&&this.addTextNode(t.ownerDocument.createTextNode("\n"),e)}ignoreFallback(t,e){"BR"!=t.nodeName||this.top.type&&this.top.type.inlineContent||this.findPlace(this.parser.schema.text("-"),e)}readStyles(t,e){let n=t.style;if(n&&n.length)for(let t=0;t<this.parser.matchedStyles.length;t++){let r=this.parser.matchedStyles[t],o=n.getPropertyValue(r);if(o)for(let t;;){let n=this.parser.matchStyle(r,o,this,t);if(!n)break;if(n.ignore)return null;if(e=n.clearMark?e.filter((t=>!n.clearMark(t))):e.concat(this.parser.schema.marks[n.mark].create(n.attrs)),!1!==n.consuming)break;t=n}}return e}addElementByRule(t,e,n,r){let o,i;if(e.node)if(i=this.parser.schema.nodes[e.node],i.isLeaf)this.insertNode(i.create(e.attrs),n)||this.leafFallback(t,n);else{let t=this.enter(i,e.attrs||null,n,e.preserveWhitespace);t&&(o=!0,n=t)}else{let t=this.parser.schema.marks[e.mark];n=n.concat(t.create(e.attrs))}let s=this.top;if(i&&i.isLeaf)this.findInside(t);else if(r)this.addElement(t,n,r);else if(e.getContent)this.findInside(t),e.getContent(t,this.parser.schema).forEach((t=>this.insertNode(t,n)));else{let r=t;"string"==typeof e.contentElement?r=t.querySelector(e.contentElement):"function"==typeof e.contentElement?r=e.contentElement(t):e.contentElement&&(r=e.contentElement),this.findAround(t,r,!0),this.addAll(r,n),this.findAround(t,r,!1)}o&&this.sync(s)&&this.open--}addAll(t,e,n,r){let o=n||0;for(let i=n?t.childNodes[n]:t.firstChild,s=null==r?null:t.childNodes[r];i!=s;i=i.nextSibling,++o)this.findAtPoint(t,o),this.addDOM(i,e);this.findAtPoint(t,o)}findPlace(t,e){let n,r;for(let e=this.open;e>=0;e--){let o=this.nodes[e],i=o.findWrapping(t);if(i&&(!n||n.length>i.length)&&(n=i,r=o,!i.length))break;if(o.solid)break}if(!n)return null;this.sync(r);for(let t=0;t<n.length;t++)e=this.enterInner(n[t],null,e,!1);return e}insertNode(t,e){if(t.isInline&&this.needsBlock&&!this.top.type){let t=this.textblockFromContext();t&&(e=this.enterInner(t,null,e))}let n=this.findPlace(t,e);if(n){this.closeExtra();let e=this.top;e.match&&(e.match=e.match.matchType(t.type));let r=a.none;for(let o of n.concat(t.marks))(e.type?e.type.allowsMarkType(o.type):ot(o.type,t.type))&&(r=o.addToSet(r));return e.content.push(t.mark(r)),!0}return!1}enter(t,e,n,r){let o=this.findPlace(t.create(e),n);return o&&(o=this.enterInner(t,e,n,!0,r)),o}enterInner(t,e,n,r=!1,o){this.closeExtra();let i=this.top;i.match=i.match&&i.match.matchType(t);let s=Z(t,o,i.options);4&i.options&&0==i.content.length&&(s|=4);let l=a.none;return n=n.filter((e=>!(i.type?i.type.allowsMarkType(e.type):ot(e.type,t))||(l=e.addToSet(l),!1))),this.nodes.push(new tt(t,e,l,r,null,s)),this.open++,n}closeExtra(t=!1){let e=this.nodes.length-1;if(e>this.open){for(;e>this.open;e--)this.nodes[e-1].content.push(this.nodes[e].finish(t));this.nodes.length=this.open+1}}finish(){return this.open=0,this.closeExtra(this.isOpen),this.nodes[0].finish(!(!this.isOpen&&!this.options.topOpen))}sync(t){for(let e=this.open;e>=0;e--){if(this.nodes[e]==t)return this.open=e,!0;this.localPreserveWS&&(this.nodes[e].options|=1)}return!1}get currentPos(){this.closeExtra();let t=0;for(let e=this.open;e>=0;e--){let n=this.nodes[e].content;for(let e=n.length-1;e>=0;e--)t+=n[e].nodeSize;e&&t++}return t}findAtPoint(t,e){if(this.find)for(let n=0;n<this.find.length;n++)this.find[n].node==t&&this.find[n].offset==e&&(this.find[n].pos=this.currentPos)}findInside(t){if(this.find)for(let e=0;e<this.find.length;e++)null==this.find[e].pos&&1==t.nodeType&&t.contains(this.find[e].node)&&(this.find[e].pos=this.currentPos)}findAround(t,e,n){if(t!=e&&this.find)for(let r=0;r<this.find.length;r++)if(null==this.find[r].pos&&1==t.nodeType&&t.contains(this.find[r].node)){e.compareDocumentPosition(this.find[r].node)&(n?2:4)&&(this.find[r].pos=this.currentPos)}}findInText(t){if(this.find)for(let e=0;e<this.find.length;e++)this.find[e].node==t&&(this.find[e].pos=this.currentPos-(t.nodeValue.length-this.find[e].offset))}matchesContext(t){if(t.indexOf("|")>-1)return t.split(/\s*\|\s*/).some(this.matchesContext,this);let e=t.split("/"),n=this.options.context,r=!(this.isOpen||n&&n.parent.type!=this.nodes[0].type),o=-(n?n.depth+1:0)+(r?0:1),i=(t,s)=>{for(;t>=0;t--){let l=e[t];if(""==l){if(t==e.length-1||0==t)continue;for(;s>=o;s--)if(i(t-1,s))return!0;return!1}{let t=s>0||0==s&&r?this.nodes[s].type:n&&s>=o?n.node(s-o).type:null;if(!t||t.name!=l&&!t.isInGroup(l))return!1;s--}}return!0};return i(e.length-1,this.open)}textblockFromContext(){let t=this.options.context;if(t)for(let e=t.depth;e>=0;e--){let n=t.node(e).contentMatchAt(t.indexAfter(e)).defaultType;if(n&&n.isTextblock&&n.defaultAttrs)return n}for(let t in this.parser.schema.nodes){let e=this.parser.schema.nodes[t];if(e.isTextblock&&e.defaultAttrs)return e}}}function nt(t,e){return(t.matches||t.msMatchesSelector||t.webkitMatchesSelector||t.mozMatchesSelector).call(t,e)}function rt(t){let e={};for(let n in t)e[n]=t[n];return e}function ot(t,e){let n=e.schema.nodes;for(let r in n){let o=n[r];if(!o.allowsMarkType(t))continue;let i=[],s=t=>{i.push(t);for(let n=0;n<t.edgeCount;n++){let{type:r,next:o}=t.edge(n);if(r==e)return!0;if(i.indexOf(o)<0&&s(o))return!0}};if(s(o.contentMatch))return!0}}class it{constructor(t,e){this.nodes=t,this.marks=e}serializeFragment(t,e={},n){n||(n=lt(e).createDocumentFragment());let r=n,o=[];return t.forEach((t=>{if(o.length||t.marks.length){let n=0,i=0;for(;n<o.length&&i<t.marks.length;){let e=t.marks[i];if(this.marks[e.type.name]){if(!e.eq(o[n][0])||!1===e.type.spec.spanning)break;n++,i++}else i++}for(;n<o.length;)r=o.pop()[1];for(;i<t.marks.length;){let n=t.marks[i++],s=this.serializeMark(n,t.isInline,e);s&&(o.push([n,r]),r.appendChild(s.dom),r=s.contentDOM||s.dom)}}r.appendChild(this.serializeNodeInner(t,e))})),n}serializeNodeInner(t,e){let{dom:n,contentDOM:r}=ht(lt(e),this.nodes[t.type.name](t),null,t.attrs);if(r){if(t.isLeaf)throw new RangeError("Content hole not allowed in a leaf node spec");this.serializeFragment(t.content,e,r)}return n}serializeNode(t,e={}){let n=this.serializeNodeInner(t,e);for(let r=t.marks.length-1;r>=0;r--){let o=this.serializeMark(t.marks[r],t.isInline,e);o&&((o.contentDOM||o.dom).appendChild(n),n=o.dom)}return n}serializeMark(t,e,n={}){let r=this.marks[t.type.name];return r&&ht(lt(n),r(t,e),null,t.attrs)}static renderSpec(t,e,n=null,r){return ht(t,e,n,r)}static fromSchema(t){return t.cached.domSerializer||(t.cached.domSerializer=new it(this.nodesFromSchema(t),this.marksFromSchema(t)))}static nodesFromSchema(t){let e=st(t.nodes);return e.text||(e.text=t=>t.text),e}static marksFromSchema(t){return st(t.marks)}}function st(t){let e={};for(let n in t){let r=t[n].spec.toDOM;r&&(e[n]=r)}return e}function lt(t){return t.document||window.document}const at=new WeakMap;function ct(t){let e=at.get(t);return void 0===e&&at.set(t,e=function(t){let e=null;function n(t){if(t&&"object"==typeof t)if(Array.isArray(t))if("string"==typeof t[0])e||(e=[]),e.push(t);else for(let e=0;e<t.length;e++)n(t[e]);else for(let e in t)n(t[e])}return n(t),e}(t)),e}function ht(t,e,n,r){if("string"==typeof e)return{dom:t.createTextNode(e)};if(null!=e.nodeType)return{dom:e};if(e.dom&&null!=e.dom.nodeType)return e;let o,i=e[0];if("string"!=typeof i)throw new RangeError("Invalid array passed to renderSpec");if(r&&(o=ct(r))&&o.indexOf(e)>-1)throw new RangeError("Using an array from an attribute object as a DOM spec. This may be an attempted cross site scripting attack.");let s,l=i.indexOf(" ");l>0&&(n=i.slice(0,l),i=i.slice(l+1));let a=n?t.createElementNS(n,i):t.createElement(i),c=e[1],h=1;if(c&&"object"==typeof c&&null==c.nodeType&&!Array.isArray(c)){h=2;for(let t in c)if(null!=c[t]){let e=t.indexOf(" ");e>0?a.setAttributeNS(t.slice(0,e),t.slice(e+1),c[t]):a.setAttribute(t,c[t])}}for(let o=h;o<e.length;o++){let i=e[o];if(0===i){if(o<e.length-1||o>h)throw new RangeError("Content hole must be the only child of its parent node");return{dom:a,contentDOM:a}}{let{dom:e,contentDOM:o}=ht(t,i,n,r);if(a.appendChild(e),o){if(s)throw new RangeError("Multiple content holes");s=o}}}return{dom:a,contentDOM:s}}const dt=Math.pow(2,16);function pt(t){return 65535&t}class ut{constructor(t,e,n){this.pos=t,this.delInfo=e,this.recover=n}get deleted(){return(8&this.delInfo)>0}get deletedBefore(){return(5&this.delInfo)>0}get deletedAfter(){return(6&this.delInfo)>0}get deletedAcross(){return(4&this.delInfo)>0}}class ft{constructor(t,e=!1){if(this.ranges=t,this.inverted=e,!t.length&&ft.empty)return ft.empty}recover(t){let e=0,n=pt(t);if(!this.inverted)for(let t=0;t<n;t++)e+=this.ranges[3*t+2]-this.ranges[3*t+1];return this.ranges[3*n]+e+function(t){return(t-(65535&t))/dt}(t)}mapResult(t,e=1){return this._map(t,e,!1)}map(t,e=1){return this._map(t,e,!0)}_map(t,e,n){let r=0,o=this.inverted?2:1,i=this.inverted?1:2;for(let s=0;s<this.ranges.length;s+=3){let l=this.ranges[s]-(this.inverted?r:0);if(l>t)break;let a=this.ranges[s+o],c=this.ranges[s+i],h=l+a;if(t<=h){let o=l+r+((a?t==l?-1:t==h?1:e:e)<0?0:c);if(n)return o;let i=t==(e<0?l:h)?null:s/3+(t-l)*dt,d=t==l?2:t==h?1:4;return(e<0?t!=l:t!=h)&&(d|=8),new ut(o,d,i)}r+=c-a}return n?t+r:new ut(t+r,0,null)}touches(t,e){let n=0,r=pt(e),o=this.inverted?2:1,i=this.inverted?1:2;for(let e=0;e<this.ranges.length;e+=3){let s=this.ranges[e]-(this.inverted?n:0);if(s>t)break;let l=this.ranges[e+o];if(t<=s+l&&e==3*r)return!0;n+=this.ranges[e+i]-l}return!1}forEach(t){let e=this.inverted?2:1,n=this.inverted?1:2;for(let r=0,o=0;r<this.ranges.length;r+=3){let i=this.ranges[r],s=i-(this.inverted?o:0),l=i+(this.inverted?0:o),a=this.ranges[r+e],c=this.ranges[r+n];t(s,s+a,l,l+c),o+=c-a}}invert(){return new ft(this.ranges,!this.inverted)}toString(){return(this.inverted?"-":"")+JSON.stringify(this.ranges)}static offset(t){return 0==t?ft.empty:new ft(t<0?[0,-t,0]:[0,0,t])}}ft.empty=new ft([]);class mt{constructor(t=[],e,n=0,r=t.length){this.maps=t,this.mirror=e,this.from=n,this.to=r}slice(t=0,e=this.maps.length){return new mt(this.maps,this.mirror,t,e)}copy(){return new mt(this.maps.slice(),this.mirror&&this.mirror.slice(),this.from,this.to)}appendMap(t,e){this.to=this.maps.push(t),null!=e&&this.setMirror(this.maps.length-1,e)}appendMapping(t){for(let e=0,n=this.maps.length;e<t.maps.length;e++){let r=t.getMirror(e);this.appendMap(t.maps[e],null!=r&&r<e?n+r:void 0)}}getMirror(t){if(this.mirror)for(let e=0;e<this.mirror.length;e++)if(this.mirror[e]==t)return this.mirror[e+(e%2?-1:1)]}setMirror(t,e){this.mirror||(this.mirror=[]),this.mirror.push(t,e)}appendMappingInverted(t){for(let e=t.maps.length-1,n=this.maps.length+t.maps.length;e>=0;e--){let r=t.getMirror(e);this.appendMap(t.maps[e].invert(),null!=r&&r>e?n-r-1:void 0)}}invert(){let t=new mt;return t.appendMappingInverted(this),t}map(t,e=1){if(this.mirror)return this._map(t,e,!0);for(let n=this.from;n<this.to;n++)t=this.maps[n].map(t,e);return t}mapResult(t,e=1){return this._map(t,e,!1)}_map(t,e,n){let r=0;for(let n=this.from;n<this.to;n++){let o=this.maps[n].mapResult(t,e);if(null!=o.recover){let e=this.getMirror(n);if(null!=e&&e>n&&e<this.to){n=e,t=this.maps[e].recover(o.recover);continue}}r|=o.delInfo,t=o.pos}return n?t:new ut(t,r,null)}}const gt=Object.create(null);class yt{getMap(){return ft.empty}merge(t){return null}static fromJSON(t,e){if(!e||!e.stepType)throw new RangeError("Invalid input for Step.fromJSON");let n=gt[e.stepType];if(!n)throw new RangeError(`No step type ${e.stepType} defined`);return n.fromJSON(t,e)}static jsonID(t,e){if(t in gt)throw new RangeError("Duplicate use of step JSON ID "+t);return gt[t]=e,e.prototype.jsonID=t,e}}class wt{constructor(t,e){this.doc=t,this.failed=e}static ok(t){return new wt(t,null)}static fail(t){return new wt(null,t)}static fromReplace(t,e,n,r){try{return wt.ok(t.replace(e,n,r))}catch(t){if(t instanceof c)return wt.fail(t.message);throw t}}}function vt(t,e,n){let r=[];for(let o=0;o<t.childCount;o++){let i=t.child(o);i.content.size&&(i=i.copy(vt(i.content,e,i))),i.isInline&&(i=e(i,n,o)),r.push(i)}return o.fromArray(r)}class kt extends yt{constructor(t,e,n){super(),this.from=t,this.to=e,this.mark=n}apply(t){let e=t.slice(this.from,this.to),n=t.resolve(this.from),r=n.node(n.sharedDepth(this.to)),o=new h(vt(e.content,((t,e)=>t.isAtom&&e.type.allowsMarkType(this.mark.type)?t.mark(this.mark.addToSet(t.marks)):t),r),e.openStart,e.openEnd);return wt.fromReplace(t,this.from,this.to,o)}invert(){return new bt(this.from,this.to,this.mark)}map(t){let e=t.mapResult(this.from,1),n=t.mapResult(this.to,-1);return e.deleted&&n.deleted||e.pos>=n.pos?null:new kt(e.pos,n.pos,this.mark)}merge(t){return t instanceof kt&&t.mark.eq(this.mark)&&this.from<=t.to&&this.to>=t.from?new kt(Math.min(this.from,t.from),Math.max(this.to,t.to),this.mark):null}toJSON(){return{stepType:"addMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(t,e){if("number"!=typeof e.from||"number"!=typeof e.to)throw new RangeError("Invalid input for AddMarkStep.fromJSON");return new kt(e.from,e.to,t.markFromJSON(e.mark))}}yt.jsonID("addMark",kt);class bt extends yt{constructor(t,e,n){super(),this.from=t,this.to=e,this.mark=n}apply(t){let e=t.slice(this.from,this.to),n=new h(vt(e.content,(t=>t.mark(this.mark.removeFromSet(t.marks))),t),e.openStart,e.openEnd);return wt.fromReplace(t,this.from,this.to,n)}invert(){return new kt(this.from,this.to,this.mark)}map(t){let e=t.mapResult(this.from,1),n=t.mapResult(this.to,-1);return e.deleted&&n.deleted||e.pos>=n.pos?null:new bt(e.pos,n.pos,this.mark)}merge(t){return t instanceof bt&&t.mark.eq(this.mark)&&this.from<=t.to&&this.to>=t.from?new bt(Math.min(this.from,t.from),Math.max(this.to,t.to),this.mark):null}toJSON(){return{stepType:"removeMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(t,e){if("number"!=typeof e.from||"number"!=typeof e.to)throw new RangeError("Invalid input for RemoveMarkStep.fromJSON");return new bt(e.from,e.to,t.markFromJSON(e.mark))}}yt.jsonID("removeMark",bt);class xt extends yt{constructor(t,e){super(),this.pos=t,this.mark=e}apply(t){let e=t.nodeAt(this.pos);if(!e)return wt.fail("No node at mark step's position");let n=e.type.create(e.attrs,null,this.mark.addToSet(e.marks));return wt.fromReplace(t,this.pos,this.pos+1,new h(o.from(n),0,e.isLeaf?0:1))}invert(t){let e=t.nodeAt(this.pos);if(e){let t=this.mark.addToSet(e.marks);if(t.length==e.marks.length){for(let n=0;n<e.marks.length;n++)if(!e.marks[n].isInSet(t))return new xt(this.pos,e.marks[n]);return new xt(this.pos,this.mark)}}return new St(this.pos,this.mark)}map(t){let e=t.mapResult(this.pos,1);return e.deletedAfter?null:new xt(e.pos,this.mark)}toJSON(){return{stepType:"addNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(t,e){if("number"!=typeof e.pos)throw new RangeError("Invalid input for AddNodeMarkStep.fromJSON");return new xt(e.pos,t.markFromJSON(e.mark))}}yt.jsonID("addNodeMark",xt);class St extends yt{constructor(t,e){super(),this.pos=t,this.mark=e}apply(t){let e=t.nodeAt(this.pos);if(!e)return wt.fail("No node at mark step's position");let n=e.type.create(e.attrs,null,this.mark.removeFromSet(e.marks));return wt.fromReplace(t,this.pos,this.pos+1,new h(o.from(n),0,e.isLeaf?0:1))}invert(t){let e=t.nodeAt(this.pos);return e&&this.mark.isInSet(e.marks)?new xt(this.pos,this.mark):this}map(t){let e=t.mapResult(this.pos,1);return e.deletedAfter?null:new St(e.pos,this.mark)}toJSON(){return{stepType:"removeNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(t,e){if("number"!=typeof e.pos)throw new RangeError("Invalid input for RemoveNodeMarkStep.fromJSON");return new St(e.pos,t.markFromJSON(e.mark))}}yt.jsonID("removeNodeMark",St);class Mt extends yt{constructor(t,e,n,r=!1){super(),this.from=t,this.to=e,this.slice=n,this.structure=r}apply(t){return this.structure&&Ot(t,this.from,this.to)?wt.fail("Structure replace would overwrite content"):wt.fromReplace(t,this.from,this.to,this.slice)}getMap(){return new ft([this.from,this.to-this.from,this.slice.size])}invert(t){return new Mt(this.from,this.from+this.slice.size,t.slice(this.from,this.to))}map(t){let e=t.mapResult(this.from,1),n=t.mapResult(this.to,-1);return e.deletedAcross&&n.deletedAcross?null:new Mt(e.pos,Math.max(e.pos,n.pos),this.slice)}merge(t){if(!(t instanceof Mt)||t.structure||this.structure)return null;if(this.from+this.slice.size!=t.from||this.slice.openEnd||t.slice.openStart){if(t.to!=this.from||this.slice.openStart||t.slice.openEnd)return null;{let e=this.slice.size+t.slice.size==0?h.empty:new h(t.slice.content.append(this.slice.content),t.slice.openStart,this.slice.openEnd);return new Mt(t.from,this.to,e,this.structure)}}{let e=this.slice.size+t.slice.size==0?h.empty:new h(this.slice.content.append(t.slice.content),this.slice.openStart,t.slice.openEnd);return new Mt(this.from,this.to+(t.to-t.from),e,this.structure)}}toJSON(){let t={stepType:"replace",from:this.from,to:this.to};return this.slice.size&&(t.slice=this.slice.toJSON()),this.structure&&(t.structure=!0),t}static fromJSON(t,e){if("number"!=typeof e.from||"number"!=typeof e.to)throw new RangeError("Invalid input for ReplaceStep.fromJSON");return new Mt(e.from,e.to,h.fromJSON(t,e.slice),!!e.structure)}}yt.jsonID("replace",Mt);class Ct extends yt{constructor(t,e,n,r,o,i,s=!1){super(),this.from=t,this.to=e,this.gapFrom=n,this.gapTo=r,this.slice=o,this.insert=i,this.structure=s}apply(t){if(this.structure&&(Ot(t,this.from,this.gapFrom)||Ot(t,this.gapTo,this.to)))return wt.fail("Structure gap-replace would overwrite content");let e=t.slice(this.gapFrom,this.gapTo);if(e.openStart||e.openEnd)return wt.fail("Gap is not a flat range");let n=this.slice.insertAt(this.insert,e.content);return n?wt.fromReplace(t,this.from,this.to,n):wt.fail("Content does not fit in gap")}getMap(){return new ft([this.from,this.gapFrom-this.from,this.insert,this.gapTo,this.to-this.gapTo,this.slice.size-this.insert])}invert(t){let e=this.gapTo-this.gapFrom;return new Ct(this.from,this.from+this.slice.size+e,this.from+this.insert,this.from+this.insert+e,t.slice(this.from,this.to).removeBetween(this.gapFrom-this.from,this.gapTo-this.from),this.gapFrom-this.from,this.structure)}map(t){let e=t.mapResult(this.from,1),n=t.mapResult(this.to,-1),r=this.from==this.gapFrom?e.pos:t.map(this.gapFrom,-1),o=this.to==this.gapTo?n.pos:t.map(this.gapTo,1);return e.deletedAcross&&n.deletedAcross||r<e.pos||o>n.pos?null:new Ct(e.pos,n.pos,r,o,this.slice,this.insert,this.structure)}toJSON(){let t={stepType:"replaceAround",from:this.from,to:this.to,gapFrom:this.gapFrom,gapTo:this.gapTo,insert:this.insert};return this.slice.size&&(t.slice=this.slice.toJSON()),this.structure&&(t.structure=!0),t}static fromJSON(t,e){if("number"!=typeof e.from||"number"!=typeof e.to||"number"!=typeof e.gapFrom||"number"!=typeof e.gapTo||"number"!=typeof e.insert)throw new RangeError("Invalid input for ReplaceAroundStep.fromJSON");return new Ct(e.from,e.to,e.gapFrom,e.gapTo,h.fromJSON(t,e.slice),e.insert,!!e.structure)}}function Ot(t,e,n){let r=t.resolve(e),o=n-e,i=r.depth;for(;o>0&&i>0&&r.indexAfter(i)==r.node(i).childCount;)i--,o--;if(o>0){let t=r.node(i).maybeChild(r.indexAfter(i));for(;o>0;){if(!t||t.isLeaf)return!0;t=t.firstChild,o--}}return!1}function Tt(t,e,n,r=n.contentMatch,i=!0){let s=t.doc.nodeAt(e),l=[],a=e+1;for(let e=0;e<s.childCount;e++){let c=s.child(e),d=a+c.nodeSize,p=r.matchType(c.type);if(p){r=p;for(let e=0;e<c.marks.length;e++)n.allowsMarkType(c.marks[e].type)||t.step(new bt(a,d,c.marks[e]));if(i&&c.isText&&"pre"!=n.whitespace){let t,e,r=/\r?\n|\r/g;for(;t=r.exec(c.text);)e||(e=new h(o.from(n.schema.text(" ",n.allowedMarks(c.marks))),0,0)),l.push(new Mt(a+t.index,a+t.index+t[0].length,e))}}else l.push(new Mt(a,d,h.empty));a=d}if(!r.validEnd){let e=r.fillBefore(o.empty,!0);t.replace(a,a,new h(e,0,0))}for(let e=l.length-1;e>=0;e--)t.step(l[e])}function Et(t,e,n){return(0==e||t.canReplace(e,t.childCount))&&(n==t.childCount||t.canReplace(0,n))}function Nt(t){let e=t.parent.content.cutByIndex(t.startIndex,t.endIndex);for(let n=t.depth;;--n){let r=t.$from.node(n),o=t.$from.index(n),i=t.$to.indexAfter(n);if(n<t.depth&&r.canReplace(o,i,e))return n;if(0==n||r.type.spec.isolating||!Et(r,o,i))break}return null}function At(t,e,n=null,r=t){let o=function(t,e){let{parent:n,startIndex:r,endIndex:o}=t,i=n.contentMatchAt(r).findWrapping(e);if(!i)return null;let s=i.length?i[0]:e;return n.canReplaceWith(r,o,s)?i:null}(t,e),i=o&&function(t,e){let{parent:n,startIndex:r,endIndex:o}=t,i=n.child(r),s=e.contentMatch.findWrapping(i.type);if(!s)return null;let l=(s.length?s[s.length-1]:e).contentMatch;for(let t=r;l&&t<o;t++)l=l.matchType(n.child(t).type);return l&&l.validEnd?s:null}(r,e);return i?o.map(Dt).concat({type:e,attrs:n}).concat(i.map(Dt)):null}function Dt(t){return{type:t,attrs:null}}function Rt(t,e,n,r){e.forEach(((o,i)=>{if(o.isText){let s,l=/\r?\n|\r/g;for(;s=l.exec(o.text);){let o=t.mapping.slice(r).map(n+1+i+s.index);t.replaceWith(o,o+1,e.type.schema.linebreakReplacement.create())}}}))}function It(t,e,n,r){e.forEach(((o,i)=>{if(o.type==o.type.schema.linebreakReplacement){let o=t.mapping.slice(r).map(n+1+i);t.replaceWith(o,o+1,e.type.schema.text("\n"))}}))}function Pt(t,e,n=1,r){let o=t.resolve(e),i=o.depth-n,s=r&&r[r.length-1]||o.parent;if(i<0||o.parent.type.spec.isolating||!o.parent.canReplace(o.index(),o.parent.childCount)||!s.type.validContent(o.parent.content.cutByIndex(o.index(),o.parent.childCount)))return!1;for(let t=o.depth-1,e=n-2;t>i;t--,e--){let n=o.node(t),i=o.index(t);if(n.type.spec.isolating)return!1;let s=n.content.cutByIndex(i,n.childCount),l=r&&r[e+1];l&&(s=s.replaceChild(0,l.type.create(l.attrs)));let a=r&&r[e]||n;if(!n.canReplace(i+1,n.childCount)||!a.type.validContent(s))return!1}let l=o.indexAfter(i),a=r&&r[0];return o.node(i).canReplaceWith(l,l,a?a.type:o.node(i+1).type)}function zt(t,e){let n=t.resolve(e),r=n.index();return Lt(n.nodeBefore,n.nodeAfter)&&n.parent.canReplace(r,r+1)}function Lt(t,e){return!(!t||!e||t.isLeaf||!function(t,e){e.content.size||t.type.compatibleContent(e.type);let n=t.contentMatchAt(t.childCount),{linebreakReplacement:r}=t.type.schema;for(let o=0;o<e.childCount;o++){let i=e.child(o),s=i.type==r?t.type.schema.nodes.text:i.type;if(n=n.matchType(s),!n)return!1;if(!t.type.allowsMarks(i.marks))return!1}return n.validEnd}(t,e))}function $t(t,e,n=-1){let r=t.resolve(e);for(let t=r.depth;;t--){let o,i,s=r.index(t);if(t==r.depth?(o=r.nodeBefore,i=r.nodeAfter):n>0?(o=r.node(t+1),s++,i=r.node(t).maybeChild(s)):(o=r.node(t).maybeChild(s-1),i=r.node(t+1)),o&&!o.isTextblock&&Lt(o,i)&&r.node(t).canReplace(s,s+1))return e;if(0==t)break;e=n<0?r.before(t):r.after(t)}}function Bt(t,e,n=e,r=h.empty){if(e==n&&!r.size)return null;let o=t.resolve(e),i=t.resolve(n);return Ft(o,i,r)?new Mt(e,n,r):new Vt(o,i,r).fit()}function Ft(t,e,n){return!n.openStart&&!n.openEnd&&t.start()==e.start()&&t.parent.canReplace(t.index(),e.index(),n.content)}yt.jsonID("replaceAround",Ct);class Vt{constructor(t,e,n){this.$from=t,this.$to=e,this.unplaced=n,this.frontier=[],this.placed=o.empty;for(let e=0;e<=t.depth;e++){let n=t.node(e);this.frontier.push({type:n.type,match:n.contentMatchAt(t.indexAfter(e))})}for(let e=t.depth;e>0;e--)this.placed=o.from(t.node(e).copy(this.placed))}get depth(){return this.frontier.length-1}fit(){for(;this.unplaced.size;){let t=this.findFittable();t?this.placeNodes(t):this.openMore()||this.dropNode()}let t=this.mustMoveInline(),e=this.placed.size-this.depth-this.$from.depth,n=this.$from,r=this.close(t<0?this.$to:n.doc.resolve(t));if(!r)return null;let o=this.placed,i=n.depth,s=r.depth;for(;i&&s&&1==o.childCount;)o=o.firstChild.content,i--,s--;let l=new h(o,i,s);return t>-1?new Ct(n.pos,t,this.$to.pos,this.$to.end(),l,e):l.size||n.pos!=this.$to.pos?new Mt(n.pos,r.pos,l):null}findFittable(){let t=this.unplaced.openStart;for(let e=this.unplaced.content,n=0,r=this.unplaced.openEnd;n<t;n++){let o=e.firstChild;if(e.childCount>1&&(r=0),o.type.spec.isolating&&r<=n){t=n;break}e=o.content}for(let e=1;e<=2;e++)for(let n=1==e?t:this.unplaced.openStart;n>=0;n--){let t,r=null;n?(r=Wt(this.unplaced.content,n-1).firstChild,t=r.content):t=this.unplaced.content;let i=t.firstChild;for(let t=this.depth;t>=0;t--){let s,{type:l,match:a}=this.frontier[t],c=null;if(1==e&&(i?a.matchType(i.type)||(c=a.fillBefore(o.from(i),!1)):r&&l.compatibleContent(r.type)))return{sliceDepth:n,frontierDepth:t,parent:r,inject:c};if(2==e&&i&&(s=a.findWrapping(i.type)))return{sliceDepth:n,frontierDepth:t,parent:r,wrap:s};if(r&&a.matchType(r.type))break}}}openMore(){let{content:t,openStart:e,openEnd:n}=this.unplaced,r=Wt(t,e);return!(!r.childCount||r.firstChild.isLeaf)&&(this.unplaced=new h(t,e+1,Math.max(n,r.size+e>=t.size-n?e+1:0)),!0)}dropNode(){let{content:t,openStart:e,openEnd:n}=this.unplaced,r=Wt(t,e);if(r.childCount<=1&&e>0){let o=t.size-e<=e+r.size;this.unplaced=new h(jt(t,e-1,1),e-1,o?e-1:n)}else this.unplaced=new h(jt(t,e,1),e,n)}placeNodes({sliceDepth:t,frontierDepth:e,parent:n,inject:r,wrap:i}){for(;this.depth>e;)this.closeFrontierNode();if(i)for(let t=0;t<i.length;t++)this.openFrontierNode(i[t]);let s=this.unplaced,l=n?n.content:s.content,a=s.openStart-t,c=0,d=[],{match:p,type:u}=this.frontier[e];if(r){for(let t=0;t<r.childCount;t++)d.push(r.child(t));p=p.matchFragment(r)}let f=l.size+t-(s.content.size-s.openEnd);for(;c<l.childCount;){let t=l.child(c),e=p.matchType(t.type);if(!e)break;c++,(c>1||0==a||t.content.size)&&(p=e,d.push(qt(t.mark(u.allowedMarks(t.marks)),1==c?a:0,c==l.childCount?f:-1)))}let m=c==l.childCount;m||(f=-1),this.placed=Ht(this.placed,e,o.from(d)),this.frontier[e].match=p,m&&f<0&&n&&n.type==this.frontier[this.depth].type&&this.frontier.length>1&&this.closeFrontierNode();for(let t=0,e=l;t<f;t++){let t=e.lastChild;this.frontier.push({type:t.type,match:t.contentMatchAt(t.childCount)}),e=t.content}this.unplaced=m?0==t?h.empty:new h(jt(s.content,t-1,1),t-1,f<0?s.openEnd:t-1):new h(jt(s.content,t,c),s.openStart,s.openEnd)}mustMoveInline(){if(!this.$to.parent.isTextblock)return-1;let t,e=this.frontier[this.depth];if(!e.type.isTextblock||!Jt(this.$to,this.$to.depth,e.type,e.match,!1)||this.$to.depth==this.depth&&(t=this.findCloseLevel(this.$to))&&t.depth==this.depth)return-1;let{depth:n}=this.$to,r=this.$to.after(n);for(;n>1&&r==this.$to.end(--n);)++r;return r}findCloseLevel(t){t:for(let e=Math.min(this.depth,t.depth);e>=0;e--){let{match:n,type:r}=this.frontier[e],o=e<t.depth&&t.end(e+1)==t.pos+(t.depth-(e+1)),i=Jt(t,e,r,n,o);if(i){for(let n=e-1;n>=0;n--){let{match:e,type:r}=this.frontier[n],o=Jt(t,n,r,e,!0);if(!o||o.childCount)continue t}return{depth:e,fit:i,move:o?t.doc.resolve(t.after(e+1)):t}}}}close(t){let e=this.findCloseLevel(t);if(!e)return null;for(;this.depth>e.depth;)this.closeFrontierNode();e.fit.childCount&&(this.placed=Ht(this.placed,e.depth,e.fit)),t=e.move;for(let n=e.depth+1;n<=t.depth;n++){let e=t.node(n),r=e.type.contentMatch.fillBefore(e.content,!0,t.index(n));this.openFrontierNode(e.type,e.attrs,r)}return t}openFrontierNode(t,e=null,n){let r=this.frontier[this.depth];r.match=r.match.matchType(t),this.placed=Ht(this.placed,this.depth,o.from(t.create(e,n))),this.frontier.push({type:t,match:t.contentMatch})}closeFrontierNode(){let t=this.frontier.pop().match.fillBefore(o.empty,!0);t.childCount&&(this.placed=Ht(this.placed,this.frontier.length,t))}}function jt(t,e,n){return 0==e?t.cutByIndex(n,t.childCount):t.replaceChild(0,t.firstChild.copy(jt(t.firstChild.content,e-1,n)))}function Ht(t,e,n){return 0==e?t.append(n):t.replaceChild(t.childCount-1,t.lastChild.copy(Ht(t.lastChild.content,e-1,n)))}function Wt(t,e){for(let n=0;n<e;n++)t=t.firstChild.content;return t}function qt(t,e,n){if(e<=0)return t;let r=t.content;return e>1&&(r=r.replaceChild(0,qt(r.firstChild,e-1,1==r.childCount?n-1:0))),e>0&&(r=t.type.contentMatch.fillBefore(r).append(r),n<=0&&(r=r.append(t.type.contentMatch.matchFragment(r).fillBefore(o.empty,!0)))),t.copy(r)}function Jt(t,e,n,r,o){let i=t.node(e),s=o?t.indexAfter(e):t.index(e);if(s==i.childCount&&!n.compatibleContent(i.type))return null;let l=r.fillBefore(i.content,!0,s);return l&&!function(t,e,n){for(let r=n;r<e.childCount;r++)if(!t.allowsMarks(e.child(r).marks))return!0;return!1}(n,i.content,s)?l:null}function Kt(t,e,n,r,i){if(e<n){let o=t.firstChild;t=t.replaceChild(0,o.copy(Kt(o.content,e+1,n,r,o)))}if(e>r){let e=i.contentMatchAt(0),n=e.fillBefore(t).append(t);t=n.append(e.matchFragment(n).fillBefore(o.empty,!0))}return t}function _t(t,e){let n=[];for(let r=Math.min(t.depth,e.depth);r>=0;r--){let o=t.start(r);if(o<t.pos-(t.depth-r)||e.end(r)>e.pos+(e.depth-r)||t.node(r).type.spec.isolating||e.node(r).type.spec.isolating)break;(o==e.start(r)||r==t.depth&&r==e.depth&&t.parent.inlineContent&&e.parent.inlineContent&&r&&e.start(r-1)==o-1)&&n.push(r)}return n}class Ut extends yt{constructor(t,e,n){super(),this.pos=t,this.attr=e,this.value=n}apply(t){let e=t.nodeAt(this.pos);if(!e)return wt.fail("No node at attribute step's position");let n=Object.create(null);for(let t in e.attrs)n[t]=e.attrs[t];n[this.attr]=this.value;let r=e.type.create(n,null,e.marks);return wt.fromReplace(t,this.pos,this.pos+1,new h(o.from(r),0,e.isLeaf?0:1))}getMap(){return ft.empty}invert(t){return new Ut(this.pos,this.attr,t.nodeAt(this.pos).attrs[this.attr])}map(t){let e=t.mapResult(this.pos,1);return e.deletedAfter?null:new Ut(e.pos,this.attr,this.value)}toJSON(){return{stepType:"attr",pos:this.pos,attr:this.attr,value:this.value}}static fromJSON(t,e){if("number"!=typeof e.pos||"string"!=typeof e.attr)throw new RangeError("Invalid input for AttrStep.fromJSON");return new Ut(e.pos,e.attr,e.value)}}yt.jsonID("attr",Ut);class Gt extends yt{constructor(t,e){super(),this.attr=t,this.value=e}apply(t){let e=Object.create(null);for(let n in t.attrs)e[n]=t.attrs[n];e[this.attr]=this.value;let n=t.type.create(e,t.content,t.marks);return wt.ok(n)}getMap(){return ft.empty}invert(t){return new Gt(this.attr,t.attrs[this.attr])}map(t){return this}toJSON(){return{stepType:"docAttr",attr:this.attr,value:this.value}}static fromJSON(t,e){if("string"!=typeof e.attr)throw new RangeError("Invalid input for DocAttrStep.fromJSON");return new Gt(e.attr,e.value)}}yt.jsonID("docAttr",Gt);let Qt=class extends Error{};Qt=function t(e){let n=Error.call(this,e);return n.__proto__=t.prototype,n},(Qt.prototype=Object.create(Error.prototype)).constructor=Qt,Qt.prototype.name="TransformError";class Yt{constructor(t){this.doc=t,this.steps=[],this.docs=[],this.mapping=new mt}get before(){return this.docs.length?this.docs[0]:this.doc}step(t){let e=this.maybeStep(t);if(e.failed)throw new Qt(e.failed);return this}maybeStep(t){let e=t.apply(this.doc);return e.failed||this.addStep(t,e.doc),e}get docChanged(){return this.steps.length>0}addStep(t,e){this.docs.push(this.doc),this.steps.push(t),this.mapping.appendMap(t.getMap()),this.doc=e}replace(t,e=t,n=h.empty){let r=Bt(this.doc,t,e,n);return r&&this.step(r),this}replaceWith(t,e,n){return this.replace(t,e,new h(o.from(n),0,0))}delete(t,e){return this.replace(t,e,h.empty)}insert(t,e){return this.replaceWith(t,t,e)}replaceRange(t,e,n){return function(t,e,n,r){if(!r.size)return t.deleteRange(e,n);let o=t.doc.resolve(e),i=t.doc.resolve(n);if(Ft(o,i,r))return t.step(new Mt(e,n,r));let s=_t(o,t.doc.resolve(n));0==s[s.length-1]&&s.pop();let l=-(o.depth+1);s.unshift(l);for(let t=o.depth,e=o.pos-1;t>0;t--,e--){let n=o.node(t).type.spec;if(n.defining||n.definingAsContext||n.isolating)break;s.indexOf(t)>-1?l=t:o.before(t)==e&&s.splice(1,0,-t)}let a=s.indexOf(l),c=[],d=r.openStart;for(let t=r.content,e=0;;e++){let n=t.firstChild;if(c.push(n),e==r.openStart)break;t=n.content}for(let t=d-1;t>=0;t--){let e=c[t],n=(p=e.type).spec.defining||p.spec.definingForContent;if(n&&!e.sameMarkup(o.node(Math.abs(l)-1)))d=t;else if(n||!e.type.isTextblock)break}var p;for(let e=r.openStart;e>=0;e--){let l=(e+d+1)%(r.openStart+1),p=c[l];if(p)for(let e=0;e<s.length;e++){let c=s[(e+a)%s.length],d=!0;c<0&&(d=!1,c=-c);let u=o.node(c-1),f=o.index(c-1);if(u.canReplaceWith(f,f,p.type,p.marks))return t.replace(o.before(c),d?i.after(c):n,new h(Kt(r.content,0,r.openStart,l),l,r.openEnd))}}let u=t.steps.length;for(let l=s.length-1;l>=0&&(t.replace(e,n,r),!(t.steps.length>u));l--){let t=s[l];t<0||(e=o.before(t),n=i.after(t))}}(this,t,e,n),this}replaceRangeWith(t,e,n){return function(t,e,n,r){if(!r.isInline&&e==n&&t.doc.resolve(e).parent.content.size){let o=function(t,e,n){let r=t.resolve(e);if(r.parent.canReplaceWith(r.index(),r.index(),n))return e;if(0==r.parentOffset)for(let t=r.depth-1;t>=0;t--){let e=r.index(t);if(r.node(t).canReplaceWith(e,e,n))return r.before(t+1);if(e>0)return null}if(r.parentOffset==r.parent.content.size)for(let t=r.depth-1;t>=0;t--){let e=r.indexAfter(t);if(r.node(t).canReplaceWith(e,e,n))return r.after(t+1);if(e<r.node(t).childCount)return null}return null}(t.doc,e,r.type);null!=o&&(e=n=o)}t.replaceRange(e,n,new h(o.from(r),0,0))}(this,t,e,n),this}deleteRange(t,e){return function(t,e,n){let r=t.doc.resolve(e),o=t.doc.resolve(n),i=_t(r,o);for(let e=0;e<i.length;e++){let n=i[e],s=e==i.length-1;if(s&&0==n||r.node(n).type.contentMatch.validEnd)return t.delete(r.start(n),o.end(n));if(n>0&&(s||r.node(n-1).canReplace(r.index(n-1),o.indexAfter(n-1))))return t.delete(r.before(n),o.after(n))}for(let i=1;i<=r.depth&&i<=o.depth;i++)if(e-r.start(i)==r.depth-i&&n>r.end(i)&&o.end(i)-n!=o.depth-i&&r.start(i-1)==o.start(i-1)&&r.node(i-1).canReplace(r.index(i-1),o.index(i-1)))return t.delete(r.before(i),n);t.delete(e,n)}(this,t,e),this}lift(t,e){return function(t,e,n){let{$from:r,$to:i,depth:s}=e,l=r.before(s+1),a=i.after(s+1),c=l,d=a,p=o.empty,u=0;for(let t=s,e=!1;t>n;t--)e||r.index(t)>0?(e=!0,p=o.from(r.node(t).copy(p)),u++):c--;let f=o.empty,m=0;for(let t=s,e=!1;t>n;t--)e||i.after(t+1)<i.end(t)?(e=!0,f=o.from(i.node(t).copy(f)),m++):d++;t.step(new Ct(c,d,l,a,new h(p.append(f),u,m),p.size-u,!0))}(this,t,e),this}join(t,e=1){return function(t,e,n){let r=null,{linebreakReplacement:o}=t.doc.type.schema,i=t.doc.resolve(e-n),s=i.node().type;if(o&&s.inlineContent){let t="pre"==s.whitespace,e=!!s.contentMatch.matchType(o);t&&!e?r=!1:!t&&e&&(r=!0)}let l=t.steps.length;if(!1===r){let r=t.doc.resolve(e+n);It(t,r.node(),r.before(),l)}s.inlineContent&&Tt(t,e+n-1,s,i.node().contentMatchAt(i.index()),null==r);let a=t.mapping.slice(l),c=a.map(e-n);if(t.step(new Mt(c,a.map(e+n,-1),h.empty,!0)),!0===r){let e=t.doc.resolve(c);Rt(t,e.node(),e.before(),t.steps.length)}}(this,t,e),this}wrap(t,e){return function(t,e,n){let r=o.empty;for(let t=n.length-1;t>=0;t--){if(r.size){let e=n[t].type.contentMatch.matchFragment(r);if(!e||!e.validEnd)throw new RangeError("Wrapper type given to Transform.wrap does not form valid content of its parent wrapper")}r=o.from(n[t].type.create(n[t].attrs,r))}let i=e.start,s=e.end;t.step(new Ct(i,s,i,s,new h(r,0,0),n.length,!0))}(this,t,e),this}setBlockType(t,e=t,n,r=null){return function(t,e,n,r,i){if(!r.isTextblock)throw new RangeError("Type given to setBlockType should be a textblock");let s=t.steps.length;t.doc.nodesBetween(e,n,((e,n)=>{let l="function"==typeof i?i(e):i;if(e.isTextblock&&!e.hasMarkup(r,l)&&function(t,e,n){let r=t.resolve(e),o=r.index();return r.parent.canReplaceWith(o,o+1,n)}(t.doc,t.mapping.slice(s).map(n),r)){let i=null;if(r.schema.linebreakReplacement){let t="pre"==r.whitespace,e=!!r.contentMatch.matchType(r.schema.linebreakReplacement);t&&!e?i=!1:!t&&e&&(i=!0)}!1===i&&It(t,e,n,s),Tt(t,t.mapping.slice(s).map(n,1),r,void 0,null===i);let a=t.mapping.slice(s),c=a.map(n,1),d=a.map(n+e.nodeSize,1);return t.step(new Ct(c,d,c+1,d-1,new h(o.from(r.create(l,null,e.marks)),0,0),1,!0)),!0===i&&Rt(t,e,n,s),!1}}))}(this,t,e,n,r),this}setNodeMarkup(t,e,n=null,r){return function(t,e,n,r,i){let s=t.doc.nodeAt(e);if(!s)throw new RangeError("No node at given position");n||(n=s.type);let l=n.create(r,null,i||s.marks);if(s.isLeaf)return t.replaceWith(e,e+s.nodeSize,l);if(!n.validContent(s.content))throw new RangeError("Invalid content for node type "+n.name);t.step(new Ct(e,e+s.nodeSize,e+1,e+s.nodeSize-1,new h(o.from(l),0,0),1,!0))}(this,t,e,n,r),this}setNodeAttribute(t,e,n){return this.step(new Ut(t,e,n)),this}setDocAttribute(t,e){return this.step(new Gt(t,e)),this}addNodeMark(t,e){return this.step(new xt(t,e)),this}removeNodeMark(t,e){if(!(e instanceof a)){let n=this.doc.nodeAt(t);if(!n)throw new RangeError("No node at position "+t);if(!(e=e.isInSet(n.marks)))return this}return this.step(new St(t,e)),this}split(t,e=1,n){return function(t,e,n=1,r){let i=t.doc.resolve(e),s=o.empty,l=o.empty;for(let t=i.depth,e=i.depth-n,a=n-1;t>e;t--,a--){s=o.from(i.node(t).copy(s));let e=r&&r[a];l=o.from(e?e.type.create(e.attrs,l):i.node(t).copy(l))}t.step(new Mt(e,e,new h(s.append(l),n,n),!0))}(this,t,e,n),this}addMark(t,e,n){return function(t,e,n,r){let o,i,s=[],l=[];t.doc.nodesBetween(e,n,((t,a,c)=>{if(!t.isInline)return;let h=t.marks;if(!r.isInSet(h)&&c.type.allowsMarkType(r.type)){let c=Math.max(a,e),d=Math.min(a+t.nodeSize,n),p=r.addToSet(h);for(let t=0;t<h.length;t++)h[t].isInSet(p)||(o&&o.to==c&&o.mark.eq(h[t])?o.to=d:s.push(o=new bt(c,d,h[t])));i&&i.to==c?i.to=d:l.push(i=new kt(c,d,r))}})),s.forEach((e=>t.step(e))),l.forEach((e=>t.step(e)))}(this,t,e,n),this}removeMark(t,e,n){return function(t,e,n,r){let o=[],i=0;t.doc.nodesBetween(e,n,((t,s)=>{if(!t.isInline)return;i++;let l=null;if(r instanceof K){let e,n=t.marks;for(;e=r.isInSet(n);)(l||(l=[])).push(e),n=e.removeFromSet(n)}else r?r.isInSet(t.marks)&&(l=[r]):l=t.marks;if(l&&l.length){let r=Math.min(s+t.nodeSize,n);for(let t=0;t<l.length;t++){let n,a=l[t];for(let t=0;t<o.length;t++){let e=o[t];e.step==i-1&&a.eq(o[t].style)&&(n=e)}n?(n.to=r,n.step=i):o.push({style:a,from:Math.max(s,e),to:r,step:i})}}})),o.forEach((e=>t.step(new bt(e.from,e.to,e.style))))}(this,t,e,n),this}clearIncompatible(t,e,n){return Tt(this,t,e,n),this}}const Xt=Object.create(null);class Zt{constructor(t,e,n){this.$anchor=t,this.$head=e,this.ranges=n||[new te(t.min(e),t.max(e))]}get anchor(){return this.$anchor.pos}get head(){return this.$head.pos}get from(){return this.$from.pos}get to(){return this.$to.pos}get $from(){return this.ranges[0].$from}get $to(){return this.ranges[0].$to}get empty(){let t=this.ranges;for(let e=0;e<t.length;e++)if(t[e].$from.pos!=t[e].$to.pos)return!1;return!0}content(){return this.$from.doc.slice(this.from,this.to,!0)}replace(t,e=h.empty){let n=e.content.lastChild,r=null;for(let t=0;t<e.openEnd;t++)r=n,n=n.lastChild;let o=t.steps.length,i=this.ranges;for(let s=0;s<i.length;s++){let{$from:l,$to:a}=i[s],c=t.mapping.slice(o);t.replaceRange(c.map(l.pos),c.map(a.pos),s?h.empty:e),0==s&&he(t,o,(n?n.isInline:r&&r.isTextblock)?-1:1)}}replaceWith(t,e){let n=t.steps.length,r=this.ranges;for(let o=0;o<r.length;o++){let{$from:i,$to:s}=r[o],l=t.mapping.slice(n),a=l.map(i.pos),c=l.map(s.pos);o?t.deleteRange(a,c):(t.replaceRangeWith(a,c,e),he(t,n,e.isInline?-1:1))}}static findFrom(t,e,n=!1){let r=t.parent.inlineContent?new re(t):ce(t.node(0),t.parent,t.pos,t.index(),e,n);if(r)return r;for(let r=t.depth-1;r>=0;r--){let o=e<0?ce(t.node(0),t.node(r),t.before(r+1),t.index(r),e,n):ce(t.node(0),t.node(r),t.after(r+1),t.index(r)+1,e,n);if(o)return o}return null}static near(t,e=1){return this.findFrom(t,e)||this.findFrom(t,-e)||new le(t.node(0))}static atStart(t){return ce(t,t,0,0,1)||new le(t)}static atEnd(t){return ce(t,t,t.content.size,t.childCount,-1)||new le(t)}static fromJSON(t,e){if(!e||!e.type)throw new RangeError("Invalid input for Selection.fromJSON");let n=Xt[e.type];if(!n)throw new RangeError(`No selection type ${e.type} defined`);return n.fromJSON(t,e)}static jsonID(t,e){if(t in Xt)throw new RangeError("Duplicate use of selection JSON ID "+t);return Xt[t]=e,e.prototype.jsonID=t,e}getBookmark(){return re.between(this.$anchor,this.$head).getBookmark()}}Zt.prototype.visible=!0;class te{constructor(t,e){this.$from=t,this.$to=e}}let ee=!1;function ne(t){ee||t.parent.inlineContent||(ee=!0,console.warn("TextSelection endpoint not pointing into a node with inline content ("+t.parent.type.name+")"))}class re extends Zt{constructor(t,e=t){ne(t),ne(e),super(t,e)}get $cursor(){return this.$anchor.pos==this.$head.pos?this.$head:null}map(t,e){let n=t.resolve(e.map(this.head));if(!n.parent.inlineContent)return Zt.near(n);let r=t.resolve(e.map(this.anchor));return new re(r.parent.inlineContent?r:n,n)}replace(t,e=h.empty){if(super.replace(t,e),e==h.empty){let e=this.$from.marksAcross(this.$to);e&&t.ensureMarks(e)}}eq(t){return t instanceof re&&t.anchor==this.anchor&&t.head==this.head}getBookmark(){return new oe(this.anchor,this.head)}toJSON(){return{type:"text",anchor:this.anchor,head:this.head}}static fromJSON(t,e){if("number"!=typeof e.anchor||"number"!=typeof e.head)throw new RangeError("Invalid input for TextSelection.fromJSON");return new re(t.resolve(e.anchor),t.resolve(e.head))}static create(t,e,n=e){let r=t.resolve(e);return new this(r,n==e?r:t.resolve(n))}static between(t,e,n){let r=t.pos-e.pos;if(n&&!r||(n=r>=0?1:-1),!e.parent.inlineContent){let t=Zt.findFrom(e,n,!0)||Zt.findFrom(e,-n,!0);if(!t)return Zt.near(e,n);e=t.$head}return t.parent.inlineContent||(0==r||(t=(Zt.findFrom(t,-n,!0)||Zt.findFrom(t,n,!0)).$anchor).pos<e.pos!=r<0)&&(t=e),new re(t,e)}}Zt.jsonID("text",re);class oe{constructor(t,e){this.anchor=t,this.head=e}map(t){return new oe(t.map(this.anchor),t.map(this.head))}resolve(t){return re.between(t.resolve(this.anchor),t.resolve(this.head))}}class ie extends Zt{constructor(t){let e=t.nodeAfter,n=t.node(0).resolve(t.pos+e.nodeSize);super(t,n),this.node=e}map(t,e){let{deleted:n,pos:r}=e.mapResult(this.anchor),o=t.resolve(r);return n?Zt.near(o):new ie(o)}content(){return new h(o.from(this.node),0,0)}eq(t){return t instanceof ie&&t.anchor==this.anchor}toJSON(){return{type:"node",anchor:this.anchor}}getBookmark(){return new se(this.anchor)}static fromJSON(t,e){if("number"!=typeof e.anchor)throw new RangeError("Invalid input for NodeSelection.fromJSON");return new ie(t.resolve(e.anchor))}static create(t,e){return new ie(t.resolve(e))}static isSelectable(t){return!t.isText&&!1!==t.type.spec.selectable}}ie.prototype.visible=!1,Zt.jsonID("node",ie);class se{constructor(t){this.anchor=t}map(t){let{deleted:e,pos:n}=t.mapResult(this.anchor);return e?new oe(n,n):new se(n)}resolve(t){let e=t.resolve(this.anchor),n=e.nodeAfter;return n&&ie.isSelectable(n)?new ie(e):Zt.near(e)}}class le extends Zt{constructor(t){super(t.resolve(0),t.resolve(t.content.size))}replace(t,e=h.empty){if(e==h.empty){t.delete(0,t.doc.content.size);let e=Zt.atStart(t.doc);e.eq(t.selection)||t.setSelection(e)}else super.replace(t,e)}toJSON(){return{type:"all"}}static fromJSON(t){return new le(t)}map(t){return new le(t)}eq(t){return t instanceof le}getBookmark(){return ae}}Zt.jsonID("all",le);const ae={map(){return this},resolve:t=>new le(t)};function ce(t,e,n,r,o,i=!1){if(e.inlineContent)return re.create(t,n);for(let s=r-(o>0?0:1);o>0?s<e.childCount:s>=0;s+=o){let r=e.child(s);if(r.isAtom){if(!i&&ie.isSelectable(r))return ie.create(t,n-(o<0?r.nodeSize:0))}else{let e=ce(t,r,n+o,o<0?r.childCount:0,o,i);if(e)return e}n+=r.nodeSize*o}return null}function he(t,e,n){let r=t.steps.length-1;if(r<e)return;let o,i=t.steps[r];(i instanceof Mt||i instanceof Ct)&&(t.mapping.maps[r].forEach(((t,e,n,r)=>{null==o&&(o=r)})),t.setSelection(Zt.near(t.doc.resolve(o),n)))}class de extends Yt{constructor(t){super(t.doc),this.curSelectionFor=0,this.updated=0,this.meta=Object.create(null),this.time=Date.now(),this.curSelection=t.selection,this.storedMarks=t.storedMarks}get selection(){return this.curSelectionFor<this.steps.length&&(this.curSelection=this.curSelection.map(this.doc,this.mapping.slice(this.curSelectionFor)),this.curSelectionFor=this.steps.length),this.curSelection}setSelection(t){if(t.$from.doc!=this.doc)throw new RangeError("Selection passed to setSelection must point at the current document");return this.curSelection=t,this.curSelectionFor=this.steps.length,this.updated=-3&this.updated|1,this.storedMarks=null,this}get selectionSet(){return(1&this.updated)>0}setStoredMarks(t){return this.storedMarks=t,this.updated|=2,this}ensureMarks(t){return a.sameSet(this.storedMarks||this.selection.$from.marks(),t)||this.setStoredMarks(t),this}addStoredMark(t){return this.ensureMarks(t.addToSet(this.storedMarks||this.selection.$head.marks()))}removeStoredMark(t){return this.ensureMarks(t.removeFromSet(this.storedMarks||this.selection.$head.marks()))}get storedMarksSet(){return(2&this.updated)>0}addStep(t,e){super.addStep(t,e),this.updated=-3&this.updated,this.storedMarks=null}setTime(t){return this.time=t,this}replaceSelection(t){return this.selection.replace(this,t),this}replaceSelectionWith(t,e=!0){let n=this.selection;return e&&(t=t.mark(this.storedMarks||(n.empty?n.$from.marks():n.$from.marksAcross(n.$to)||a.none))),n.replaceWith(this,t),this}deleteSelection(){return this.selection.replace(this),this}insertText(t,e,n){let r=this.doc.type.schema;if(null==e)return t?this.replaceSelectionWith(r.text(t),!0):this.deleteSelection();{if(null==n&&(n=e),n=null==n?e:n,!t)return this.deleteRange(e,n);let o=this.storedMarks;if(!o){let t=this.doc.resolve(e);o=n==e?t.marks():t.marksAcross(this.doc.resolve(n))}return this.replaceRangeWith(e,n,r.text(t,o)),this.selection.empty||this.setSelection(Zt.near(this.selection.$to)),this}}setMeta(t,e){return this.meta["string"==typeof t?t:t.key]=e,this}getMeta(t){return this.meta["string"==typeof t?t:t.key]}get isGeneric(){for(let t in this.meta)return!1;return!0}scrollIntoView(){return this.updated|=4,this}get scrolledIntoView(){return(4&this.updated)>0}}function pe(t,e){return e&&t?t.bind(e):t}class ue{constructor(t,e,n){this.name=t,this.init=pe(e.init,n),this.apply=pe(e.apply,n)}}const fe=[new ue("doc",{init:t=>t.doc||t.schema.topNodeType.createAndFill(),apply:t=>t.doc}),new ue("selection",{init:(t,e)=>t.selection||Zt.atStart(e.doc),apply:t=>t.selection}),new ue("storedMarks",{init:t=>t.storedMarks||null,apply:(t,e,n,r)=>r.selection.$cursor?t.storedMarks:null}),new ue("scrollToSelection",{init:()=>0,apply:(t,e)=>t.scrolledIntoView?e+1:e})];class me{constructor(t,e){this.schema=t,this.plugins=[],this.pluginsByKey=Object.create(null),this.fields=fe.slice(),e&&e.forEach((t=>{if(this.pluginsByKey[t.key])throw new RangeError("Adding different instances of a keyed plugin ("+t.key+")");this.plugins.push(t),this.pluginsByKey[t.key]=t,t.spec.state&&this.fields.push(new ue(t.key,t.spec.state,t))}))}}class ge{constructor(t){this.config=t}get schema(){return this.config.schema}get plugins(){return this.config.plugins}apply(t){return this.applyTransaction(t).state}filterTransaction(t,e=-1){for(let n=0;n<this.config.plugins.length;n++)if(n!=e){let e=this.config.plugins[n];if(e.spec.filterTransaction&&!e.spec.filterTransaction.call(e,t,this))return!1}return!0}applyTransaction(t){if(!this.filterTransaction(t))return{state:this,transactions:[]};let e=[t],n=this.applyInner(t),r=null;for(;;){let o=!1;for(let i=0;i<this.config.plugins.length;i++){let s=this.config.plugins[i];if(s.spec.appendTransaction){let l=r?r[i].n:0,a=r?r[i].state:this,c=l<e.length&&s.spec.appendTransaction.call(s,l?e.slice(l):e,a,n);if(c&&n.filterTransaction(c,i)){if(c.setMeta("appendedTransaction",t),!r){r=[];for(let t=0;t<this.config.plugins.length;t++)r.push(t<i?{state:n,n:e.length}:{state:this,n:0})}e.push(c),n=n.applyInner(c),o=!0}r&&(r[i]={state:n,n:e.length})}}if(!o)return{state:n,transactions:e}}}applyInner(t){if(!t.before.eq(this.doc))throw new RangeError("Applying a mismatched transaction");let e=new ge(this.config),n=this.config.fields;for(let r=0;r<n.length;r++){let o=n[r];e[o.name]=o.apply(t,this[o.name],this,e)}return e}get tr(){return new de(this)}static create(t){let e=new me(t.doc?t.doc.type.schema:t.schema,t.plugins),n=new ge(e);for(let r=0;r<e.fields.length;r++)n[e.fields[r].name]=e.fields[r].init(t,n);return n}reconfigure(t){let e=new me(this.schema,t.plugins),n=e.fields,r=new ge(e);for(let e=0;e<n.length;e++){let o=n[e].name;r[o]=this.hasOwnProperty(o)?this[o]:n[e].init(t,r)}return r}toJSON(t){let e={doc:this.doc.toJSON(),selection:this.selection.toJSON()};if(this.storedMarks&&(e.storedMarks=this.storedMarks.map((t=>t.toJSON()))),t&&"object"==typeof t)for(let n in t){if("doc"==n||"selection"==n)throw new RangeError("The JSON fields `doc` and `selection` are reserved");let r=t[n],o=r.spec.state;o&&o.toJSON&&(e[n]=o.toJSON.call(r,this[r.key]))}return e}static fromJSON(t,e,n){if(!e)throw new RangeError("Invalid input for EditorState.fromJSON");if(!t.schema)throw new RangeError("Required config field 'schema' missing");let r=new me(t.schema,t.plugins),o=new ge(r);return r.fields.forEach((r=>{if("doc"==r.name)o.doc=E.fromJSON(t.schema,e.doc);else if("selection"==r.name)o.selection=Zt.fromJSON(o.doc,e.selection);else if("storedMarks"==r.name)e.storedMarks&&(o.storedMarks=e.storedMarks.map(t.schema.markFromJSON));else{if(n)for(let i in n){let s=n[i],l=s.spec.state;if(s.key==r.name&&l&&l.fromJSON&&Object.prototype.hasOwnProperty.call(e,i))return void(o[r.name]=l.fromJSON.call(s,t,e[i],o))}o[r.name]=r.init(t,o)}})),o}}function ye(t,e,n){for(let r in t){let o=t[r];o instanceof Function?o=o.bind(e):"handleDOMEvents"==r&&(o=ye(o,e,{})),n[r]=o}return n}class we{constructor(t){this.spec=t,this.props={},t.props&&ye(t.props,this,this.props),this.key=t.key?t.key.key:ke("plugin")}getState(t){return t[this.key]}}const ve=Object.create(null);function ke(t){return t in ve?t+"$"+ ++ve[t]:(ve[t]=0,t+"$")}class be{constructor(t="key"){this.key=ke(t)}get(t){return t.config.pluginsByKey[this.key]}getState(t){return t[this.key]}}const xe=function(t){for(var e=0;;e++)if(!(t=t.previousSibling))return e},Se=function(t){let e=t.assignedSlot||t.parentNode;return e&&11==e.nodeType?e.host:e};let Me=null;const Ce=function(t,e,n){let r=Me||(Me=document.createRange());return r.setEnd(t,null==n?t.nodeValue.length:n),r.setStart(t,e||0),r},Oe=function(t,e,n,r){return n&&(Ee(t,e,n,r,-1)||Ee(t,e,n,r,1))},Te=/^(img|br|input|textarea|hr)$/i;function Ee(t,e,n,r,o){for(;;){if(t==n&&e==r)return!0;if(e==(o<0?0:Ne(t))){let n=t.parentNode;if(!n||1!=n.nodeType||Ae(t)||Te.test(t.nodeName)||"false"==t.contentEditable)return!1;e=xe(t)+(o<0?0:1),t=n}else{if(1!=t.nodeType)return!1;if("false"==(t=t.childNodes[e+(o<0?-1:0)]).contentEditable)return!1;e=o<0?Ne(t):0}}}function Ne(t){return 3==t.nodeType?t.nodeValue.length:t.childNodes.length}function Ae(t){let e;for(let n=t;n&&!(e=n.pmViewDesc);n=n.parentNode);return e&&e.node&&e.node.isBlock&&(e.dom==t||e.contentDOM==t)}const De=function(t){return t.focusNode&&Oe(t.focusNode,t.focusOffset,t.anchorNode,t.anchorOffset)};function Re(t,e){let n=document.createEvent("Event");return n.initEvent("keydown",!0,!0),n.keyCode=t,n.key=n.code=e,n}const Ie="undefined"!=typeof navigator?navigator:null,Pe="undefined"!=typeof document?document:null,ze=Ie&&Ie.userAgent||"",Le=/Edge\/(\d+)/.exec(ze),$e=/MSIE \d/.exec(ze),Be=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(ze),Fe=!!($e||Be||Le),Ve=$e?document.documentMode:Be?+Be[1]:Le?+Le[1]:0,je=!Fe&&/gecko\/(\d+)/i.test(ze);je&&(/Firefox\/(\d+)/.exec(ze)||[0,0])[1];const He=!Fe&&/Chrome\/(\d+)/.exec(ze),We=!!He,qe=He?+He[1]:0,Je=!Fe&&!!Ie&&/Apple Computer/.test(Ie.vendor),Ke=Je&&(/Mobile\/\w+/.test(ze)||!!Ie&&Ie.maxTouchPoints>2),_e=Ke||!!Ie&&/Mac/.test(Ie.platform),Ue=!!Ie&&/Win/.test(Ie.platform),Ge=/Android \d/.test(ze),Qe=!!Pe&&"webkitFontSmoothing"in Pe.documentElement.style,Ye=Qe?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0;function Xe(t){let e=t.defaultView&&t.defaultView.visualViewport;return e?{left:0,right:e.width,top:0,bottom:e.height}:{left:0,right:t.documentElement.clientWidth,top:0,bottom:t.documentElement.clientHeight}}function Ze(t,e){return"number"==typeof t?t:t[e]}function tn(t){let e=t.getBoundingClientRect(),n=e.width/t.offsetWidth||1,r=e.height/t.offsetHeight||1;return{left:e.left,right:e.left+t.clientWidth*n,top:e.top,bottom:e.top+t.clientHeight*r}}function en(t,e,n){let r=t.someProp("scrollThreshold")||0,o=t.someProp("scrollMargin")||5,i=t.dom.ownerDocument;for(let s=n||t.dom;s;s=Se(s)){if(1!=s.nodeType)continue;let t=s,n=t==i.body,l=n?Xe(i):tn(t),a=0,c=0;if(e.top<l.top+Ze(r,"top")?c=-(l.top-e.top+Ze(o,"top")):e.bottom>l.bottom-Ze(r,"bottom")&&(c=e.bottom-e.top>l.bottom-l.top?e.top+Ze(o,"top")-l.top:e.bottom-l.bottom+Ze(o,"bottom")),e.left<l.left+Ze(r,"left")?a=-(l.left-e.left+Ze(o,"left")):e.right>l.right-Ze(r,"right")&&(a=e.right-l.right+Ze(o,"right")),a||c)if(n)i.defaultView.scrollBy(a,c);else{let n=t.scrollLeft,r=t.scrollTop;c&&(t.scrollTop+=c),a&&(t.scrollLeft+=a);let o=t.scrollLeft-n,i=t.scrollTop-r;e={left:e.left-o,top:e.top-i,right:e.right-o,bottom:e.bottom-i}}if(n||/^(fixed|sticky)$/.test(getComputedStyle(s).position))break}}function nn(t){let e=[],n=t.ownerDocument;for(let r=t;r&&(e.push({dom:r,top:r.scrollTop,left:r.scrollLeft}),t!=n);r=Se(r));return e}function rn(t,e){for(let n=0;n<t.length;n++){let{dom:r,top:o,left:i}=t[n];r.scrollTop!=o+e&&(r.scrollTop=o+e),r.scrollLeft!=i&&(r.scrollLeft=i)}}let on=null;function sn(t,e){let n,r,o,i,s=2e8,l=0,a=e.top,c=e.top;for(let h=t.firstChild,d=0;h;h=h.nextSibling,d++){let t;if(1==h.nodeType)t=h.getClientRects();else{if(3!=h.nodeType)continue;t=Ce(h).getClientRects()}for(let p=0;p<t.length;p++){let u=t[p];if(u.top<=a&&u.bottom>=c){a=Math.max(u.bottom,a),c=Math.min(u.top,c);let t=u.left>e.left?u.left-e.left:u.right<e.left?e.left-u.right:0;if(t<s){n=h,s=t,r=t&&3==n.nodeType?{left:u.right<e.left?u.right:u.left,top:e.top}:e,1==h.nodeType&&t&&(l=d+(e.left>=(u.left+u.right)/2?1:0));continue}}else u.top>e.top&&!o&&u.left<=e.left&&u.right>=e.left&&(o=h,i={left:Math.max(u.left,Math.min(u.right,e.left)),top:u.top});!n&&(e.left>=u.right&&e.top>=u.top||e.left>=u.left&&e.top>=u.bottom)&&(l=d+1)}}return!n&&o&&(n=o,r=i,s=0),n&&3==n.nodeType?function(t,e){let n=t.nodeValue.length,r=document.createRange();for(let o=0;o<n;o++){r.setEnd(t,o+1),r.setStart(t,o);let n=dn(r,1);if(n.top!=n.bottom&&ln(e,n))return{node:t,offset:o+(e.left>=(n.left+n.right)/2?1:0)}}return{node:t,offset:0}}(n,r):!n||s&&1==n.nodeType?{node:t,offset:l}:sn(n,r)}function ln(t,e){return t.left>=e.left-1&&t.left<=e.right+1&&t.top>=e.top-1&&t.top<=e.bottom+1}function an(t,e,n){let r=t.childNodes.length;if(r&&n.top<n.bottom)for(let o=Math.max(0,Math.min(r-1,Math.floor(r*(e.top-n.top)/(n.bottom-n.top))-2)),i=o;;){let n=t.childNodes[i];if(1==n.nodeType){let t=n.getClientRects();for(let r=0;r<t.length;r++){let o=t[r];if(ln(e,o))return an(n,e,o)}}if((i=(i+1)%r)==o)break}return t}function cn(t,e){let n,r=t.dom.ownerDocument,o=0,i=function(t,e,n){if(t.caretPositionFromPoint)try{let r=t.caretPositionFromPoint(e,n);if(r)return{node:r.offsetNode,offset:Math.min(Ne(r.offsetNode),r.offset)}}catch(t){}if(t.caretRangeFromPoint){let r=t.caretRangeFromPoint(e,n);if(r)return{node:r.startContainer,offset:Math.min(Ne(r.startContainer),r.startOffset)}}}(r,e.left,e.top);i&&({node:n,offset:o}=i);let s,l=(t.root.elementFromPoint?t.root:r).elementFromPoint(e.left,e.top);if(!l||!t.dom.contains(1!=l.nodeType?l.parentNode:l)){let n=t.dom.getBoundingClientRect();if(!ln(e,n))return null;if(l=an(t.dom,e,n),!l)return null}if(Je)for(let t=l;n&&t;t=Se(t))t.draggable&&(n=void 0);if(l=function(t,e){let n=t.parentNode;return n&&/^li$/i.test(n.nodeName)&&e.left<t.getBoundingClientRect().left?n:t}(l,e),n){if(je&&1==n.nodeType&&(o=Math.min(o,n.childNodes.length),o<n.childNodes.length)){let t,r=n.childNodes[o];"IMG"==r.nodeName&&(t=r.getBoundingClientRect()).right<=e.left&&t.bottom>e.top&&o++}let r;Qe&&o&&1==n.nodeType&&1==(r=n.childNodes[o-1]).nodeType&&"false"==r.contentEditable&&r.getBoundingClientRect().top>=e.top&&o--,n==t.dom&&o==n.childNodes.length-1&&1==n.lastChild.nodeType&&e.top>n.lastChild.getBoundingClientRect().bottom?s=t.state.doc.content.size:0!=o&&1==n.nodeType&&"BR"==n.childNodes[o-1].nodeName||(s=function(t,e,n,r){let o=-1;for(let n=e,i=!1;n!=t.dom;){let e,s=t.docView.nearestDesc(n,!0);if(!s)return null;if(1==s.dom.nodeType&&(s.node.isBlock&&s.parent||!s.contentDOM)&&((e=s.dom.getBoundingClientRect()).width||e.height)&&(s.node.isBlock&&s.parent&&(!i&&e.left>r.left||e.top>r.top?o=s.posBefore:(!i&&e.right<r.left||e.bottom<r.top)&&(o=s.posAfter),i=!0),!s.contentDOM&&o<0&&!s.node.isText))return(s.node.isBlock?r.top<(e.top+e.bottom)/2:r.left<(e.left+e.right)/2)?s.posBefore:s.posAfter;n=s.dom.parentNode}return o>-1?o:t.docView.posFromDOM(e,n,-1)}(t,n,o,e))}null==s&&(s=function(t,e,n){let{node:r,offset:o}=sn(e,n),i=-1;if(1==r.nodeType&&!r.firstChild){let t=r.getBoundingClientRect();i=t.left!=t.right&&n.left>(t.left+t.right)/2?1:-1}return t.docView.posFromDOM(r,o,i)}(t,l,e));let a=t.docView.nearestDesc(l,!0);return{pos:s,inside:a?a.posAtStart-a.border:-1}}function hn(t){return t.top<t.bottom||t.left<t.right}function dn(t,e){let n=t.getClientRects();if(n.length){let t=n[e<0?0:n.length-1];if(hn(t))return t}return Array.prototype.find.call(n,hn)||t.getBoundingClientRect()}const pn=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/;function un(t,e,n){let{node:r,offset:o,atom:i}=t.docView.domFromPos(e,n<0?-1:1),s=Qe||je;if(3==r.nodeType){if(!s||!pn.test(r.nodeValue)&&(n<0?o:o!=r.nodeValue.length)){let t=o,e=o,i=n<0?1:-1;return n<0&&!o?(e++,i=-1):n>=0&&o==r.nodeValue.length?(t--,i=1):n<0?t--:e++,fn(dn(Ce(r,t,e),i),i<0)}{let t=dn(Ce(r,o,o),n);if(je&&o&&/\s/.test(r.nodeValue[o-1])&&o<r.nodeValue.length){let e=dn(Ce(r,o-1,o-1),-1);if(e.top==t.top){let n=dn(Ce(r,o,o+1),-1);if(n.top!=t.top)return fn(n,n.left<e.left)}}return t}}if(!t.state.doc.resolve(e-(i||0)).parent.inlineContent){if(null==i&&o&&(n<0||o==Ne(r))){let t=r.childNodes[o-1];if(1==t.nodeType)return mn(t.getBoundingClientRect(),!1)}if(null==i&&o<Ne(r)){let t=r.childNodes[o];if(1==t.nodeType)return mn(t.getBoundingClientRect(),!0)}return mn(r.getBoundingClientRect(),n>=0)}if(null==i&&o&&(n<0||o==Ne(r))){let t=r.childNodes[o-1],e=3==t.nodeType?Ce(t,Ne(t)-(s?0:1)):1!=t.nodeType||"BR"==t.nodeName&&t.nextSibling?null:t;if(e)return fn(dn(e,1),!1)}if(null==i&&o<Ne(r)){let t=r.childNodes[o];for(;t.pmViewDesc&&t.pmViewDesc.ignoreForCoords;)t=t.nextSibling;let e=t?3==t.nodeType?Ce(t,0,s?0:1):1==t.nodeType?t:null:null;if(e)return fn(dn(e,-1),!0)}return fn(dn(3==r.nodeType?Ce(r):r,-n),n>=0)}function fn(t,e){if(0==t.width)return t;let n=e?t.left:t.right;return{top:t.top,bottom:t.bottom,left:n,right:n}}function mn(t,e){if(0==t.height)return t;let n=e?t.top:t.bottom;return{top:n,bottom:n,left:t.left,right:t.right}}function gn(t,e,n){let r=t.state,o=t.root.activeElement;r!=e&&t.updateState(e),o!=t.dom&&t.focus();try{return n()}finally{r!=e&&t.updateState(r),o!=t.dom&&o&&o.focus()}}const yn=/[\u0590-\u08ac]/;let wn=null,vn=null,kn=!1;function bn(t,e,n){return wn==e&&vn==n?kn:(wn=e,vn=n,kn="up"==n||"down"==n?function(t,e,n){let r=e.selection,o="up"==n?r.$from:r.$to;return gn(t,e,(()=>{let{node:e}=t.docView.domFromPos(o.pos,"up"==n?-1:1);for(;;){let n=t.docView.nearestDesc(e,!0);if(!n)break;if(n.node.isBlock){e=n.contentDOM||n.dom;break}e=n.dom.parentNode}let r=un(t,o.pos,1);for(let t=e.firstChild;t;t=t.nextSibling){let e;if(1==t.nodeType)e=t.getClientRects();else{if(3!=t.nodeType)continue;e=Ce(t,0,t.nodeValue.length).getClientRects()}for(let t=0;t<e.length;t++){let o=e[t];if(o.bottom>o.top+1&&("up"==n?r.top-o.top>2*(o.bottom-r.top):o.bottom-r.bottom>2*(r.bottom-o.top)))return!1}}return!0}))}(t,e,n):function(t,e,n){let{$head:r}=e.selection;if(!r.parent.isTextblock)return!1;let o=r.parentOffset,i=!o,s=o==r.parent.content.size,l=t.domSelection();return l?yn.test(r.parent.textContent)&&l.modify?gn(t,e,(()=>{let{focusNode:e,focusOffset:o,anchorNode:i,anchorOffset:s}=t.domSelectionRange(),a=l.caretBidiLevel;l.modify("move",n,"character");let c=r.depth?t.docView.domAfterPos(r.before()):t.dom,{focusNode:h,focusOffset:d}=t.domSelectionRange(),p=h&&!c.contains(1==h.nodeType?h:h.parentNode)||e==h&&o==d;try{l.collapse(i,s),e&&(e!=i||o!=s)&&l.extend&&l.extend(e,o)}catch(t){}return null!=a&&(l.caretBidiLevel=a),p})):"left"==n||"backward"==n?i:s:r.pos==r.start()||r.pos==r.end()}(t,e,n))}class xn{constructor(t,e,n,r){this.parent=t,this.children=e,this.dom=n,this.contentDOM=r,this.dirty=0,n.pmViewDesc=this}matchesWidget(t){return!1}matchesMark(t){return!1}matchesNode(t,e,n){return!1}matchesHack(t){return!1}parseRule(){return null}stopEvent(t){return!1}get size(){let t=0;for(let e=0;e<this.children.length;e++)t+=this.children[e].size;return t}get border(){return 0}destroy(){this.parent=void 0,this.dom.pmViewDesc==this&&(this.dom.pmViewDesc=void 0);for(let t=0;t<this.children.length;t++)this.children[t].destroy()}posBeforeChild(t){for(let e=0,n=this.posAtStart;;e++){let r=this.children[e];if(r==t)return n;n+=r.size}}get posBefore(){return this.parent.posBeforeChild(this)}get posAtStart(){return this.parent?this.parent.posBeforeChild(this)+this.border:0}get posAfter(){return this.posBefore+this.size}get posAtEnd(){return this.posAtStart+this.size-2*this.border}localPosFromDOM(t,e,n){if(this.contentDOM&&this.contentDOM.contains(1==t.nodeType?t:t.parentNode)){if(n<0){let n,r;if(t==this.contentDOM)n=t.childNodes[e-1];else{for(;t.parentNode!=this.contentDOM;)t=t.parentNode;n=t.previousSibling}for(;n&&(!(r=n.pmViewDesc)||r.parent!=this);)n=n.previousSibling;return n?this.posBeforeChild(r)+r.size:this.posAtStart}{let n,r;if(t==this.contentDOM)n=t.childNodes[e];else{for(;t.parentNode!=this.contentDOM;)t=t.parentNode;n=t.nextSibling}for(;n&&(!(r=n.pmViewDesc)||r.parent!=this);)n=n.nextSibling;return n?this.posBeforeChild(r):this.posAtEnd}}let r;if(t==this.dom&&this.contentDOM)r=e>xe(this.contentDOM);else if(this.contentDOM&&this.contentDOM!=this.dom&&this.dom.contains(this.contentDOM))r=2&t.compareDocumentPosition(this.contentDOM);else if(this.dom.firstChild){if(0==e)for(let e=t;;e=e.parentNode){if(e==this.dom){r=!1;break}if(e.previousSibling)break}if(null==r&&e==t.childNodes.length)for(let e=t;;e=e.parentNode){if(e==this.dom){r=!0;break}if(e.nextSibling)break}}return(null==r?n>0:r)?this.posAtEnd:this.posAtStart}nearestDesc(t,e=!1){for(let n=!0,r=t;r;r=r.parentNode){let o,i=this.getDesc(r);if(i&&(!e||i.node)){if(!n||!(o=i.nodeDOM)||(1==o.nodeType?o.contains(1==t.nodeType?t:t.parentNode):o==t))return i;n=!1}}}getDesc(t){let e=t.pmViewDesc;for(let t=e;t;t=t.parent)if(t==this)return e}posFromDOM(t,e,n){for(let r=t;r;r=r.parentNode){let o=this.getDesc(r);if(o)return o.localPosFromDOM(t,e,n)}return-1}descAt(t){for(let e=0,n=0;e<this.children.length;e++){let r=this.children[e],o=n+r.size;if(n==t&&o!=n){for(;!r.border&&r.children.length;)r=r.children[0];return r}if(t<o)return r.descAt(t-n-r.border);n=o}}domFromPos(t,e){if(!this.contentDOM)return{node:this.dom,offset:0,atom:t+1};let n=0,r=0;for(let e=0;n<this.children.length;n++){let o=this.children[n],i=e+o.size;if(i>t||o instanceof Nn){r=t-e;break}e=i}if(r)return this.children[n].domFromPos(r-this.children[n].border,e);for(let t;n&&!(t=this.children[n-1]).size&&t instanceof Sn&&t.side>=0;n--);if(e<=0){let t,r=!0;for(;t=n?this.children[n-1]:null,t&&t.dom.parentNode!=this.contentDOM;n--,r=!1);return t&&e&&r&&!t.border&&!t.domAtom?t.domFromPos(t.size,e):{node:this.contentDOM,offset:t?xe(t.dom)+1:0}}{let t,r=!0;for(;t=n<this.children.length?this.children[n]:null,t&&t.dom.parentNode!=this.contentDOM;n++,r=!1);return t&&r&&!t.border&&!t.domAtom?t.domFromPos(0,e):{node:this.contentDOM,offset:t?xe(t.dom):this.contentDOM.childNodes.length}}}parseRange(t,e,n=0){if(0==this.children.length)return{node:this.contentDOM,from:t,to:e,fromOffset:0,toOffset:this.contentDOM.childNodes.length};let r=-1,o=-1;for(let i=n,s=0;;s++){let n=this.children[s],l=i+n.size;if(-1==r&&t<=l){let o=i+n.border;if(t>=o&&e<=l-n.border&&n.node&&n.contentDOM&&this.contentDOM.contains(n.contentDOM))return n.parseRange(t,e,o);t=i;for(let e=s;e>0;e--){let n=this.children[e-1];if(n.size&&n.dom.parentNode==this.contentDOM&&!n.emptyChildAt(1)){r=xe(n.dom)+1;break}t-=n.size}-1==r&&(r=0)}if(r>-1&&(l>e||s==this.children.length-1)){e=l;for(let t=s+1;t<this.children.length;t++){let n=this.children[t];if(n.size&&n.dom.parentNode==this.contentDOM&&!n.emptyChildAt(-1)){o=xe(n.dom);break}e+=n.size}-1==o&&(o=this.contentDOM.childNodes.length);break}i=l}return{node:this.contentDOM,from:t,to:e,fromOffset:r,toOffset:o}}emptyChildAt(t){if(this.border||!this.contentDOM||!this.children.length)return!1;let e=this.children[t<0?0:this.children.length-1];return 0==e.size||e.emptyChildAt(t)}domAfterPos(t){let{node:e,offset:n}=this.domFromPos(t,0);if(1!=e.nodeType||n==e.childNodes.length)throw new RangeError("No node after pos "+t);return e.childNodes[n]}setSelection(t,e,n,r=!1){let o=Math.min(t,e),i=Math.max(t,e);for(let s=0,l=0;s<this.children.length;s++){let a=this.children[s],c=l+a.size;if(o>l&&i<c)return a.setSelection(t-l-a.border,e-l-a.border,n,r);l=c}let s=this.domFromPos(t,t?-1:1),l=e==t?s:this.domFromPos(e,e?-1:1),a=n.root.getSelection(),c=n.domSelectionRange(),h=!1;if((je||Je)&&t==e){let{node:t,offset:e}=s;if(3==t.nodeType){if(h=!(!e||"\n"!=t.nodeValue[e-1]),h&&e==t.nodeValue.length)for(let e,n=t;n;n=n.parentNode){if(e=n.nextSibling){"BR"==e.nodeName&&(s=l={node:e.parentNode,offset:xe(e)+1});break}let t=n.pmViewDesc;if(t&&t.node&&t.node.isBlock)break}}else{let n=t.childNodes[e-1];h=n&&("BR"==n.nodeName||"false"==n.contentEditable)}}if(je&&c.focusNode&&c.focusNode!=l.node&&1==c.focusNode.nodeType){let t=c.focusNode.childNodes[c.focusOffset];t&&"false"==t.contentEditable&&(r=!0)}if(!(r||h&&Je)&&Oe(s.node,s.offset,c.anchorNode,c.anchorOffset)&&Oe(l.node,l.offset,c.focusNode,c.focusOffset))return;let d=!1;if((a.extend||t==e)&&!h){a.collapse(s.node,s.offset);try{t!=e&&a.extend(l.node,l.offset),d=!0}catch(t){}}if(!d){if(t>e){let t=s;s=l,l=t}let n=document.createRange();n.setEnd(l.node,l.offset),n.setStart(s.node,s.offset),a.removeAllRanges(),a.addRange(n)}}ignoreMutation(t){return!this.contentDOM&&"selection"!=t.type}get contentLost(){return this.contentDOM&&this.contentDOM!=this.dom&&!this.dom.contains(this.contentDOM)}markDirty(t,e){for(let n=0,r=0;r<this.children.length;r++){let o=this.children[r],i=n+o.size;if(n==i?t<=i&&e>=n:t<i&&e>n){let r=n+o.border,s=i-o.border;if(t>=r&&e<=s)return this.dirty=t==n||e==i?2:1,void(t!=r||e!=s||!o.contentLost&&o.dom.parentNode==this.contentDOM?o.markDirty(t-r,e-r):o.dirty=3);o.dirty=o.dom!=o.contentDOM||o.dom.parentNode!=this.contentDOM||o.children.length?3:2}n=i}this.dirty=2}markParentsDirty(){let t=1;for(let e=this.parent;e;e=e.parent,t++){let n=1==t?2:1;e.dirty<n&&(e.dirty=n)}}get domAtom(){return!1}get ignoreForCoords(){return!1}isText(t){return!1}}class Sn extends xn{constructor(t,e,n,r){let o,i=e.type.toDOM;if("function"==typeof i&&(i=i(n,(()=>o?o.parent?o.parent.posBeforeChild(o):void 0:r))),!e.type.spec.raw){if(1!=i.nodeType){let t=document.createElement("span");t.appendChild(i),i=t}i.contentEditable="false",i.classList.add("ProseMirror-widget")}super(t,[],i,null),this.widget=e,this.widget=e,o=this}matchesWidget(t){return 0==this.dirty&&t.type.eq(this.widget.type)}parseRule(){return{ignore:!0}}stopEvent(t){let e=this.widget.spec.stopEvent;return!!e&&e(t)}ignoreMutation(t){return"selection"!=t.type||this.widget.spec.ignoreSelection}destroy(){this.widget.type.destroy(this.dom),super.destroy()}get domAtom(){return!0}get side(){return this.widget.type.side}}class Mn extends xn{constructor(t,e,n,r){super(t,[],e,null),this.textDOM=n,this.text=r}get size(){return this.text.length}localPosFromDOM(t,e){return t!=this.textDOM?this.posAtStart+(e?this.size:0):this.posAtStart+e}domFromPos(t){return{node:this.textDOM,offset:t}}ignoreMutation(t){return"characterData"===t.type&&t.target.nodeValue==t.oldValue}}class Cn extends xn{constructor(t,e,n,r,o){super(t,[],n,r),this.mark=e,this.spec=o}static create(t,e,n,r){let o=r.nodeViews[e.type.name],i=o&&o(e,r,n);return i&&i.dom||(i=it.renderSpec(document,e.type.spec.toDOM(e,n),null,e.attrs)),new Cn(t,e,i.dom,i.contentDOM||i.dom,i)}parseRule(){return 3&this.dirty||this.mark.type.spec.reparseInView?null:{mark:this.mark.type.name,attrs:this.mark.attrs,contentElement:this.contentDOM}}matchesMark(t){return 3!=this.dirty&&this.mark.eq(t)}markDirty(t,e){if(super.markDirty(t,e),0!=this.dirty){let t=this.parent;for(;!t.node;)t=t.parent;t.dirty<this.dirty&&(t.dirty=this.dirty),this.dirty=0}}slice(t,e,n){let r=Cn.create(this.parent,this.mark,!0,n),o=this.children,i=this.size;e<i&&(o=Hn(o,e,i,n)),t>0&&(o=Hn(o,0,t,n));for(let t=0;t<o.length;t++)o[t].parent=r;return r.children=o,r}ignoreMutation(t){return this.spec.ignoreMutation?this.spec.ignoreMutation(t):super.ignoreMutation(t)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}}class On extends xn{constructor(t,e,n,r,o,i,s,l,a){super(t,[],o,i),this.node=e,this.outerDeco=n,this.innerDeco=r,this.nodeDOM=s}static create(t,e,n,r,o,i){let s,l=o.nodeViews[e.type.name],a=l&&l(e,o,(()=>s?s.parent?s.parent.posBeforeChild(s):void 0:i),n,r),c=a&&a.dom,h=a&&a.contentDOM;if(e.isText)if(c){if(3!=c.nodeType)throw new RangeError("Text must be rendered as a DOM text node")}else c=document.createTextNode(e.text);else if(!c){let t=it.renderSpec(document,e.type.spec.toDOM(e),null,e.attrs);({dom:c,contentDOM:h}=t)}h||e.isText||"BR"==c.nodeName||(c.hasAttribute("contenteditable")||(c.contentEditable="false"),e.type.spec.draggable&&(c.draggable=!0));let d=c;return c=$n(c,n,e),a?s=new An(t,e,n,r,c,h||null,d,a,o,i+1):e.isText?new En(t,e,n,r,c,d,o):new On(t,e,n,r,c,h||null,d,o,i+1)}parseRule(){if(this.node.type.spec.reparseInView)return null;let t={node:this.node.type.name,attrs:this.node.attrs};if("pre"==this.node.type.whitespace&&(t.preserveWhitespace="full"),this.contentDOM)if(this.contentLost){for(let e=this.children.length-1;e>=0;e--){let n=this.children[e];if(this.dom.contains(n.dom.parentNode)){t.contentElement=n.dom.parentNode;break}}t.contentElement||(t.getContent=()=>o.empty)}else t.contentElement=this.contentDOM;else t.getContent=()=>this.node.content;return t}matchesNode(t,e,n){return 0==this.dirty&&t.eq(this.node)&&Bn(e,this.outerDeco)&&n.eq(this.innerDeco)}get size(){return this.node.nodeSize}get border(){return this.node.isLeaf?0:1}updateChildren(t,e){let n=this.node.inlineContent,r=e,o=t.composing?this.localCompositionInfo(t,e):null,i=o&&o.pos>-1?o:null,s=o&&o.pos<0,l=new Vn(this,i&&i.node,t);!function(t,e,n,r){let o=e.locals(t),i=0;if(0==o.length){for(let n=0;n<t.childCount;n++){let s=t.child(n);r(s,o,e.forChild(i,s),n),i+=s.nodeSize}return}let s=0,l=[],a=null;for(let c=0;;){let h,d,p,u;for(;s<o.length&&o[s].to==i;){let t=o[s++];t.widget&&(h?(d||(d=[h])).push(t):h=t)}if(h)if(d){d.sort(jn);for(let t=0;t<d.length;t++)n(d[t],c,!!a)}else n(h,c,!!a);if(a)u=-1,p=a,a=null;else{if(!(c<t.childCount))break;u=c,p=t.child(c++)}for(let t=0;t<l.length;t++)l[t].to<=i&&l.splice(t--,1);for(;s<o.length&&o[s].from<=i&&o[s].to>i;)l.push(o[s++]);let f=i+p.nodeSize;if(p.isText){let t=f;s<o.length&&o[s].from<t&&(t=o[s].from);for(let e=0;e<l.length;e++)l[e].to<t&&(t=l[e].to);t<f&&(a=p.cut(t-i),p=p.cut(0,t-i),f=t,u=-1)}else for(;s<o.length&&o[s].to<f;)s++;r(p,p.isInline&&!p.isLeaf?l.filter((t=>!t.inline)):l.slice(),e.forChild(i,p),u),i=f}}(this.node,this.innerDeco,((e,o,i)=>{e.spec.marks?l.syncToMarks(e.spec.marks,n,t):e.type.side>=0&&!i&&l.syncToMarks(o==this.node.childCount?a.none:this.node.child(o).marks,n,t),l.placeWidget(e,t,r)}),((e,i,a,c)=>{let h;l.syncToMarks(e.marks,n,t),l.findNodeMatch(e,i,a,c)||s&&t.state.selection.from>r&&t.state.selection.to<r+e.nodeSize&&(h=l.findIndexWithChild(o.node))>-1&&l.updateNodeAt(e,i,a,h,t)||l.updateNextNode(e,i,a,t,c,r)||l.addNode(e,i,a,t,r),r+=e.nodeSize})),l.syncToMarks([],n,t),this.node.isTextblock&&l.addTextblockHacks(),l.destroyRest(),(l.changed||2==this.dirty)&&(i&&this.protectLocalComposition(t,i),Dn(this.contentDOM,this.children,t),Ke&&function(t){if("UL"==t.nodeName||"OL"==t.nodeName){let e=t.style.cssText;t.style.cssText=e+"; list-style: square !important",window.getComputedStyle(t).listStyle,t.style.cssText=e}}(this.dom))}localCompositionInfo(t,e){let{from:n,to:r}=t.state.selection;if(!(t.state.selection instanceof re)||n<e||r>e+this.node.content.size)return null;let o=t.input.compositionNode;if(!o||!this.dom.contains(o.parentNode))return null;if(this.node.inlineContent){let t=o.nodeValue,i=function(t,e,n,r){for(let o=0,i=0;o<t.childCount&&i<=r;){let s=t.child(o++),l=i;if(i+=s.nodeSize,!s.isText)continue;let a=s.text;for(;o<t.childCount;){let e=t.child(o++);if(i+=e.nodeSize,!e.isText)break;a+=e.text}if(i>=n){if(i>=r&&a.slice(r-e.length-l,r-l)==e)return r-e.length;let t=l<r?a.lastIndexOf(e,r-l-1):-1;if(t>=0&&t+e.length+l>=n)return l+t;if(n==r&&a.length>=r+e.length-l&&a.slice(r-l,r-l+e.length)==e)return r}}return-1}(this.node.content,t,n-e,r-e);return i<0?null:{node:o,pos:i,text:t}}return{node:o,pos:-1,text:""}}protectLocalComposition(t,{node:e,pos:n,text:r}){if(this.getDesc(e))return;let o=e;for(;o.parentNode!=this.contentDOM;o=o.parentNode){for(;o.previousSibling;)o.parentNode.removeChild(o.previousSibling);for(;o.nextSibling;)o.parentNode.removeChild(o.nextSibling);o.pmViewDesc&&(o.pmViewDesc=void 0)}let i=new Mn(this,o,e,r);t.input.compositionNodes.push(i),this.children=Hn(this.children,n,n+r.length,t,i)}update(t,e,n,r){return!(3==this.dirty||!t.sameMarkup(this.node))&&(this.updateInner(t,e,n,r),!0)}updateInner(t,e,n,r){this.updateOuterDeco(e),this.node=t,this.innerDeco=n,this.contentDOM&&this.updateChildren(r,this.posAtStart),this.dirty=0}updateOuterDeco(t){if(Bn(t,this.outerDeco))return;let e=1!=this.nodeDOM.nodeType,n=this.dom;this.dom=zn(this.dom,this.nodeDOM,Pn(this.outerDeco,this.node,e),Pn(t,this.node,e)),this.dom!=n&&(n.pmViewDesc=void 0,this.dom.pmViewDesc=this),this.outerDeco=t}selectNode(){1==this.nodeDOM.nodeType&&this.nodeDOM.classList.add("ProseMirror-selectednode"),!this.contentDOM&&this.node.type.spec.draggable||(this.dom.draggable=!0)}deselectNode(){1==this.nodeDOM.nodeType&&(this.nodeDOM.classList.remove("ProseMirror-selectednode"),!this.contentDOM&&this.node.type.spec.draggable||this.dom.removeAttribute("draggable"))}get domAtom(){return this.node.isAtom}}function Tn(t,e,n,r,o){$n(r,e,t);let i=new On(void 0,t,e,n,r,r,r,o,0);return i.contentDOM&&i.updateChildren(o,0),i}class En extends On{constructor(t,e,n,r,o,i,s){super(t,e,n,r,o,null,i,s,0)}parseRule(){let t=this.nodeDOM.parentNode;for(;t&&t!=this.dom&&!t.pmIsDeco;)t=t.parentNode;return{skip:t||!0}}update(t,e,n,r){return!(3==this.dirty||0!=this.dirty&&!this.inParent()||!t.sameMarkup(this.node))&&(this.updateOuterDeco(e),0==this.dirty&&t.text==this.node.text||t.text==this.nodeDOM.nodeValue||(this.nodeDOM.nodeValue=t.text,r.trackWrites==this.nodeDOM&&(r.trackWrites=null)),this.node=t,this.dirty=0,!0)}inParent(){let t=this.parent.contentDOM;for(let e=this.nodeDOM;e;e=e.parentNode)if(e==t)return!0;return!1}domFromPos(t){return{node:this.nodeDOM,offset:t}}localPosFromDOM(t,e,n){return t==this.nodeDOM?this.posAtStart+Math.min(e,this.node.text.length):super.localPosFromDOM(t,e,n)}ignoreMutation(t){return"characterData"!=t.type&&"selection"!=t.type}slice(t,e,n){let r=this.node.cut(t,e),o=document.createTextNode(r.text);return new En(this.parent,r,this.outerDeco,this.innerDeco,o,o,n)}markDirty(t,e){super.markDirty(t,e),this.dom==this.nodeDOM||0!=t&&e!=this.nodeDOM.nodeValue.length||(this.dirty=3)}get domAtom(){return!1}isText(t){return this.node.text==t}}class Nn extends xn{parseRule(){return{ignore:!0}}matchesHack(t){return 0==this.dirty&&this.dom.nodeName==t}get domAtom(){return!0}get ignoreForCoords(){return"IMG"==this.dom.nodeName}}class An extends On{constructor(t,e,n,r,o,i,s,l,a,c){super(t,e,n,r,o,i,s,a,c),this.spec=l}update(t,e,n,r){if(3==this.dirty)return!1;if(this.spec.update&&(this.node.type==t.type||this.spec.multiType)){let o=this.spec.update(t,e,n);return o&&this.updateInner(t,e,n,r),o}return!(!this.contentDOM&&!t.isLeaf)&&super.update(t,e,n,r)}selectNode(){this.spec.selectNode?this.spec.selectNode():super.selectNode()}deselectNode(){this.spec.deselectNode?this.spec.deselectNode():super.deselectNode()}setSelection(t,e,n,r){this.spec.setSelection?this.spec.setSelection(t,e,n.root):super.setSelection(t,e,n,r)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}stopEvent(t){return!!this.spec.stopEvent&&this.spec.stopEvent(t)}ignoreMutation(t){return this.spec.ignoreMutation?this.spec.ignoreMutation(t):super.ignoreMutation(t)}}function Dn(t,e,n){let r=t.firstChild,o=!1;for(let i=0;i<e.length;i++){let s=e[i],l=s.dom;if(l.parentNode==t){for(;l!=r;)r=Fn(r),o=!0;r=r.nextSibling}else o=!0,t.insertBefore(l,r);if(s instanceof Cn){let e=r?r.previousSibling:t.lastChild;Dn(s.contentDOM,s.children,n),r=e?e.nextSibling:t.firstChild}}for(;r;)r=Fn(r),o=!0;o&&n.trackWrites==t&&(n.trackWrites=null)}const Rn=function(t){t&&(this.nodeName=t)};Rn.prototype=Object.create(null);const In=[new Rn];function Pn(t,e,n){if(0==t.length)return In;let r=n?In[0]:new Rn,o=[r];for(let i=0;i<t.length;i++){let s=t[i].type.attrs;if(s){s.nodeName&&o.push(r=new Rn(s.nodeName));for(let t in s){let i=s[t];null!=i&&(n&&1==o.length&&o.push(r=new Rn(e.isInline?"span":"div")),"class"==t?r.class=(r.class?r.class+" ":"")+i:"style"==t?r.style=(r.style?r.style+";":"")+i:"nodeName"!=t&&(r[t]=i))}}}return o}function zn(t,e,n,r){if(n==In&&r==In)return e;let o=e;for(let e=0;e<r.length;e++){let i=r[e],s=n[e];if(e){let e;s&&s.nodeName==i.nodeName&&o!=t&&(e=o.parentNode)&&e.nodeName.toLowerCase()==i.nodeName||(e=document.createElement(i.nodeName),e.pmIsDeco=!0,e.appendChild(o),s=In[0]),o=e}Ln(o,s||In[0],i)}return o}function Ln(t,e,n){for(let r in e)"class"==r||"style"==r||"nodeName"==r||r in n||t.removeAttribute(r);for(let r in n)"class"!=r&&"style"!=r&&"nodeName"!=r&&n[r]!=e[r]&&t.setAttribute(r,n[r]);if(e.class!=n.class){let r=e.class?e.class.split(" ").filter(Boolean):[],o=n.class?n.class.split(" ").filter(Boolean):[];for(let e=0;e<r.length;e++)-1==o.indexOf(r[e])&&t.classList.remove(r[e]);for(let e=0;e<o.length;e++)-1==r.indexOf(o[e])&&t.classList.add(o[e]);0==t.classList.length&&t.removeAttribute("class")}if(e.style!=n.style){if(e.style){let n,r=/\s*([\w\-\xa1-\uffff]+)\s*:(?:"(?:\\.|[^"])*"|'(?:\\.|[^'])*'|\(.*?\)|[^;])*/g;for(;n=r.exec(e.style);)t.style.removeProperty(n[1])}n.style&&(t.style.cssText+=n.style)}}function $n(t,e,n){return zn(t,t,In,Pn(e,n,1!=t.nodeType))}function Bn(t,e){if(t.length!=e.length)return!1;for(let n=0;n<t.length;n++)if(!t[n].type.eq(e[n].type))return!1;return!0}function Fn(t){let e=t.nextSibling;return t.parentNode.removeChild(t),e}class Vn{constructor(t,e,n){this.lock=e,this.view=n,this.index=0,this.stack=[],this.changed=!1,this.top=t,this.preMatch=function(t,e){let n=e,r=n.children.length,o=t.childCount,i=new Map,s=[];t:for(;o>0;){let l;for(;;)if(r){let t=n.children[r-1];if(!(t instanceof Cn)){l=t,r--;break}n=t,r=t.children.length}else{if(n==e)break t;r=n.parent.children.indexOf(n),n=n.parent}let a=l.node;if(a){if(a!=t.child(o-1))break;--o,i.set(l,o),s.push(l)}}return{index:o,matched:i,matches:s.reverse()}}(t.node.content,t)}destroyBetween(t,e){if(t!=e){for(let n=t;n<e;n++)this.top.children[n].destroy();this.top.children.splice(t,e-t),this.changed=!0}}destroyRest(){this.destroyBetween(this.index,this.top.children.length)}syncToMarks(t,e,n){let r=0,o=this.stack.length>>1,i=Math.min(o,t.length);for(;r<i&&(r==o-1?this.top:this.stack[r+1<<1]).matchesMark(t[r])&&!1!==t[r].type.spec.spanning;)r++;for(;r<o;)this.destroyRest(),this.top.dirty=0,this.index=this.stack.pop(),this.top=this.stack.pop(),o--;for(;o<t.length;){this.stack.push(this.top,this.index+1);let r=-1;for(let e=this.index;e<Math.min(this.index+3,this.top.children.length);e++){let n=this.top.children[e];if(n.matchesMark(t[o])&&!this.isLocked(n.dom)){r=e;break}}if(r>-1)r>this.index&&(this.changed=!0,this.destroyBetween(this.index,r)),this.top=this.top.children[this.index];else{let r=Cn.create(this.top,t[o],e,n);this.top.children.splice(this.index,0,r),this.top=r,this.changed=!0}this.index=0,o++}}findNodeMatch(t,e,n,r){let o,i=-1;if(r>=this.preMatch.index&&(o=this.preMatch.matches[r-this.preMatch.index]).parent==this.top&&o.matchesNode(t,e,n))i=this.top.children.indexOf(o,this.index);else for(let r=this.index,o=Math.min(this.top.children.length,r+5);r<o;r++){let o=this.top.children[r];if(o.matchesNode(t,e,n)&&!this.preMatch.matched.has(o)){i=r;break}}return!(i<0)&&(this.destroyBetween(this.index,i),this.index++,!0)}updateNodeAt(t,e,n,r,o){let i=this.top.children[r];return 3==i.dirty&&i.dom==i.contentDOM&&(i.dirty=2),!!i.update(t,e,n,o)&&(this.destroyBetween(this.index,r),this.index++,!0)}findIndexWithChild(t){for(;;){let e=t.parentNode;if(!e)return-1;if(e==this.top.contentDOM){let e=t.pmViewDesc;if(e)for(let t=this.index;t<this.top.children.length;t++)if(this.top.children[t]==e)return t;return-1}t=e}}updateNextNode(t,e,n,r,o,i){for(let s=this.index;s<this.top.children.length;s++){let l=this.top.children[s];if(l instanceof On){let a=this.preMatch.matched.get(l);if(null!=a&&a!=o)return!1;let c,h=l.dom,d=this.isLocked(h)&&!(t.isText&&l.node&&l.node.isText&&l.nodeDOM.nodeValue==t.text&&3!=l.dirty&&Bn(e,l.outerDeco));if(!d&&l.update(t,e,n,r))return this.destroyBetween(this.index,s),l.dom!=h&&(this.changed=!0),this.index++,!0;if(!d&&(c=this.recreateWrapper(l,t,e,n,r,i)))return this.destroyBetween(this.index,s),this.top.children[this.index]=c,c.contentDOM&&(c.dirty=2,c.updateChildren(r,i+1),c.dirty=0),this.changed=!0,this.index++,!0;break}}return!1}recreateWrapper(t,e,n,r,o,i){if(t.dirty||e.isAtom||!t.children.length||!t.node.content.eq(e.content)||!Bn(n,t.outerDeco)||!r.eq(t.innerDeco))return null;let s=On.create(this.top,e,n,r,o,i);if(s.contentDOM){s.children=t.children,t.children=[];for(let t of s.children)t.parent=s}return t.destroy(),s}addNode(t,e,n,r,o){let i=On.create(this.top,t,e,n,r,o);i.contentDOM&&i.updateChildren(r,o+1),this.top.children.splice(this.index++,0,i),this.changed=!0}placeWidget(t,e,n){let r=this.index<this.top.children.length?this.top.children[this.index]:null;if(!r||!r.matchesWidget(t)||t!=r.widget&&r.widget.type.toDOM.parentNode){let r=new Sn(this.top,t,e,n);this.top.children.splice(this.index++,0,r),this.changed=!0}else this.index++}addTextblockHacks(){let t=this.top.children[this.index-1],e=this.top;for(;t instanceof Cn;)e=t,t=e.children[e.children.length-1];(!t||!(t instanceof En)||/\n$/.test(t.node.text)||this.view.requiresGeckoHackNode&&/\s$/.test(t.node.text))&&((Je||We)&&t&&"false"==t.dom.contentEditable&&this.addHackNode("IMG",e),this.addHackNode("BR",this.top))}addHackNode(t,e){if(e==this.top&&this.index<e.children.length&&e.children[this.index].matchesHack(t))this.index++;else{let n=document.createElement(t);"IMG"==t&&(n.className="ProseMirror-separator",n.alt=""),"BR"==t&&(n.className="ProseMirror-trailingBreak");let r=new Nn(this.top,[],n,null);e!=this.top?e.children.push(r):e.children.splice(this.index++,0,r),this.changed=!0}}isLocked(t){return this.lock&&(t==this.lock||1==t.nodeType&&t.contains(this.lock.parentNode))}}function jn(t,e){return t.type.side-e.type.side}function Hn(t,e,n,r,o){let i=[];for(let s=0,l=0;s<t.length;s++){let a=t[s],c=l,h=l+=a.size;c>=n||h<=e?i.push(a):(c<e&&i.push(a.slice(0,e-c,r)),o&&(i.push(o),o=void 0),h>n&&i.push(a.slice(n-c,a.size,r)))}return i}function Wn(t,e=null){let n=t.domSelectionRange(),r=t.state.doc;if(!n.focusNode)return null;let o=t.docView.nearestDesc(n.focusNode),i=o&&0==o.size,s=t.docView.posFromDOM(n.focusNode,n.focusOffset,1);if(s<0)return null;let l,a,c=r.resolve(s);if(De(n)){for(l=s;o&&!o.node;)o=o.parent;let t=o.node;if(o&&t.isAtom&&ie.isSelectable(t)&&o.parent&&(!t.isInline||!function(t,e,n){for(let r=0==e,o=e==Ne(t);r||o;){if(t==n)return!0;let e=xe(t);if(!(t=t.parentNode))return!1;r=r&&0==e,o=o&&e==Ne(t)}}(n.focusNode,n.focusOffset,o.dom))){let t=o.posBefore;a=new ie(s==t?c:r.resolve(t))}}else{if(n instanceof t.dom.ownerDocument.defaultView.Selection&&n.rangeCount>1){let e=s,o=s;for(let r=0;r<n.rangeCount;r++){let i=n.getRangeAt(r);e=Math.min(e,t.docView.posFromDOM(i.startContainer,i.startOffset,1)),o=Math.max(o,t.docView.posFromDOM(i.endContainer,i.endOffset,-1))}if(e<0)return null;[l,s]=o==t.state.selection.anchor?[o,e]:[e,o],c=r.resolve(s)}else l=t.docView.posFromDOM(n.anchorNode,n.anchorOffset,1);if(l<0)return null}let h=r.resolve(l);if(!a){a=Xn(t,h,c,"pointer"==e||t.state.selection.head<c.pos&&!i?1:-1)}return a}function qn(t){return t.editable?t.hasFocus():tr(t)&&document.activeElement&&document.activeElement.contains(t.dom)}function Jn(t,e=!1){let n=t.state.selection;if(Qn(t,n),qn(t)){if(!e&&t.input.mouseDown&&t.input.mouseDown.allowDefault&&We){let e=t.domSelectionRange(),n=t.domObserver.currentSelection;if(e.anchorNode&&n.anchorNode&&Oe(e.anchorNode,e.anchorOffset,n.anchorNode,n.anchorOffset))return t.input.mouseDown.delayedSelectionSync=!0,void t.domObserver.setCurSelection()}if(t.domObserver.disconnectSelection(),t.cursorWrapper)!function(t){let e=t.domSelection(),n=document.createRange();if(!e)return;let r=t.cursorWrapper.dom,o="IMG"==r.nodeName;o?n.setStart(r.parentNode,xe(r)+1):n.setStart(r,0);n.collapse(!0),e.removeAllRanges(),e.addRange(n),!o&&!t.state.selection.visible&&Fe&&Ve<=11&&(r.disabled=!0,r.disabled=!1)}(t);else{let r,o,{anchor:i,head:s}=n;!Kn||n instanceof re||(n.$from.parent.inlineContent||(r=_n(t,n.from)),n.empty||n.$from.parent.inlineContent||(o=_n(t,n.to))),t.docView.setSelection(i,s,t,e),Kn&&(r&&Gn(r),o&&Gn(o)),n.visible?t.dom.classList.remove("ProseMirror-hideselection"):(t.dom.classList.add("ProseMirror-hideselection"),"onselectionchange"in document&&function(t){let e=t.dom.ownerDocument;e.removeEventListener("selectionchange",t.input.hideSelectionGuard);let n=t.domSelectionRange(),r=n.anchorNode,o=n.anchorOffset;e.addEventListener("selectionchange",t.input.hideSelectionGuard=()=>{n.anchorNode==r&&n.anchorOffset==o||(e.removeEventListener("selectionchange",t.input.hideSelectionGuard),setTimeout((()=>{qn(t)&&!t.state.selection.visible||t.dom.classList.remove("ProseMirror-hideselection")}),20))})}(t))}t.domObserver.setCurSelection(),t.domObserver.connectSelection()}}const Kn=Je||We&&qe<63;function _n(t,e){let{node:n,offset:r}=t.docView.domFromPos(e,0),o=r<n.childNodes.length?n.childNodes[r]:null,i=r?n.childNodes[r-1]:null;if(Je&&o&&"false"==o.contentEditable)return Un(o);if(!(o&&"false"!=o.contentEditable||i&&"false"!=i.contentEditable)){if(o)return Un(o);if(i)return Un(i)}}function Un(t){return t.contentEditable="true",Je&&t.draggable&&(t.draggable=!1,t.wasDraggable=!0),t}function Gn(t){t.contentEditable="false",t.wasDraggable&&(t.draggable=!0,t.wasDraggable=null)}function Qn(t,e){if(e instanceof ie){let n=t.docView.descAt(e.from);n!=t.lastSelectedViewDesc&&(Yn(t),n&&n.selectNode(),t.lastSelectedViewDesc=n)}else Yn(t)}function Yn(t){t.lastSelectedViewDesc&&(t.lastSelectedViewDesc.parent&&t.lastSelectedViewDesc.deselectNode(),t.lastSelectedViewDesc=void 0)}function Xn(t,e,n,r){return t.someProp("createSelectionBetween",(r=>r(t,e,n)))||re.between(e,n,r)}function Zn(t){return!(t.editable&&!t.hasFocus())&&tr(t)}function tr(t){let e=t.domSelectionRange();if(!e.anchorNode)return!1;try{return t.dom.contains(3==e.anchorNode.nodeType?e.anchorNode.parentNode:e.anchorNode)&&(t.editable||t.dom.contains(3==e.focusNode.nodeType?e.focusNode.parentNode:e.focusNode))}catch(t){return!1}}function er(t,e){let{$anchor:n,$head:r}=t.selection,o=e>0?n.max(r):n.min(r),i=o.parent.inlineContent?o.depth?t.doc.resolve(e>0?o.after():o.before()):null:o;return i&&Zt.findFrom(i,e)}function nr(t,e){return t.dispatch(t.state.tr.setSelection(e).scrollIntoView()),!0}function rr(t,e,n){let r=t.state.selection;if(!(r instanceof re)){if(r instanceof ie&&r.node.isInline)return nr(t,new re(e>0?r.$to:r.$from));{let n=er(t.state,e);return!!n&&nr(t,n)}}if(n.indexOf("s")>-1){let{$head:n}=r,o=n.textOffset?null:e<0?n.nodeBefore:n.nodeAfter;if(!o||o.isText||!o.isLeaf)return!1;let i=t.state.doc.resolve(n.pos+o.nodeSize*(e<0?-1:1));return nr(t,new re(r.$anchor,i))}if(!r.empty)return!1;if(t.endOfTextblock(e>0?"forward":"backward")){let n=er(t.state,e);return!!(n&&n instanceof ie)&&nr(t,n)}if(!(_e&&n.indexOf("m")>-1)){let n,o=r.$head,i=o.textOffset?null:e<0?o.nodeBefore:o.nodeAfter;if(!i||i.isText)return!1;let s=e<0?o.pos-i.nodeSize:o.pos;return!!(i.isAtom||(n=t.docView.descAt(s))&&!n.contentDOM)&&(ie.isSelectable(i)?nr(t,new ie(e<0?t.state.doc.resolve(o.pos-i.nodeSize):o)):!!Qe&&nr(t,new re(t.state.doc.resolve(e<0?s:s+i.nodeSize))))}}function or(t){return 3==t.nodeType?t.nodeValue.length:t.childNodes.length}function ir(t,e){let n=t.pmViewDesc;return n&&0==n.size&&(e<0||t.nextSibling||"BR"!=t.nodeName)}function sr(t,e){return e<0?function(t){let e=t.domSelectionRange(),n=e.focusNode,r=e.focusOffset;if(!n)return;let o,i,s=!1;je&&1==n.nodeType&&r<or(n)&&ir(n.childNodes[r],-1)&&(s=!0);for(;;)if(r>0){if(1!=n.nodeType)break;{let t=n.childNodes[r-1];if(ir(t,-1))o=n,i=--r;else{if(3!=t.nodeType)break;n=t,r=n.nodeValue.length}}}else{if(lr(n))break;{let e=n.previousSibling;for(;e&&ir(e,-1);)o=n.parentNode,i=xe(e),e=e.previousSibling;if(e)n=e,r=or(n);else{if(n=n.parentNode,n==t.dom)break;r=0}}}s?ar(t,n,r):o&&ar(t,o,i)}(t):function(t){let e=t.domSelectionRange(),n=e.focusNode,r=e.focusOffset;if(!n)return;let o,i,s=or(n);for(;;)if(r<s){if(1!=n.nodeType)break;if(!ir(n.childNodes[r],1))break;o=n,i=++r}else{if(lr(n))break;{let e=n.nextSibling;for(;e&&ir(e,1);)o=e.parentNode,i=xe(e)+1,e=e.nextSibling;if(e)n=e,r=0,s=or(n);else{if(n=n.parentNode,n==t.dom)break;r=s=0}}}o&&ar(t,o,i)}(t)}function lr(t){let e=t.pmViewDesc;return e&&e.node&&e.node.isBlock}function ar(t,e,n){if(3!=e.nodeType){let t,r;(r=function(t,e){for(;t&&e==t.childNodes.length&&!Ae(t);)e=xe(t)+1,t=t.parentNode;for(;t&&e<t.childNodes.length;){let n=t.childNodes[e];if(3==n.nodeType)return n;if(1==n.nodeType&&"false"==n.contentEditable)break;t=n,e=0}}(e,n))?(e=r,n=0):(t=function(t,e){for(;t&&!e&&!Ae(t);)e=xe(t),t=t.parentNode;for(;t&&e;){let n=t.childNodes[e-1];if(3==n.nodeType)return n;if(1==n.nodeType&&"false"==n.contentEditable)break;e=(t=n).childNodes.length}}(e,n))&&(e=t,n=t.nodeValue.length)}let r=t.domSelection();if(!r)return;if(De(r)){let t=document.createRange();t.setEnd(e,n),t.setStart(e,n),r.removeAllRanges(),r.addRange(t)}else r.extend&&r.extend(e,n);t.domObserver.setCurSelection();let{state:o}=t;setTimeout((()=>{t.state==o&&Jn(t)}),50)}function cr(t,e){let n=t.state.doc.resolve(e);if(!We&&!Ue&&n.parent.inlineContent){let r=t.coordsAtPos(e);if(e>n.start()){let n=t.coordsAtPos(e-1),o=(n.top+n.bottom)/2;if(o>r.top&&o<r.bottom&&Math.abs(n.left-r.left)>1)return n.left<r.left?"ltr":"rtl"}if(e<n.end()){let n=t.coordsAtPos(e+1),o=(n.top+n.bottom)/2;if(o>r.top&&o<r.bottom&&Math.abs(n.left-r.left)>1)return n.left>r.left?"ltr":"rtl"}}return"rtl"==getComputedStyle(t.dom).direction?"rtl":"ltr"}function hr(t,e,n){let r=t.state.selection;if(r instanceof re&&!r.empty||n.indexOf("s")>-1)return!1;if(_e&&n.indexOf("m")>-1)return!1;let{$from:o,$to:i}=r;if(!o.parent.inlineContent||t.endOfTextblock(e<0?"up":"down")){let n=er(t.state,e);if(n&&n instanceof ie)return nr(t,n)}if(!o.parent.inlineContent){let n=e<0?o:i,s=r instanceof le?Zt.near(n,e):Zt.findFrom(n,e);return!!s&&nr(t,s)}return!1}function dr(t,e){if(!(t.state.selection instanceof re))return!0;let{$head:n,$anchor:r,empty:o}=t.state.selection;if(!n.sameParent(r))return!0;if(!o)return!1;if(t.endOfTextblock(e>0?"forward":"backward"))return!0;let i=!n.textOffset&&(e<0?n.nodeBefore:n.nodeAfter);if(i&&!i.isText){let r=t.state.tr;return e<0?r.delete(n.pos-i.nodeSize,n.pos):r.delete(n.pos,n.pos+i.nodeSize),t.dispatch(r),!0}return!1}function pr(t,e,n){t.domObserver.stop(),e.contentEditable=n,t.domObserver.start()}function ur(t,e){let n=e.keyCode,r=function(t){let e="";return t.ctrlKey&&(e+="c"),t.metaKey&&(e+="m"),t.altKey&&(e+="a"),t.shiftKey&&(e+="s"),e}(e);if(8==n||_e&&72==n&&"c"==r)return dr(t,-1)||sr(t,-1);if(46==n&&!e.shiftKey||_e&&68==n&&"c"==r)return dr(t,1)||sr(t,1);if(13==n||27==n)return!0;if(37==n||_e&&66==n&&"c"==r){let e=37==n?"ltr"==cr(t,t.state.selection.from)?-1:1:-1;return rr(t,e,r)||sr(t,e)}if(39==n||_e&&70==n&&"c"==r){let e=39==n?"ltr"==cr(t,t.state.selection.from)?1:-1:1;return rr(t,e,r)||sr(t,e)}return 38==n||_e&&80==n&&"c"==r?hr(t,-1,r)||sr(t,-1):40==n||_e&&78==n&&"c"==r?function(t){if(!Je||t.state.selection.$head.parentOffset>0)return!1;let{focusNode:e,focusOffset:n}=t.domSelectionRange();if(e&&1==e.nodeType&&0==n&&e.firstChild&&"false"==e.firstChild.contentEditable){let n=e.firstChild;pr(t,n,"true"),setTimeout((()=>pr(t,n,"false")),20)}return!1}(t)||hr(t,1,r)||sr(t,1):r==(_e?"m":"c")&&(66==n||73==n||89==n||90==n)}function fr(t,e){t.someProp("transformCopied",(n=>{e=n(e,t)}));let n=[],{content:r,openStart:o,openEnd:i}=e;for(;o>1&&i>1&&1==r.childCount&&1==r.firstChild.childCount;){o--,i--;let t=r.firstChild;n.push(t.type.name,t.attrs!=t.type.defaultAttrs?t.attrs:null),r=t.content}let s=t.someProp("clipboardSerializer")||it.fromSchema(t.state.schema),l=Mr(),a=l.createElement("div");a.appendChild(s.serializeFragment(r,{document:l}));let c,h=a.firstChild,d=0;for(;h&&1==h.nodeType&&(c=xr[h.nodeName.toLowerCase()]);){for(let t=c.length-1;t>=0;t--){let e=l.createElement(c[t]);for(;a.firstChild;)e.appendChild(a.firstChild);a.appendChild(e),d++}h=a.firstChild}return h&&1==h.nodeType&&h.setAttribute("data-pm-slice",`${o} ${i}${d?` -${d}`:""} ${JSON.stringify(n)}`),{dom:a,text:t.someProp("clipboardTextSerializer",(n=>n(e,t)))||e.content.textBetween(0,e.content.size,"\n\n"),slice:e}}function mr(t,e,n,r,i){let s,l,a=i.parent.type.spec.code;if(!n&&!e)return null;let c=e&&(r||a||!n);if(c){if(t.someProp("transformPastedText",(n=>{e=n(e,a||r,t)})),a)return e?new h(o.from(t.state.schema.text(e.replace(/\r\n?/g,"\n"))),0,0):h.empty;let n=t.someProp("clipboardTextParser",(n=>n(e,i,r,t)));if(n)l=n;else{let n=i.marks(),{schema:r}=t.state,o=it.fromSchema(r);s=document.createElement("div"),e.split(/(?:\r\n?|\n)+/).forEach((t=>{let e=s.appendChild(document.createElement("p"));t&&e.appendChild(o.serializeNode(r.text(t,n)))}))}}else t.someProp("transformPastedHTML",(e=>{n=e(n,t)})),s=function(t){let e=/^(\s*<meta [^>]*>)*/.exec(t);e&&(t=t.slice(e[0].length));let n,r=Mr().createElement("div"),o=/<([a-z][^>\s]+)/i.exec(t);(n=o&&xr[o[1].toLowerCase()])&&(t=n.map((t=>"<"+t+">")).join("")+t+n.map((t=>"</"+t+">")).reverse().join(""));if(r.innerHTML=function(t){let e=window.trustedTypes;if(!e)return t;Cr||(Cr=e.createPolicy("ProseMirrorClipboard",{createHTML:t=>t}));return Cr.createHTML(t)}(t),n)for(let t=0;t<n.length;t++)r=r.querySelector(n[t])||r;return r}(n),Qe&&function(t){let e=t.querySelectorAll(We?"span:not([class]):not([style])":"span.Apple-converted-space");for(let n=0;n<e.length;n++){let r=e[n];1==r.childNodes.length&&" "==r.textContent&&r.parentNode&&r.parentNode.replaceChild(t.ownerDocument.createTextNode(" "),r)}}(s);let d=s&&s.querySelector("[data-pm-slice]"),p=d&&/^(\d+) (\d+)(?: -(\d+))? (.*)/.exec(d.getAttribute("data-pm-slice")||"");if(p&&p[3])for(let t=+p[3];t>0;t--){let t=s.firstChild;for(;t&&1!=t.nodeType;)t=t.nextSibling;if(!t)break;s=t}if(!l){let e=t.someProp("clipboardParser")||t.someProp("domParser")||G.fromSchema(t.state.schema);l=e.parseSlice(s,{preserveWhitespace:!(!c&&!p),context:i,ruleFromNode:t=>"BR"!=t.nodeName||t.nextSibling||!t.parentNode||gr.test(t.parentNode.nodeName)?null:{ignore:!0}})}if(p)l=function(t,e){if(!t.size)return t;let n,r=t.content.firstChild.type.schema;try{n=JSON.parse(e)}catch(e){return t}let{content:i,openStart:s,openEnd:l}=t;for(let t=n.length-2;t>=0;t-=2){let e=r.nodes[n[t]];if(!e||e.hasRequiredAttrs())break;i=o.from(e.create(n[t+1],i)),s++,l++}return new h(i,s,l)}(br(l,+p[1],+p[2]),p[4]);else if(l=h.maxOpen(function(t,e){if(t.childCount<2)return t;for(let n=e.depth;n>=0;n--){let r,i=e.node(n).contentMatchAt(e.index(n)),s=[];if(t.forEach((t=>{if(!s)return;let e,n=i.findWrapping(t.type);if(!n)return s=null;if(e=s.length&&r.length&&wr(n,r,t,s[s.length-1],0))s[s.length-1]=e;else{s.length&&(s[s.length-1]=vr(s[s.length-1],r.length));let e=yr(t,n);s.push(e),i=i.matchType(e.type),r=n}})),s)return o.from(s)}return t}(l.content,i),!0),l.openStart||l.openEnd){let t=0,e=0;for(let e=l.content.firstChild;t<l.openStart&&!e.type.spec.isolating;t++,e=e.firstChild);for(let t=l.content.lastChild;e<l.openEnd&&!t.type.spec.isolating;e++,t=t.lastChild);l=br(l,t,e)}return t.someProp("transformPasted",(e=>{l=e(l,t)})),l}const gr=/^(a|abbr|acronym|b|cite|code|del|em|i|ins|kbd|label|output|q|ruby|s|samp|span|strong|sub|sup|time|u|tt|var)$/i;function yr(t,e,n=0){for(let r=e.length-1;r>=n;r--)t=e[r].create(null,o.from(t));return t}function wr(t,e,n,r,i){if(i<t.length&&i<e.length&&t[i]==e[i]){let s=wr(t,e,n,r.lastChild,i+1);if(s)return r.copy(r.content.replaceChild(r.childCount-1,s));if(r.contentMatchAt(r.childCount).matchType(i==t.length-1?n.type:t[i+1]))return r.copy(r.content.append(o.from(yr(n,t,i+1))))}}function vr(t,e){if(0==e)return t;let n=t.content.replaceChild(t.childCount-1,vr(t.lastChild,e-1)),r=t.contentMatchAt(t.childCount).fillBefore(o.empty,!0);return t.copy(n.append(r))}function kr(t,e,n,r,i,s){let l=e<0?t.firstChild:t.lastChild,a=l.content;return t.childCount>1&&(s=0),i<r-1&&(a=kr(a,e,n,r,i+1,s)),i>=n&&(a=e<0?l.contentMatchAt(0).fillBefore(a,s<=i).append(a):a.append(l.contentMatchAt(l.childCount).fillBefore(o.empty,!0))),t.replaceChild(e<0?0:t.childCount-1,l.copy(a))}function br(t,e,n){return e<t.openStart&&(t=new h(kr(t.content,-1,e,t.openStart,0,t.openEnd),e,t.openEnd)),n<t.openEnd&&(t=new h(kr(t.content,1,n,t.openEnd,0,0),t.openStart,n)),t}const xr={thead:["table"],tbody:["table"],tfoot:["table"],caption:["table"],colgroup:["table"],col:["table","colgroup"],tr:["table","tbody"],td:["table","tbody","tr"],th:["table","tbody","tr"]};let Sr=null;function Mr(){return Sr||(Sr=document.implementation.createHTMLDocument("title"))}let Cr=null;const Or={},Tr={},Er={touchstart:!0,touchmove:!0};class Nr{constructor(){this.shiftKey=!1,this.mouseDown=null,this.lastKeyCode=null,this.lastKeyCodeTime=0,this.lastClick={time:0,x:0,y:0,type:""},this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastIOSEnter=0,this.lastIOSEnterFallbackTimeout=-1,this.lastFocus=0,this.lastTouch=0,this.lastChromeDelete=0,this.composing=!1,this.compositionNode=null,this.composingTimeout=-1,this.compositionNodes=[],this.compositionEndedAt=-2e8,this.compositionID=1,this.compositionPendingChanges=0,this.domChangeCount=0,this.eventHandlers=Object.create(null),this.hideSelectionGuard=null}}function Ar(t,e){t.input.lastSelectionOrigin=e,t.input.lastSelectionTime=Date.now()}function Dr(t){t.someProp("handleDOMEvents",(e=>{for(let n in e)t.input.eventHandlers[n]||t.dom.addEventListener(n,t.input.eventHandlers[n]=e=>Rr(t,e))}))}function Rr(t,e){return t.someProp("handleDOMEvents",(n=>{let r=n[e.type];return!!r&&(r(t,e)||e.defaultPrevented)}))}function Ir(t,e){if(!e.bubbles)return!0;if(e.defaultPrevented)return!1;for(let n=e.target;n!=t.dom;n=n.parentNode)if(!n||11==n.nodeType||n.pmViewDesc&&n.pmViewDesc.stopEvent(e))return!1;return!0}function Pr(t){return{left:t.clientX,top:t.clientY}}function zr(t,e,n,r,o){if(-1==r)return!1;let i=t.state.doc.resolve(r);for(let r=i.depth+1;r>0;r--)if(t.someProp(e,(e=>r>i.depth?e(t,n,i.nodeAfter,i.before(r),o,!0):e(t,n,i.node(r),i.before(r),o,!1))))return!0;return!1}function Lr(t,e,n){if(t.focused||t.focus(),t.state.selection.eq(e))return;let r=t.state.tr.setSelection(e);r.setMeta("pointer",!0),t.dispatch(r)}function $r(t,e,n,r,o){return zr(t,"handleClickOn",e,n,r)||t.someProp("handleClick",(n=>n(t,e,r)))||(o?function(t,e){if(-1==e)return!1;let n,r,o=t.state.selection;o instanceof ie&&(n=o.node);let i=t.state.doc.resolve(e);for(let t=i.depth+1;t>0;t--){let e=t>i.depth?i.nodeAfter:i.node(t);if(ie.isSelectable(e)){r=n&&o.$from.depth>0&&t>=o.$from.depth&&i.before(o.$from.depth+1)==o.$from.pos?i.before(o.$from.depth):i.before(t);break}}return null!=r&&(Lr(t,ie.create(t.state.doc,r)),!0)}(t,n):function(t,e){if(-1==e)return!1;let n=t.state.doc.resolve(e),r=n.nodeAfter;return!!(r&&r.isAtom&&ie.isSelectable(r))&&(Lr(t,new ie(n)),!0)}(t,n))}function Br(t,e,n,r){return zr(t,"handleDoubleClickOn",e,n,r)||t.someProp("handleDoubleClick",(n=>n(t,e,r)))}function Fr(t,e,n,r){return zr(t,"handleTripleClickOn",e,n,r)||t.someProp("handleTripleClick",(n=>n(t,e,r)))||function(t,e,n){if(0!=n.button)return!1;let r=t.state.doc;if(-1==e)return!!r.inlineContent&&(Lr(t,re.create(r,0,r.content.size)),!0);let o=r.resolve(e);for(let e=o.depth+1;e>0;e--){let n=e>o.depth?o.nodeAfter:o.node(e),i=o.before(e);if(n.inlineContent)Lr(t,re.create(r,i+1,i+1+n.content.size));else{if(!ie.isSelectable(n))continue;Lr(t,ie.create(r,i))}return!0}}(t,n,r)}function Vr(t){return Ur(t)}Tr.keydown=(t,e)=>{let n=e;if(t.input.shiftKey=16==n.keyCode||n.shiftKey,!Wr(t,n)&&(t.input.lastKeyCode=n.keyCode,t.input.lastKeyCodeTime=Date.now(),!Ge||!We||13!=n.keyCode))if(229!=n.keyCode&&t.domObserver.forceFlush(),!Ke||13!=n.keyCode||n.ctrlKey||n.altKey||n.metaKey)t.someProp("handleKeyDown",(e=>e(t,n)))||ur(t,n)?n.preventDefault():Ar(t,"key");else{let e=Date.now();t.input.lastIOSEnter=e,t.input.lastIOSEnterFallbackTimeout=setTimeout((()=>{t.input.lastIOSEnter==e&&(t.someProp("handleKeyDown",(e=>e(t,Re(13,"Enter")))),t.input.lastIOSEnter=0)}),200)}},Tr.keyup=(t,e)=>{16==e.keyCode&&(t.input.shiftKey=!1)},Tr.keypress=(t,e)=>{let n=e;if(Wr(t,n)||!n.charCode||n.ctrlKey&&!n.altKey||_e&&n.metaKey)return;if(t.someProp("handleKeyPress",(e=>e(t,n))))return void n.preventDefault();let r=t.state.selection;if(!(r instanceof re&&r.$from.sameParent(r.$to))){let e=String.fromCharCode(n.charCode);/[\r\n]/.test(e)||t.someProp("handleTextInput",(n=>n(t,r.$from.pos,r.$to.pos,e)))||t.dispatch(t.state.tr.insertText(e).scrollIntoView()),n.preventDefault()}};const jr=_e?"metaKey":"ctrlKey";Or.mousedown=(t,e)=>{let n=e;t.input.shiftKey=n.shiftKey;let r=Vr(t),o=Date.now(),i="singleClick";o-t.input.lastClick.time<500&&function(t,e){let n=e.x-t.clientX,r=e.y-t.clientY;return n*n+r*r<100}(n,t.input.lastClick)&&!n[jr]&&("singleClick"==t.input.lastClick.type?i="doubleClick":"doubleClick"==t.input.lastClick.type&&(i="tripleClick")),t.input.lastClick={time:o,x:n.clientX,y:n.clientY,type:i};let s=t.posAtCoords(Pr(n));s&&("singleClick"==i?(t.input.mouseDown&&t.input.mouseDown.done(),t.input.mouseDown=new Hr(t,s,n,!!r)):("doubleClick"==i?Br:Fr)(t,s.pos,s.inside,n)?n.preventDefault():Ar(t,"pointer"))};class Hr{constructor(t,e,n,r){let o,i;if(this.view=t,this.pos=e,this.event=n,this.flushed=r,this.delayedSelectionSync=!1,this.mightDrag=null,this.startDoc=t.state.doc,this.selectNode=!!n[jr],this.allowDefault=n.shiftKey,e.inside>-1)o=t.state.doc.nodeAt(e.inside),i=e.inside;else{let n=t.state.doc.resolve(e.pos);o=n.parent,i=n.depth?n.before():0}const s=r?null:n.target,l=s?t.docView.nearestDesc(s,!0):null;this.target=l&&1==l.dom.nodeType?l.dom:null;let{selection:a}=t.state;(0==n.button&&o.type.spec.draggable&&!1!==o.type.spec.selectable||a instanceof ie&&a.from<=i&&a.to>i)&&(this.mightDrag={node:o,pos:i,addAttr:!(!this.target||this.target.draggable),setUneditable:!(!this.target||!je||this.target.hasAttribute("contentEditable"))}),this.target&&this.mightDrag&&(this.mightDrag.addAttr||this.mightDrag.setUneditable)&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&(this.target.draggable=!0),this.mightDrag.setUneditable&&setTimeout((()=>{this.view.input.mouseDown==this&&this.target.setAttribute("contentEditable","false")}),20),this.view.domObserver.start()),t.root.addEventListener("mouseup",this.up=this.up.bind(this)),t.root.addEventListener("mousemove",this.move=this.move.bind(this)),Ar(t,"pointer")}done(){this.view.root.removeEventListener("mouseup",this.up),this.view.root.removeEventListener("mousemove",this.move),this.mightDrag&&this.target&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&this.target.removeAttribute("draggable"),this.mightDrag.setUneditable&&this.target.removeAttribute("contentEditable"),this.view.domObserver.start()),this.delayedSelectionSync&&setTimeout((()=>Jn(this.view))),this.view.input.mouseDown=null}up(t){if(this.done(),!this.view.dom.contains(t.target))return;let e=this.pos;this.view.state.doc!=this.startDoc&&(e=this.view.posAtCoords(Pr(t))),this.updateAllowDefault(t),this.allowDefault||!e?Ar(this.view,"pointer"):$r(this.view,e.pos,e.inside,t,this.selectNode)?t.preventDefault():0==t.button&&(this.flushed||Je&&this.mightDrag&&!this.mightDrag.node.isAtom||We&&!this.view.state.selection.visible&&Math.min(Math.abs(e.pos-this.view.state.selection.from),Math.abs(e.pos-this.view.state.selection.to))<=2)?(Lr(this.view,Zt.near(this.view.state.doc.resolve(e.pos))),t.preventDefault()):Ar(this.view,"pointer")}move(t){this.updateAllowDefault(t),Ar(this.view,"pointer"),0==t.buttons&&this.done()}updateAllowDefault(t){!this.allowDefault&&(Math.abs(this.event.x-t.clientX)>4||Math.abs(this.event.y-t.clientY)>4)&&(this.allowDefault=!0)}}function Wr(t,e){return!!t.composing||!!(Je&&Math.abs(e.timeStamp-t.input.compositionEndedAt)<500)&&(t.input.compositionEndedAt=-2e8,!0)}Or.touchstart=t=>{t.input.lastTouch=Date.now(),Vr(t),Ar(t,"pointer")},Or.touchmove=t=>{t.input.lastTouch=Date.now(),Ar(t,"pointer")},Or.contextmenu=t=>Vr(t);const qr=Ge?5e3:-1;function Jr(t,e){clearTimeout(t.input.composingTimeout),e>-1&&(t.input.composingTimeout=setTimeout((()=>Ur(t)),e))}function Kr(t){for(t.composing&&(t.input.composing=!1,t.input.compositionEndedAt=function(){let t=document.createEvent("Event");return t.initEvent("event",!0,!0),t.timeStamp}());t.input.compositionNodes.length>0;)t.input.compositionNodes.pop().markParentsDirty()}function _r(t){let e=t.domSelectionRange();if(!e.focusNode)return null;let n=function(t,e){for(;;){if(3==t.nodeType&&e)return t;if(1==t.nodeType&&e>0){if("false"==t.contentEditable)return null;e=Ne(t=t.childNodes[e-1])}else{if(!t.parentNode||Ae(t))return null;e=xe(t),t=t.parentNode}}}(e.focusNode,e.focusOffset),r=function(t,e){for(;;){if(3==t.nodeType&&e<t.nodeValue.length)return t;if(1==t.nodeType&&e<t.childNodes.length){if("false"==t.contentEditable)return null;t=t.childNodes[e],e=0}else{if(!t.parentNode||Ae(t))return null;e=xe(t)+1,t=t.parentNode}}}(e.focusNode,e.focusOffset);if(n&&r&&n!=r){let e=r.pmViewDesc,o=t.domObserver.lastChangedTextNode;if(n==o||r==o)return o;if(!e||!e.isText(r.nodeValue))return r;if(t.input.compositionNode==r){let t=n.pmViewDesc;if(t&&t.isText(n.nodeValue))return r}}return n||r}function Ur(t,e=!1){if(!(Ge&&t.domObserver.flushingSoon>=0)){if(t.domObserver.forceFlush(),Kr(t),e||t.docView&&t.docView.dirty){let n=Wn(t);return n&&!n.eq(t.state.selection)?t.dispatch(t.state.tr.setSelection(n)):!t.markCursor&&!e||t.state.selection.empty?t.updateState(t.state):t.dispatch(t.state.tr.deleteSelection()),!0}return!1}}Tr.compositionstart=Tr.compositionupdate=t=>{if(!t.composing){t.domObserver.flush();let{state:e}=t,n=e.selection.$to;if(e.selection instanceof re&&(e.storedMarks||!n.textOffset&&n.parentOffset&&n.nodeBefore.marks.some((t=>!1===t.type.spec.inclusive))))t.markCursor=t.state.storedMarks||n.marks(),Ur(t,!0),t.markCursor=null;else if(Ur(t,!e.selection.empty),je&&e.selection.empty&&n.parentOffset&&!n.textOffset&&n.nodeBefore.marks.length){let e=t.domSelectionRange();for(let n=e.focusNode,r=e.focusOffset;n&&1==n.nodeType&&0!=r;){let e=r<0?n.lastChild:n.childNodes[r-1];if(!e)break;if(3==e.nodeType){let n=t.domSelection();n&&n.collapse(e,e.nodeValue.length);break}n=e,r=-1}}t.input.composing=!0}Jr(t,qr)},Tr.compositionend=(t,e)=>{t.composing&&(t.input.composing=!1,t.input.compositionEndedAt=e.timeStamp,t.input.compositionPendingChanges=t.domObserver.pendingRecords().length?t.input.compositionID:0,t.input.compositionNode=null,t.input.compositionPendingChanges&&Promise.resolve().then((()=>t.domObserver.flush())),t.input.compositionID++,Jr(t,20))};const Gr=Fe&&Ve<15||Ke&&Ye<604;function Qr(t,e,n,r,o){let i=mr(t,e,n,r,t.state.selection.$from);if(t.someProp("handlePaste",(e=>e(t,o,i||h.empty))))return!0;if(!i)return!1;let s=function(t){return 0==t.openStart&&0==t.openEnd&&1==t.content.childCount?t.content.firstChild:null}(i),l=s?t.state.tr.replaceSelectionWith(s,r):t.state.tr.replaceSelection(i);return t.dispatch(l.scrollIntoView().setMeta("paste",!0).setMeta("uiEvent","paste")),!0}function Yr(t){let e=t.getData("text/plain")||t.getData("Text");if(e)return e;let n=t.getData("text/uri-list");return n?n.replace(/\r?\n/g," "):""}Or.copy=Tr.cut=(t,e)=>{let n=e,r=t.state.selection,o="cut"==n.type;if(r.empty)return;let i=Gr?null:n.clipboardData,s=r.content(),{dom:l,text:a}=fr(t,s);i?(n.preventDefault(),i.clearData(),i.setData("text/html",l.innerHTML),i.setData("text/plain",a)):function(t,e){if(!t.dom.parentNode)return;let n=t.dom.parentNode.appendChild(document.createElement("div"));n.appendChild(e),n.style.cssText="position: fixed; left: -10000px; top: 10px";let r=getSelection(),o=document.createRange();o.selectNodeContents(e),t.dom.blur(),r.removeAllRanges(),r.addRange(o),setTimeout((()=>{n.parentNode&&n.parentNode.removeChild(n),t.focus()}),50)}(t,l),o&&t.dispatch(t.state.tr.deleteSelection().scrollIntoView().setMeta("uiEvent","cut"))},Tr.paste=(t,e)=>{let n=e;if(t.composing&&!Ge)return;let r=Gr?null:n.clipboardData,o=t.input.shiftKey&&45!=t.input.lastKeyCode;r&&Qr(t,Yr(r),r.getData("text/html"),o,n)?n.preventDefault():function(t,e){if(!t.dom.parentNode)return;let n=t.input.shiftKey||t.state.selection.$from.parent.type.spec.code,r=t.dom.parentNode.appendChild(document.createElement(n?"textarea":"div"));n||(r.contentEditable="true"),r.style.cssText="position: fixed; left: -10000px; top: 10px",r.focus();let o=t.input.shiftKey&&45!=t.input.lastKeyCode;setTimeout((()=>{t.focus(),r.parentNode&&r.parentNode.removeChild(r),n?Qr(t,r.value,null,o,e):Qr(t,r.textContent,r.innerHTML,o,e)}),50)}(t,n)};class Xr{constructor(t,e,n){this.slice=t,this.move=e,this.node=n}}const Zr=_e?"altKey":"ctrlKey";Or.dragstart=(t,e)=>{let n=e,r=t.input.mouseDown;if(r&&r.done(),!n.dataTransfer)return;let o,i=t.state.selection,s=i.empty?null:t.posAtCoords(Pr(n));if(s&&s.pos>=i.from&&s.pos<=(i instanceof ie?i.to-1:i.to));else if(r&&r.mightDrag)o=ie.create(t.state.doc,r.mightDrag.pos);else if(n.target&&1==n.target.nodeType){let e=t.docView.nearestDesc(n.target,!0);e&&e.node.type.spec.draggable&&e!=t.docView&&(o=ie.create(t.state.doc,e.posBefore))}let l=(o||t.state.selection).content(),{dom:a,text:c,slice:h}=fr(t,l);(!n.dataTransfer.files.length||!We||qe>120)&&n.dataTransfer.clearData(),n.dataTransfer.setData(Gr?"Text":"text/html",a.innerHTML),n.dataTransfer.effectAllowed="copyMove",Gr||n.dataTransfer.setData("text/plain",c),t.dragging=new Xr(h,!n[Zr],o)},Or.dragend=t=>{let e=t.dragging;window.setTimeout((()=>{t.dragging==e&&(t.dragging=null)}),50)},Tr.dragover=Tr.dragenter=(t,e)=>e.preventDefault(),Tr.drop=(t,e)=>{let n=e,r=t.dragging;if(t.dragging=null,!n.dataTransfer)return;let o=t.posAtCoords(Pr(n));if(!o)return;let i=t.state.doc.resolve(o.pos),s=r&&r.slice;s?t.someProp("transformPasted",(e=>{s=e(s,t)})):s=mr(t,Yr(n.dataTransfer),Gr?null:n.dataTransfer.getData("text/html"),!1,i);let l=!(!r||n[Zr]);if(t.someProp("handleDrop",(e=>e(t,n,s||h.empty,l))))return void n.preventDefault();if(!s)return;n.preventDefault();let a=s?function(t,e,n){let r=t.resolve(e);if(!n.content.size)return e;let o=n.content;for(let t=0;t<n.openStart;t++)o=o.firstChild.content;for(let t=1;t<=(0==n.openStart&&n.size?2:1);t++)for(let e=r.depth;e>=0;e--){let n=e==r.depth?0:r.pos<=(r.start(e+1)+r.end(e+1))/2?-1:1,i=r.index(e)+(n>0?1:0),s=r.node(e),l=!1;if(1==t)l=s.canReplace(i,i,o);else{let t=s.contentMatchAt(i).findWrapping(o.firstChild.type);l=t&&s.canReplaceWith(i,i,t[0])}if(l)return 0==n?r.pos:n<0?r.before(e+1):r.after(e+1)}return null}(t.state.doc,i.pos,s):i.pos;null==a&&(a=i.pos);let c=t.state.tr;if(l){let{node:t}=r;t?t.replace(c):c.deleteSelection()}let d=c.mapping.map(a),p=0==s.openStart&&0==s.openEnd&&1==s.content.childCount,u=c.doc;if(p?c.replaceRangeWith(d,d,s.content.firstChild):c.replaceRange(d,d,s),c.doc.eq(u))return;let f=c.doc.resolve(d);if(p&&ie.isSelectable(s.content.firstChild)&&f.nodeAfter&&f.nodeAfter.sameMarkup(s.content.firstChild))c.setSelection(new ie(f));else{let e=c.mapping.map(a);c.mapping.maps[c.mapping.maps.length-1].forEach(((t,n,r,o)=>e=o)),c.setSelection(Xn(t,f,c.doc.resolve(e)))}t.focus(),t.dispatch(c.setMeta("uiEvent","drop"))},Or.focus=t=>{t.input.lastFocus=Date.now(),t.focused||(t.domObserver.stop(),t.dom.classList.add("ProseMirror-focused"),t.domObserver.start(),t.focused=!0,setTimeout((()=>{t.docView&&t.hasFocus()&&!t.domObserver.currentSelection.eq(t.domSelectionRange())&&Jn(t)}),20))},Or.blur=(t,e)=>{let n=e;t.focused&&(t.domObserver.stop(),t.dom.classList.remove("ProseMirror-focused"),t.domObserver.start(),n.relatedTarget&&t.dom.contains(n.relatedTarget)&&t.domObserver.currentSelection.clear(),t.focused=!1)},Or.beforeinput=(t,e)=>{if(We&&Ge&&"deleteContentBackward"==e.inputType){t.domObserver.flushSoon();let{domChangeCount:e}=t.input;setTimeout((()=>{if(t.input.domChangeCount!=e)return;if(t.dom.blur(),t.focus(),t.someProp("handleKeyDown",(e=>e(t,Re(8,"Backspace")))))return;let{$cursor:n}=t.state.selection;n&&n.pos>0&&t.dispatch(t.state.tr.delete(n.pos-1,n.pos).scrollIntoView())}),50)}};for(let t in Tr)Or[t]=Tr[t];function to(t,e){if(t==e)return!0;for(let n in t)if(t[n]!==e[n])return!1;for(let n in e)if(!(n in t))return!1;return!0}class eo{constructor(t,e){this.toDOM=t,this.spec=e||so,this.side=this.spec.side||0}map(t,e,n,r){let{pos:o,deleted:i}=t.mapResult(e.from+r,this.side<0?-1:1);return i?null:new oo(o-n,o-n,this)}valid(){return!0}eq(t){return this==t||t instanceof eo&&(this.spec.key&&this.spec.key==t.spec.key||this.toDOM==t.toDOM&&to(this.spec,t.spec))}destroy(t){this.spec.destroy&&this.spec.destroy(t)}}class no{constructor(t,e){this.attrs=t,this.spec=e||so}map(t,e,n,r){let o=t.map(e.from+r,this.spec.inclusiveStart?-1:1)-n,i=t.map(e.to+r,this.spec.inclusiveEnd?1:-1)-n;return o>=i?null:new oo(o,i,this)}valid(t,e){return e.from<e.to}eq(t){return this==t||t instanceof no&&to(this.attrs,t.attrs)&&to(this.spec,t.spec)}static is(t){return t.type instanceof no}destroy(){}}class ro{constructor(t,e){this.attrs=t,this.spec=e||so}map(t,e,n,r){let o=t.mapResult(e.from+r,1);if(o.deleted)return null;let i=t.mapResult(e.to+r,-1);return i.deleted||i.pos<=o.pos?null:new oo(o.pos-n,i.pos-n,this)}valid(t,e){let n,{index:r,offset:o}=t.content.findIndex(e.from);return o==e.from&&!(n=t.child(r)).isText&&o+n.nodeSize==e.to}eq(t){return this==t||t instanceof ro&&to(this.attrs,t.attrs)&&to(this.spec,t.spec)}destroy(){}}class oo{constructor(t,e,n){this.from=t,this.to=e,this.type=n}copy(t,e){return new oo(t,e,this.type)}eq(t,e=0){return this.type.eq(t.type)&&this.from+e==t.from&&this.to+e==t.to}map(t,e,n){return this.type.map(t,this,e,n)}static widget(t,e,n){return new oo(t,t,new eo(e,n))}static inline(t,e,n,r){return new oo(t,e,new no(n,r))}static node(t,e,n,r){return new oo(t,e,new ro(n,r))}get spec(){return this.type.spec}get inline(){return this.type instanceof no}get widget(){return this.type instanceof eo}}const io=[],so={};class lo{constructor(t,e){this.local=t.length?t:io,this.children=e.length?e:io}static create(t,e){return e.length?fo(e,t,0,so):ao}find(t,e,n){let r=[];return this.findInner(null==t?0:t,null==e?1e9:e,r,0,n),r}findInner(t,e,n,r,o){for(let i=0;i<this.local.length;i++){let s=this.local[i];s.from<=e&&s.to>=t&&(!o||o(s.spec))&&n.push(s.copy(s.from+r,s.to+r))}for(let i=0;i<this.children.length;i+=3)if(this.children[i]<e&&this.children[i+1]>t){let s=this.children[i]+1;this.children[i+2].findInner(t-s,e-s,n,r+s,o)}}map(t,e,n){return this==ao||0==t.maps.length?this:this.mapInner(t,e,0,0,n||so)}mapInner(t,e,n,r,o){let i;for(let s=0;s<this.local.length;s++){let l=this.local[s].map(t,n,r);l&&l.type.valid(e,l)?(i||(i=[])).push(l):o.onRemove&&o.onRemove(this.local[s].spec)}return this.children.length?function(t,e,n,r,o,i,s){let l=t.slice();for(let t=0,e=i;t<n.maps.length;t++){let r=0;n.maps[t].forEach(((t,n,o,i)=>{let s=i-o-(n-t);for(let o=0;o<l.length;o+=3){let i=l[o+1];if(i<0||t>i+e-r)continue;let a=l[o]+e-r;n>=a?l[o+1]=t<=a?-2:-1:t>=e&&s&&(l[o]+=s,l[o+1]+=s)}r+=s})),e=n.maps[t].map(e,-1)}let a=!1;for(let e=0;e<l.length;e+=3)if(l[e+1]<0){if(-2==l[e+1]){a=!0,l[e+1]=-1;continue}let c=n.map(t[e]+i),h=c-o;if(h<0||h>=r.content.size){a=!0;continue}let d=n.map(t[e+1]+i,-1)-o,{index:p,offset:u}=r.content.findIndex(h),f=r.maybeChild(p);if(f&&u==h&&u+f.nodeSize==d){let r=l[e+2].mapInner(n,f,c+1,t[e]+i+1,s);r!=ao?(l[e]=h,l[e+1]=d,l[e+2]=r):(l[e+1]=-2,a=!0)}else a=!0}if(a){let a=function(t,e,n,r,o,i,s){function l(t,e){for(let i=0;i<t.local.length;i++){let l=t.local[i].map(r,o,e);l?n.push(l):s.onRemove&&s.onRemove(t.local[i].spec)}for(let n=0;n<t.children.length;n+=3)l(t.children[n+2],t.children[n]+e+1)}for(let n=0;n<t.length;n+=3)-1==t[n+1]&&l(t[n+2],e[n]+i+1);return n}(l,t,e,n,o,i,s),c=fo(a,r,0,s);e=c.local;for(let t=0;t<l.length;t+=3)l[t+1]<0&&(l.splice(t,3),t-=3);for(let t=0,e=0;t<c.children.length;t+=3){let n=c.children[t];for(;e<l.length&&l[e]<n;)e+=3;l.splice(e,0,c.children[t],c.children[t+1],c.children[t+2])}}return new lo(e.sort(mo),l)}(this.children,i||[],t,e,n,r,o):i?new lo(i.sort(mo),io):ao}add(t,e){return e.length?this==ao?lo.create(t,e):this.addInner(t,e,0):this}addInner(t,e,n){let r,o=0;t.forEach(((t,i)=>{let s,l=i+n;if(s=po(e,t,l)){for(r||(r=this.children.slice());o<r.length&&r[o]<i;)o+=3;r[o]==i?r[o+2]=r[o+2].addInner(t,s,l+1):r.splice(o,0,i,i+t.nodeSize,fo(s,t,l+1,so)),o+=3}}));let i=ho(o?uo(e):e,-n);for(let e=0;e<i.length;e++)i[e].type.valid(t,i[e])||i.splice(e--,1);return new lo(i.length?this.local.concat(i).sort(mo):this.local,r||this.children)}remove(t){return 0==t.length||this==ao?this:this.removeInner(t,0)}removeInner(t,e){let n=this.children,r=this.local;for(let r=0;r<n.length;r+=3){let o,i=n[r]+e,s=n[r+1]+e;for(let e,n=0;n<t.length;n++)(e=t[n])&&e.from>i&&e.to<s&&(t[n]=null,(o||(o=[])).push(e));if(!o)continue;n==this.children&&(n=this.children.slice());let l=n[r+2].removeInner(o,i+1);l!=ao?n[r+2]=l:(n.splice(r,3),r-=3)}if(r.length)for(let n,o=0;o<t.length;o++)if(n=t[o])for(let t=0;t<r.length;t++)r[t].eq(n,e)&&(r==this.local&&(r=this.local.slice()),r.splice(t--,1));return n==this.children&&r==this.local?this:r.length||n.length?new lo(r,n):ao}forChild(t,e){if(this==ao)return this;if(e.isLeaf)return lo.empty;let n,r;for(let e=0;e<this.children.length;e+=3)if(this.children[e]>=t){this.children[e]==t&&(n=this.children[e+2]);break}let o=t+1,i=o+e.content.size;for(let t=0;t<this.local.length;t++){let e=this.local[t];if(e.from<i&&e.to>o&&e.type instanceof no){let t=Math.max(o,e.from)-o,n=Math.min(i,e.to)-o;t<n&&(r||(r=[])).push(e.copy(t,n))}}if(r){let t=new lo(r.sort(mo),io);return n?new co([t,n]):t}return n||ao}eq(t){if(this==t)return!0;if(!(t instanceof lo)||this.local.length!=t.local.length||this.children.length!=t.children.length)return!1;for(let e=0;e<this.local.length;e++)if(!this.local[e].eq(t.local[e]))return!1;for(let e=0;e<this.children.length;e+=3)if(this.children[e]!=t.children[e]||this.children[e+1]!=t.children[e+1]||!this.children[e+2].eq(t.children[e+2]))return!1;return!0}locals(t){return go(this.localsInner(t))}localsInner(t){if(this==ao)return io;if(t.inlineContent||!this.local.some(no.is))return this.local;let e=[];for(let t=0;t<this.local.length;t++)this.local[t].type instanceof no||e.push(this.local[t]);return e}forEachSet(t){t(this)}}lo.empty=new lo([],[]),lo.removeOverlap=go;const ao=lo.empty;class co{constructor(t){this.members=t}map(t,e){const n=this.members.map((n=>n.map(t,e,so)));return co.from(n)}forChild(t,e){if(e.isLeaf)return lo.empty;let n=[];for(let r=0;r<this.members.length;r++){let o=this.members[r].forChild(t,e);o!=ao&&(o instanceof co?n=n.concat(o.members):n.push(o))}return co.from(n)}eq(t){if(!(t instanceof co)||t.members.length!=this.members.length)return!1;for(let e=0;e<this.members.length;e++)if(!this.members[e].eq(t.members[e]))return!1;return!0}locals(t){let e,n=!0;for(let r=0;r<this.members.length;r++){let o=this.members[r].localsInner(t);if(o.length)if(e){n&&(e=e.slice(),n=!1);for(let t=0;t<o.length;t++)e.push(o[t])}else e=o}return e?go(n?e:e.sort(mo)):io}static from(t){switch(t.length){case 0:return ao;case 1:return t[0];default:return new co(t.every((t=>t instanceof lo))?t:t.reduce(((t,e)=>t.concat(e instanceof lo?e:e.members)),[]))}}forEachSet(t){for(let e=0;e<this.members.length;e++)this.members[e].forEachSet(t)}}function ho(t,e){if(!e||!t.length)return t;let n=[];for(let r=0;r<t.length;r++){let o=t[r];n.push(new oo(o.from+e,o.to+e,o.type))}return n}function po(t,e,n){if(e.isLeaf)return null;let r=n+e.nodeSize,o=null;for(let e,i=0;i<t.length;i++)(e=t[i])&&e.from>n&&e.to<r&&((o||(o=[])).push(e),t[i]=null);return o}function uo(t){let e=[];for(let n=0;n<t.length;n++)null!=t[n]&&e.push(t[n]);return e}function fo(t,e,n,r){let o=[],i=!1;e.forEach(((e,s)=>{let l=po(t,e,s+n);if(l){i=!0;let t=fo(l,e,n+s+1,r);t!=ao&&o.push(s,s+e.nodeSize,t)}}));let s=ho(i?uo(t):t,-n).sort(mo);for(let t=0;t<s.length;t++)s[t].type.valid(e,s[t])||(r.onRemove&&r.onRemove(s[t].spec),s.splice(t--,1));return s.length||o.length?new lo(s,o):ao}function mo(t,e){return t.from-e.from||t.to-e.to}function go(t){let e=t;for(let n=0;n<e.length-1;n++){let r=e[n];if(r.from!=r.to)for(let o=n+1;o<e.length;o++){let i=e[o];if(i.from!=r.from){i.from<r.to&&(e==t&&(e=t.slice()),e[n]=r.copy(r.from,i.from),yo(e,o,r.copy(i.from,r.to)));break}i.to!=r.to&&(e==t&&(e=t.slice()),e[o]=i.copy(i.from,r.to),yo(e,o+1,i.copy(r.to,i.to)))}}return e}function yo(t,e,n){for(;e<t.length&&mo(n,t[e])>0;)e++;t.splice(e,0,n)}function wo(t){let e=[];return t.someProp("decorations",(n=>{let r=n(t.state);r&&r!=ao&&e.push(r)})),t.cursorWrapper&&e.push(lo.create(t.state.doc,[t.cursorWrapper.deco])),co.from(e)}const vo={childList:!0,characterData:!0,characterDataOldValue:!0,attributes:!0,attributeOldValue:!0,subtree:!0},ko=Fe&&Ve<=11;class bo{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}set(t){this.anchorNode=t.anchorNode,this.anchorOffset=t.anchorOffset,this.focusNode=t.focusNode,this.focusOffset=t.focusOffset}clear(){this.anchorNode=this.focusNode=null}eq(t){return t.anchorNode==this.anchorNode&&t.anchorOffset==this.anchorOffset&&t.focusNode==this.focusNode&&t.focusOffset==this.focusOffset}}class xo{constructor(t,e){this.view=t,this.handleDOMChange=e,this.queue=[],this.flushingSoon=-1,this.observer=null,this.currentSelection=new bo,this.onCharData=null,this.suppressingSelectionUpdates=!1,this.lastChangedTextNode=null,this.observer=window.MutationObserver&&new window.MutationObserver((t=>{for(let e=0;e<t.length;e++)this.queue.push(t[e]);Fe&&Ve<=11&&t.some((t=>"childList"==t.type&&t.removedNodes.length||"characterData"==t.type&&t.oldValue.length>t.target.nodeValue.length))?this.flushSoon():this.flush()})),ko&&(this.onCharData=t=>{this.queue.push({target:t.target,type:"characterData",oldValue:t.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this)}flushSoon(){this.flushingSoon<0&&(this.flushingSoon=window.setTimeout((()=>{this.flushingSoon=-1,this.flush()}),20))}forceFlush(){this.flushingSoon>-1&&(window.clearTimeout(this.flushingSoon),this.flushingSoon=-1,this.flush())}start(){this.observer&&(this.observer.takeRecords(),this.observer.observe(this.view.dom,vo)),this.onCharData&&this.view.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.connectSelection()}stop(){if(this.observer){let t=this.observer.takeRecords();if(t.length){for(let e=0;e<t.length;e++)this.queue.push(t[e]);window.setTimeout((()=>this.flush()),20)}this.observer.disconnect()}this.onCharData&&this.view.dom.removeEventListener("DOMCharacterDataModified",this.onCharData),this.disconnectSelection()}connectSelection(){this.view.dom.ownerDocument.addEventListener("selectionchange",this.onSelectionChange)}disconnectSelection(){this.view.dom.ownerDocument.removeEventListener("selectionchange",this.onSelectionChange)}suppressSelectionUpdates(){this.suppressingSelectionUpdates=!0,setTimeout((()=>this.suppressingSelectionUpdates=!1),50)}onSelectionChange(){if(Zn(this.view)){if(this.suppressingSelectionUpdates)return Jn(this.view);if(Fe&&Ve<=11&&!this.view.state.selection.empty){let t=this.view.domSelectionRange();if(t.focusNode&&Oe(t.focusNode,t.focusOffset,t.anchorNode,t.anchorOffset))return this.flushSoon()}this.flush()}}setCurSelection(){this.currentSelection.set(this.view.domSelectionRange())}ignoreSelectionChange(t){if(!t.focusNode)return!0;let e,n=new Set;for(let e=t.focusNode;e;e=Se(e))n.add(e);for(let r=t.anchorNode;r;r=Se(r))if(n.has(r)){e=r;break}let r=e&&this.view.docView.nearestDesc(e);return r&&r.ignoreMutation({type:"selection",target:3==e.nodeType?e.parentNode:e})?(this.setCurSelection(),!0):void 0}pendingRecords(){if(this.observer)for(let t of this.observer.takeRecords())this.queue.push(t);return this.queue}flush(){let{view:t}=this;if(!t.docView||this.flushingSoon>-1)return;let e=this.pendingRecords();e.length&&(this.queue=[]);let n=t.domSelectionRange(),r=!this.suppressingSelectionUpdates&&!this.currentSelection.eq(n)&&Zn(t)&&!this.ignoreSelectionChange(n),o=-1,i=-1,s=!1,l=[];if(t.editable)for(let t=0;t<e.length;t++){let n=this.registerMutation(e[t],l);n&&(o=o<0?n.from:Math.min(n.from,o),i=i<0?n.to:Math.max(n.to,i),n.typeOver&&(s=!0))}if(je&&l.length){let e=l.filter((t=>"BR"==t.nodeName));if(2==e.length){let[t,n]=e;t.parentNode&&t.parentNode.parentNode==n.parentNode?n.remove():t.remove()}else{let{focusNode:n}=this.currentSelection;for(let r of e){let e=r.parentNode;!e||"LI"!=e.nodeName||n&&Oo(t,n)==e||r.remove()}}}let a=null;o<0&&r&&t.input.lastFocus>Date.now()-200&&Math.max(t.input.lastTouch,t.input.lastClick.time)<Date.now()-300&&De(n)&&(a=Wn(t))&&a.eq(Zt.near(t.state.doc.resolve(0),1))?(t.input.lastFocus=0,Jn(t),this.currentSelection.set(n),t.scrollToSelection()):(o>-1||r)&&(o>-1&&(t.docView.markDirty(o,i),function(t){if(So.has(t))return;if(So.set(t,null),-1!==["normal","nowrap","pre-line"].indexOf(getComputedStyle(t.dom).whiteSpace)){if(t.requiresGeckoHackNode=je,Mo)return;console.warn("ProseMirror expects the CSS white-space property to be set, preferably to 'pre-wrap'. It is recommended to load style/prosemirror.css from the prosemirror-view package."),Mo=!0}}(t)),this.handleDOMChange(o,i,s,l),t.docView&&t.docView.dirty?t.updateState(t.state):this.currentSelection.eq(n)||Jn(t),this.currentSelection.set(n))}registerMutation(t,e){if(e.indexOf(t.target)>-1)return null;let n=this.view.docView.nearestDesc(t.target);if("attributes"==t.type&&(n==this.view.docView||"contenteditable"==t.attributeName||"style"==t.attributeName&&!t.oldValue&&!t.target.getAttribute("style")))return null;if(!n||n.ignoreMutation(t))return null;if("childList"==t.type){for(let n=0;n<t.addedNodes.length;n++){let r=t.addedNodes[n];e.push(r),3==r.nodeType&&(this.lastChangedTextNode=r)}if(n.contentDOM&&n.contentDOM!=n.dom&&!n.contentDOM.contains(t.target))return{from:n.posBefore,to:n.posAfter};let r=t.previousSibling,o=t.nextSibling;if(Fe&&Ve<=11&&t.addedNodes.length)for(let e=0;e<t.addedNodes.length;e++){let{previousSibling:n,nextSibling:i}=t.addedNodes[e];(!n||Array.prototype.indexOf.call(t.addedNodes,n)<0)&&(r=n),(!i||Array.prototype.indexOf.call(t.addedNodes,i)<0)&&(o=i)}let i=r&&r.parentNode==t.target?xe(r)+1:0,s=n.localPosFromDOM(t.target,i,-1),l=o&&o.parentNode==t.target?xe(o):t.target.childNodes.length;return{from:s,to:n.localPosFromDOM(t.target,l,1)}}return"attributes"==t.type?{from:n.posAtStart-n.border,to:n.posAtEnd+n.border}:(this.lastChangedTextNode=t.target,{from:n.posAtStart,to:n.posAtEnd,typeOver:t.target.nodeValue==t.oldValue})}}let So=new WeakMap,Mo=!1;function Co(t,e){let n=e.startContainer,r=e.startOffset,o=e.endContainer,i=e.endOffset,s=t.domAtPos(t.state.selection.anchor);return Oe(s.node,s.offset,o,i)&&([n,r,o,i]=[o,i,n,r]),{anchorNode:n,anchorOffset:r,focusNode:o,focusOffset:i}}function Oo(t,e){for(let n=e.parentNode;n&&n!=t.dom;n=n.parentNode){let e=t.docView.nearestDesc(n,!0);if(e&&e.node.isBlock)return n}return null}function To(t){let e=t.pmViewDesc;if(e)return e.parseRule();if("BR"==t.nodeName&&t.parentNode){if(Je&&/^(ul|ol)$/i.test(t.parentNode.nodeName)){let t=document.createElement("div");return t.appendChild(document.createElement("li")),{skip:t}}if(t.parentNode.lastChild==t||Je&&/^(tr|table)$/i.test(t.parentNode.nodeName))return{ignore:!0}}else if("IMG"==t.nodeName&&t.getAttribute("mark-placeholder"))return{ignore:!0};return null}const Eo=/^(a|abbr|acronym|b|bd[io]|big|br|button|cite|code|data(list)?|del|dfn|em|i|ins|kbd|label|map|mark|meter|output|q|ruby|s|samp|small|span|strong|su[bp]|time|u|tt|var)$/i;function No(t,e,n,r,i){let s=t.input.compositionPendingChanges||(t.composing?t.input.compositionID:0);if(t.input.compositionPendingChanges=0,e<0){let e=t.input.lastSelectionTime>Date.now()-50?t.input.lastSelectionOrigin:null,n=Wn(t,e);if(n&&!t.state.selection.eq(n)){if(We&&Ge&&13===t.input.lastKeyCode&&Date.now()-100<t.input.lastKeyCodeTime&&t.someProp("handleKeyDown",(e=>e(t,Re(13,"Enter")))))return;let r=t.state.tr.setSelection(n);"pointer"==e?r.setMeta("pointer",!0):"key"==e&&r.scrollIntoView(),s&&r.setMeta("composition",s),t.dispatch(r)}return}let l=t.state.doc.resolve(e),a=l.sharedDepth(n);e=l.before(a+1),n=t.state.doc.resolve(n).after(a+1);let c,h,d=t.state.selection,p=function(t,e,n){let r,{node:o,fromOffset:i,toOffset:s,from:l,to:a}=t.docView.parseRange(e,n),c=t.domSelectionRange(),h=c.anchorNode;if(h&&t.dom.contains(1==h.nodeType?h:h.parentNode)&&(r=[{node:h,offset:c.anchorOffset}],De(c)||r.push({node:c.focusNode,offset:c.focusOffset})),We&&8===t.input.lastKeyCode)for(let t=s;t>i;t--){let e=o.childNodes[t-1],n=e.pmViewDesc;if("BR"==e.nodeName&&!n){s=t;break}if(!n||n.size)break}let d=t.state.doc,p=t.someProp("domParser")||G.fromSchema(t.state.schema),u=d.resolve(l),f=null,m=p.parse(o,{topNode:u.parent,topMatch:u.parent.contentMatchAt(u.index()),topOpen:!0,from:i,to:s,preserveWhitespace:"pre"!=u.parent.type.whitespace||"full",findPositions:r,ruleFromNode:To,context:u});if(r&&null!=r[0].pos){let t=r[0].pos,e=r[1]&&r[1].pos;null==e&&(e=t),f={anchor:t+l,head:e+l}}return{doc:m,sel:f,from:l,to:a}}(t,e,n),u=t.state.doc,f=u.slice(p.from,p.to);8===t.input.lastKeyCode&&Date.now()-100<t.input.lastKeyCodeTime?(c=t.state.selection.to,h="end"):(c=t.state.selection.from,h="start"),t.input.lastKeyCode=null;let m=function(t,e,n,r,o){let i=t.findDiffStart(e,n);if(null==i)return null;let{a:s,b:l}=t.findDiffEnd(e,n+t.size,n+e.size);if("end"==o){r-=s+Math.max(0,i-Math.min(s,l))-i}if(s<i&&t.size<e.size){let t=r<=i&&r>=s?i-r:0;i-=t,i&&i<e.size&&Ro(e.textBetween(i-1,i+1))&&(i+=t?1:-1),l=i+(l-s),s=i}else if(l<i){let e=r<=i&&r>=l?i-r:0;i-=e,i&&i<t.size&&Ro(t.textBetween(i-1,i+1))&&(i+=e?1:-1),s=i+(s-l),l=i}return{start:i,endA:s,endB:l}}(f.content,p.doc.content,p.from,c,h);if(m&&t.input.domChangeCount++,(Ke&&t.input.lastIOSEnter>Date.now()-225||Ge)&&i.some((t=>1==t.nodeType&&!Eo.test(t.nodeName)))&&(!m||m.endA>=m.endB)&&t.someProp("handleKeyDown",(e=>e(t,Re(13,"Enter")))))return void(t.input.lastIOSEnter=0);if(!m){if(!(r&&d instanceof re&&!d.empty&&d.$head.sameParent(d.$anchor))||t.composing||p.sel&&p.sel.anchor!=p.sel.head){if(p.sel){let e=Ao(t,t.state.doc,p.sel);if(e&&!e.eq(t.state.selection)){let n=t.state.tr.setSelection(e);s&&n.setMeta("composition",s),t.dispatch(n)}}return}m={start:d.from,endA:d.to,endB:d.to}}t.state.selection.from<t.state.selection.to&&m.start==m.endB&&t.state.selection instanceof re&&(m.start>t.state.selection.from&&m.start<=t.state.selection.from+2&&t.state.selection.from>=p.from?m.start=t.state.selection.from:m.endA<t.state.selection.to&&m.endA>=t.state.selection.to-2&&t.state.selection.to<=p.to&&(m.endB+=t.state.selection.to-m.endA,m.endA=t.state.selection.to)),Fe&&Ve<=11&&m.endB==m.start+1&&m.endA==m.start&&m.start>p.from&&"  "==p.doc.textBetween(m.start-p.from-1,m.start-p.from+1)&&(m.start--,m.endA--,m.endB--);let g,y=p.doc.resolveNoCache(m.start-p.from),w=p.doc.resolveNoCache(m.endB-p.from),v=u.resolve(m.start),k=y.sameParent(w)&&y.parent.inlineContent&&v.end()>=m.endA;if((Ke&&t.input.lastIOSEnter>Date.now()-225&&(!k||i.some((t=>"DIV"==t.nodeName||"P"==t.nodeName)))||!k&&y.pos<p.doc.content.size&&!y.sameParent(w)&&(g=Zt.findFrom(p.doc.resolve(y.pos+1),1,!0))&&g.head==w.pos)&&t.someProp("handleKeyDown",(e=>e(t,Re(13,"Enter")))))return void(t.input.lastIOSEnter=0);if(t.state.selection.anchor>m.start&&function(t,e,n,r,o){if(n-e<=o.pos-r.pos||Do(r,!0,!1)<o.pos)return!1;let i=t.resolve(e);if(!r.parent.isTextblock){let t=i.nodeAfter;return null!=t&&n==e+t.nodeSize}if(i.parentOffset<i.parent.content.size||!i.parent.isTextblock)return!1;let s=t.resolve(Do(i,!0,!0));return!(!s.parent.isTextblock||s.pos>n||Do(s,!0,!1)<n)&&r.parent.content.cut(r.parentOffset).eq(s.parent.content)}(u,m.start,m.endA,y,w)&&t.someProp("handleKeyDown",(e=>e(t,Re(8,"Backspace")))))return void(Ge&&We&&t.domObserver.suppressSelectionUpdates());We&&m.endB==m.start&&(t.input.lastChromeDelete=Date.now()),Ge&&!k&&y.start()!=w.start()&&0==w.parentOffset&&y.depth==w.depth&&p.sel&&p.sel.anchor==p.sel.head&&p.sel.head==m.endA&&(m.endB-=2,w=p.doc.resolveNoCache(m.endB-p.from),setTimeout((()=>{t.someProp("handleKeyDown",(function(e){return e(t,Re(13,"Enter"))}))}),20));let b,x,S,M=m.start,C=m.endA;if(k)if(y.pos==w.pos)Fe&&Ve<=11&&0==y.parentOffset&&(t.domObserver.suppressSelectionUpdates(),setTimeout((()=>Jn(t)),20)),b=t.state.tr.delete(M,C),x=u.resolve(m.start).marksAcross(u.resolve(m.endA));else if(m.endA==m.endB&&(S=function(t,e){let n,r,i,s=t.firstChild.marks,l=e.firstChild.marks,a=s,c=l;for(let t=0;t<l.length;t++)a=l[t].removeFromSet(a);for(let t=0;t<s.length;t++)c=s[t].removeFromSet(c);if(1==a.length&&0==c.length)r=a[0],n="add",i=t=>t.mark(r.addToSet(t.marks));else{if(0!=a.length||1!=c.length)return null;r=c[0],n="remove",i=t=>t.mark(r.removeFromSet(t.marks))}let h=[];for(let t=0;t<e.childCount;t++)h.push(i(e.child(t)));if(o.from(h).eq(t))return{mark:r,type:n}}(y.parent.content.cut(y.parentOffset,w.parentOffset),v.parent.content.cut(v.parentOffset,m.endA-v.start()))))b=t.state.tr,"add"==S.type?b.addMark(M,C,S.mark):b.removeMark(M,C,S.mark);else if(y.parent.child(y.index()).isText&&y.index()==w.index()-(w.textOffset?0:1)){let e=y.parent.textBetween(y.parentOffset,w.parentOffset);if(t.someProp("handleTextInput",(n=>n(t,M,C,e))))return;b=t.state.tr.insertText(e,M,C)}if(b||(b=t.state.tr.replace(M,C,p.doc.slice(m.start-p.from,m.endB-p.from))),p.sel){let e=Ao(t,b.doc,p.sel);e&&!(We&&t.composing&&e.empty&&(m.start!=m.endB||t.input.lastChromeDelete<Date.now()-100)&&(e.head==M||e.head==b.mapping.map(C)-1)||Fe&&e.empty&&e.head==M)&&b.setSelection(e)}x&&b.ensureMarks(x),s&&b.setMeta("composition",s),t.dispatch(b.scrollIntoView())}function Ao(t,e,n){return Math.max(n.anchor,n.head)>e.content.size?null:Xn(t,e.resolve(n.anchor),e.resolve(n.head))}function Do(t,e,n){let r=t.depth,o=e?t.end():t.pos;for(;r>0&&(e||t.indexAfter(r)==t.node(r).childCount);)r--,o++,e=!1;if(n){let e=t.node(r).maybeChild(t.indexAfter(r));for(;e&&!e.isLeaf;)e=e.firstChild,o++}return o}function Ro(t){if(2!=t.length)return!1;let e=t.charCodeAt(0),n=t.charCodeAt(1);return e>=56320&&e<=57343&&n>=55296&&n<=56319}class Io{constructor(t,e){this._root=null,this.focused=!1,this.trackWrites=null,this.mounted=!1,this.markCursor=null,this.cursorWrapper=null,this.lastSelectedViewDesc=void 0,this.input=new Nr,this.prevDirectPlugins=[],this.pluginViews=[],this.requiresGeckoHackNode=!1,this.dragging=null,this._props=e,this.state=e.state,this.directPlugins=e.plugins||[],this.directPlugins.forEach(Bo),this.dispatch=this.dispatch.bind(this),this.dom=t&&t.mount||document.createElement("div"),t&&(t.appendChild?t.appendChild(this.dom):"function"==typeof t?t(this.dom):t.mount&&(this.mounted=!0)),this.editable=Lo(this),zo(this),this.nodeViews=$o(this),this.docView=Tn(this.state.doc,Po(this),wo(this),this.dom,this),this.domObserver=new xo(this,((t,e,n,r)=>No(this,t,e,n,r))),this.domObserver.start(),function(t){for(let e in Or){let n=Or[e];t.dom.addEventListener(e,t.input.eventHandlers[e]=e=>{!Ir(t,e)||Rr(t,e)||!t.editable&&e.type in Tr||n(t,e)},Er[e]?{passive:!0}:void 0)}Je&&t.dom.addEventListener("input",(()=>null)),Dr(t)}(this),this.updatePluginViews()}get composing(){return this.input.composing}get props(){if(this._props.state!=this.state){let t=this._props;this._props={};for(let e in t)this._props[e]=t[e];this._props.state=this.state}return this._props}update(t){t.handleDOMEvents!=this._props.handleDOMEvents&&Dr(this);let e=this._props;this._props=t,t.plugins&&(t.plugins.forEach(Bo),this.directPlugins=t.plugins),this.updateStateInner(t.state,e)}setProps(t){let e={};for(let t in this._props)e[t]=this._props[t];e.state=this.state;for(let n in t)e[n]=t[n];this.update(e)}updateState(t){this.updateStateInner(t,this._props)}updateStateInner(t,e){var n;let r=this.state,o=!1,i=!1;t.storedMarks&&this.composing&&(Kr(this),i=!0),this.state=t;let s=r.plugins!=t.plugins||this._props.plugins!=e.plugins;if(s||this._props.plugins!=e.plugins||this._props.nodeViews!=e.nodeViews){let t=$o(this);(function(t,e){let n=0,r=0;for(let r in t){if(t[r]!=e[r])return!0;n++}for(let t in e)r++;return n!=r})(t,this.nodeViews)&&(this.nodeViews=t,o=!0)}(s||e.handleDOMEvents!=this._props.handleDOMEvents)&&Dr(this),this.editable=Lo(this),zo(this);let l=wo(this),a=Po(this),c=r.plugins==t.plugins||r.doc.eq(t.doc)?t.scrollToSelection>r.scrollToSelection?"to selection":"preserve":"reset",h=o||!this.docView.matchesNode(t.doc,a,l);!h&&t.selection.eq(r.selection)||(i=!0);let d="preserve"==c&&i&&null==this.dom.style.overflowAnchor&&function(t){let e,n,r=t.dom.getBoundingClientRect(),o=Math.max(0,r.top);for(let i=(r.left+r.right)/2,s=o+1;s<Math.min(innerHeight,r.bottom);s+=5){let r=t.root.elementFromPoint(i,s);if(!r||r==t.dom||!t.dom.contains(r))continue;let l=r.getBoundingClientRect();if(l.top>=o-20){e=r,n=l.top;break}}return{refDOM:e,refTop:n,stack:nn(t.dom)}}(this);if(i){this.domObserver.stop();let e=h&&(Fe||We)&&!this.composing&&!r.selection.empty&&!t.selection.empty&&function(t,e){let n=Math.min(t.$anchor.sharedDepth(t.head),e.$anchor.sharedDepth(e.head));return t.$anchor.start(n)!=e.$anchor.start(n)}(r.selection,t.selection);if(h){let n=We?this.trackWrites=this.domSelectionRange().focusNode:null;this.composing&&(this.input.compositionNode=_r(this)),!o&&this.docView.update(t.doc,a,l,this)||(this.docView.updateOuterDeco(a),this.docView.destroy(),this.docView=Tn(t.doc,a,l,this.dom,this)),n&&!this.trackWrites&&(e=!0)}e||!(this.input.mouseDown&&this.domObserver.currentSelection.eq(this.domSelectionRange())&&function(t){let e=t.docView.domFromPos(t.state.selection.anchor,0),n=t.domSelectionRange();return Oe(e.node,e.offset,n.anchorNode,n.anchorOffset)}(this))?Jn(this,e):(Qn(this,t.selection),this.domObserver.setCurSelection()),this.domObserver.start()}this.updatePluginViews(r),(null===(n=this.dragging)||void 0===n?void 0:n.node)&&!r.doc.eq(t.doc)&&this.updateDraggedNode(this.dragging,r),"reset"==c?this.dom.scrollTop=0:"to selection"==c?this.scrollToSelection():d&&function({refDOM:t,refTop:e,stack:n}){let r=t?t.getBoundingClientRect().top:0;rn(n,0==r?0:r-e)}(d)}scrollToSelection(){let t=this.domSelectionRange().focusNode;if(this.someProp("handleScrollToSelection",(t=>t(this))));else if(this.state.selection instanceof ie){let e=this.docView.domAfterPos(this.state.selection.from);1==e.nodeType&&en(this,e.getBoundingClientRect(),t)}else en(this,this.coordsAtPos(this.state.selection.head,1),t)}destroyPluginViews(){let t;for(;t=this.pluginViews.pop();)t.destroy&&t.destroy()}updatePluginViews(t){if(t&&t.plugins==this.state.plugins&&this.directPlugins==this.prevDirectPlugins)for(let e=0;e<this.pluginViews.length;e++){let n=this.pluginViews[e];n.update&&n.update(this,t)}else{this.prevDirectPlugins=this.directPlugins,this.destroyPluginViews();for(let t=0;t<this.directPlugins.length;t++){let e=this.directPlugins[t];e.spec.view&&this.pluginViews.push(e.spec.view(this))}for(let t=0;t<this.state.plugins.length;t++){let e=this.state.plugins[t];e.spec.view&&this.pluginViews.push(e.spec.view(this))}}}updateDraggedNode(t,e){let n=t.node,r=-1;if(this.state.doc.nodeAt(n.from)==n.node)r=n.from;else{let t=n.from+(this.state.doc.content.size-e.doc.content.size);(t>0&&this.state.doc.nodeAt(t))==n.node&&(r=t)}this.dragging=new Xr(t.slice,t.move,r<0?void 0:ie.create(this.state.doc,r))}someProp(t,e){let n,r=this._props&&this._props[t];if(null!=r&&(n=e?e(r):r))return n;for(let r=0;r<this.directPlugins.length;r++){let o=this.directPlugins[r].props[t];if(null!=o&&(n=e?e(o):o))return n}let o=this.state.plugins;if(o)for(let r=0;r<o.length;r++){let i=o[r].props[t];if(null!=i&&(n=e?e(i):i))return n}}hasFocus(){if(Fe){let t=this.root.activeElement;if(t==this.dom)return!0;if(!t||!this.dom.contains(t))return!1;for(;t&&this.dom!=t&&this.dom.contains(t);){if("false"==t.contentEditable)return!1;t=t.parentElement}return!0}return this.root.activeElement==this.dom}focus(){this.domObserver.stop(),this.editable&&function(t){if(t.setActive)return t.setActive();if(on)return t.focus(on);let e=nn(t);t.focus(null==on?{get preventScroll(){return on={preventScroll:!0},!0}}:void 0),on||(on=!1,rn(e,0))}(this.dom),Jn(this),this.domObserver.start()}get root(){let t=this._root;if(null==t)for(let t=this.dom.parentNode;t;t=t.parentNode)if(9==t.nodeType||11==t.nodeType&&t.host)return t.getSelection||(Object.getPrototypeOf(t).getSelection=()=>t.ownerDocument.getSelection()),this._root=t;return t||document}updateRoot(){this._root=null}posAtCoords(t){return cn(this,t)}coordsAtPos(t,e=1){return un(this,t,e)}domAtPos(t,e=0){return this.docView.domFromPos(t,e)}nodeDOM(t){let e=this.docView.descAt(t);return e?e.nodeDOM:null}posAtDOM(t,e,n=-1){let r=this.docView.posFromDOM(t,e,n);if(null==r)throw new RangeError("DOM position not inside the editor");return r}endOfTextblock(t,e){return bn(this,e||this.state,t)}pasteHTML(t,e){return Qr(this,"",t,!1,e||new ClipboardEvent("paste"))}pasteText(t,e){return Qr(this,t,null,!0,e||new ClipboardEvent("paste"))}destroy(){this.docView&&(!function(t){t.domObserver.stop();for(let e in t.input.eventHandlers)t.dom.removeEventListener(e,t.input.eventHandlers[e]);clearTimeout(t.input.composingTimeout),clearTimeout(t.input.lastIOSEnterFallbackTimeout)}(this),this.destroyPluginViews(),this.mounted?(this.docView.update(this.state.doc,[],wo(this),this),this.dom.textContent=""):this.dom.parentNode&&this.dom.parentNode.removeChild(this.dom),this.docView.destroy(),this.docView=null,Me=null)}get isDestroyed(){return null==this.docView}dispatchEvent(t){return function(t,e){Rr(t,e)||!Or[e.type]||!t.editable&&e.type in Tr||Or[e.type](t,e)}(this,t)}dispatch(t){let e=this._props.dispatchTransaction;e?e.call(this,t):this.updateState(this.state.apply(t))}domSelectionRange(){let t=this.domSelection();return t?Je&&11===this.root.nodeType&&function(t){let e=t.activeElement;for(;e&&e.shadowRoot;)e=e.shadowRoot.activeElement;return e}(this.dom.ownerDocument)==this.dom&&function(t,e){if(e.getComposedRanges){let n=e.getComposedRanges(t.root)[0];if(n)return Co(t,n)}let n;function r(t){t.preventDefault(),t.stopImmediatePropagation(),n=t.getTargetRanges()[0]}return t.dom.addEventListener("beforeinput",r,!0),document.execCommand("indent"),t.dom.removeEventListener("beforeinput",r,!0),n?Co(t,n):null}(this,t)||t:{focusNode:null,focusOffset:0,anchorNode:null,anchorOffset:0}}domSelection(){return this.root.getSelection()}}function Po(t){let e=Object.create(null);return e.class="ProseMirror",e.contenteditable=String(t.editable),t.someProp("attributes",(n=>{if("function"==typeof n&&(n=n(t.state)),n)for(let t in n)"class"==t?e.class+=" "+n[t]:"style"==t?e.style=(e.style?e.style+";":"")+n[t]:e[t]||"contenteditable"==t||"nodeName"==t||(e[t]=String(n[t]))})),e.translate||(e.translate="no"),[oo.node(0,t.state.doc.content.size,e)]}function zo(t){if(t.markCursor){let e=document.createElement("img");e.className="ProseMirror-separator",e.setAttribute("mark-placeholder","true"),e.setAttribute("alt",""),t.cursorWrapper={dom:e,deco:oo.widget(t.state.selection.from,e,{raw:!0,marks:t.markCursor})}}else t.cursorWrapper=null}function Lo(t){return!t.someProp("editable",(e=>!1===e(t.state)))}function $o(t){let e=Object.create(null);function n(t){for(let n in t)Object.prototype.hasOwnProperty.call(e,n)||(e[n]=t[n])}return t.someProp("nodeViews",n),t.someProp("markViews",n),e}function Bo(t){if(t.spec.state||t.spec.filterTransaction||t.spec.appendTransaction)throw new RangeError("Plugins passed directly to the view must not have a state component")}for(var Fo={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},Vo={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},jo="undefined"!=typeof navigator&&/Mac/.test(navigator.platform),Ho="undefined"!=typeof navigator&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent),Wo=0;Wo<10;Wo++)Fo[48+Wo]=Fo[96+Wo]=String(Wo);for(Wo=1;Wo<=24;Wo++)Fo[Wo+111]="F"+Wo;for(Wo=65;Wo<=90;Wo++)Fo[Wo]=String.fromCharCode(Wo+32),Vo[Wo]=String.fromCharCode(Wo);for(var qo in Fo)Vo.hasOwnProperty(qo)||(Vo[qo]=Fo[qo]);const Jo="undefined"!=typeof navigator&&/Mac|iP(hone|[oa]d)/.test(navigator.platform);function Ko(t){let e,n,r,o,i=t.split(/-(?!$)/),s=i[i.length-1];"Space"==s&&(s=" ");for(let t=0;t<i.length-1;t++){let s=i[t];if(/^(cmd|meta|m)$/i.test(s))o=!0;else if(/^a(lt)?$/i.test(s))e=!0;else if(/^(c|ctrl|control)$/i.test(s))n=!0;else if(/^s(hift)?$/i.test(s))r=!0;else{if(!/^mod$/i.test(s))throw new Error("Unrecognized modifier name: "+s);Jo?o=!0:n=!0}}return e&&(s="Alt-"+s),n&&(s="Ctrl-"+s),o&&(s="Meta-"+s),r&&(s="Shift-"+s),s}function _o(t,e,n=!0){return e.altKey&&(t="Alt-"+t),e.ctrlKey&&(t="Ctrl-"+t),e.metaKey&&(t="Meta-"+t),n&&e.shiftKey&&(t="Shift-"+t),t}function Uo(t){let e=function(t){let e=Object.create(null);for(let n in t)e[Ko(n)]=t[n];return e}(t);return function(t,n){let r,o=function(t){var e=!(jo&&t.metaKey&&t.shiftKey&&!t.ctrlKey&&!t.altKey||Ho&&t.shiftKey&&t.key&&1==t.key.length||"Unidentified"==t.key)&&t.key||(t.shiftKey?Vo:Fo)[t.keyCode]||t.key||"Unidentified";return"Esc"==e&&(e="Escape"),"Del"==e&&(e="Delete"),"Left"==e&&(e="ArrowLeft"),"Up"==e&&(e="ArrowUp"),"Right"==e&&(e="ArrowRight"),"Down"==e&&(e="ArrowDown"),e}(n),i=e[_o(o,n)];if(i&&i(t.state,t.dispatch,t))return!0;if(1==o.length&&" "!=o){if(n.shiftKey){let r=e[_o(o,n,!1)];if(r&&r(t.state,t.dispatch,t))return!0}if((n.shiftKey||n.altKey||n.metaKey||o.charCodeAt(0)>127)&&(r=Fo[n.keyCode])&&r!=o){let o=e[_o(r,n)];if(o&&o(t.state,t.dispatch,t))return!0}}return!1}}function Go(t,e){let{$cursor:n}=t.selection;return!n||(e?!e.endOfTextblock("backward",t):n.parentOffset>0)?null:n}function Qo(t,e,n){let r=e.nodeBefore,o=e.pos-1;for(;!r.isTextblock;o--){if(r.type.spec.isolating)return!1;let t=r.lastChild;if(!t)return!1;r=t}let i=e.nodeAfter,s=e.pos+1;for(;!i.isTextblock;s++){if(i.type.spec.isolating)return!1;let t=i.firstChild;if(!t)return!1;i=t}let l=Bt(t.doc,o,s,h.empty);if(!l||l.from!=o||l instanceof Mt&&l.slice.size>=s-o)return!1;if(n){let e=t.tr.step(l);e.setSelection(re.create(e.doc,o)),n(e.scrollIntoView())}return!0}function Yo(t,e,n=!1){for(let r=t;r;r="start"==e?r.firstChild:r.lastChild){if(r.isTextblock)return!0;if(n&&1!=r.childCount)return!1}return!1}function Xo(t){if(!t.parent.type.spec.isolating)for(let e=t.depth-1;e>=0;e--){if(t.index(e)>0)return t.doc.resolve(t.before(e+1));if(t.node(e).type.spec.isolating)break}return null}function Zo(t,e){let{$cursor:n}=t.selection;return!n||(e?!e.endOfTextblock("forward",t):n.parentOffset<n.parent.content.size)?null:n}function ti(t){if(!t.parent.type.spec.isolating)for(let e=t.depth-1;e>=0;e--){let n=t.node(e);if(t.index(e)+1<n.childCount)return t.doc.resolve(t.after(e+1));if(n.type.spec.isolating)break}return null}function ei(t){for(let e=0;e<t.edgeCount;e++){let{type:n}=t.edge(e);if(n.isTextblock&&!n.hasRequiredAttrs())return n}return null}function ni(t,e,n,r){let i,s,l=e.nodeBefore,a=e.nodeAfter,c=l.type.spec.isolating||a.type.spec.isolating;if(!c&&function(t,e,n){let r=e.nodeBefore,o=e.nodeAfter,i=e.index();return!(!(r&&o&&r.type.compatibleContent(o.type))||(!r.content.size&&e.parent.canReplace(i-1,i)?(n&&n(t.tr.delete(e.pos-r.nodeSize,e.pos).scrollIntoView()),0):!e.parent.canReplace(i,i+1)||!o.isTextblock&&!zt(t.doc,e.pos)||(n&&n(t.tr.join(e.pos).scrollIntoView()),0)))}(t,e,n))return!0;let d=!c&&e.parent.canReplace(e.index(),e.index()+1);if(d&&(i=(s=l.contentMatchAt(l.childCount)).findWrapping(a.type))&&s.matchType(i[0]||a.type).validEnd){if(n){let r=e.pos+a.nodeSize,s=o.empty;for(let t=i.length-1;t>=0;t--)s=o.from(i[t].create(null,s));s=o.from(l.copy(s));let c=t.tr.step(new Ct(e.pos-1,r,e.pos,r,new h(s,1,0),i.length,!0)),d=c.doc.resolve(r+2*i.length);d.nodeAfter&&d.nodeAfter.type==l.type&&zt(c.doc,d.pos)&&c.join(d.pos),n(c.scrollIntoView())}return!0}let p=a.type.spec.isolating||r>0&&c?null:Zt.findFrom(e,1),u=p&&p.$from.blockRange(p.$to),f=u&&Nt(u);if(null!=f&&f>=e.depth)return n&&n(t.tr.lift(u,f).scrollIntoView()),!0;if(d&&Yo(a,"start",!0)&&Yo(l,"end")){let r=l,i=[];for(;i.push(r),!r.isTextblock;)r=r.lastChild;let s=a,c=1;for(;!s.isTextblock;s=s.firstChild)c++;if(r.canReplace(r.childCount,r.childCount,s.content)){if(n){let r=o.empty;for(let t=i.length-1;t>=0;t--)r=o.from(i[t].copy(r));n(t.tr.step(new Ct(e.pos-i.length,e.pos+a.nodeSize,e.pos+c,e.pos+a.nodeSize-c,new h(r,i.length,0),0,!0)).scrollIntoView())}return!0}}return!1}function ri(t){return function(e,n){let r=e.selection,o=t<0?r.$from:r.$to,i=o.depth;for(;o.node(i).isInline;){if(!i)return!1;i--}return!!o.node(i).isTextblock&&(n&&n(e.tr.setSelection(re.create(e.doc,t<0?o.start(i):o.end(i)))),!0)}}const oi=ri(-1),ii=ri(1);function si(t,e=null){return function(n,r){let o=!1;for(let r=0;r<n.selection.ranges.length&&!o;r++){let{$from:{pos:i},$to:{pos:s}}=n.selection.ranges[r];n.doc.nodesBetween(i,s,((r,i)=>{if(o)return!1;if(r.isTextblock&&!r.hasMarkup(t,e))if(r.type==t)o=!0;else{let e=n.doc.resolve(i),r=e.index();o=e.parent.canReplaceWith(r,r+1,t)}}))}if(!o)return!1;if(r){let o=n.tr;for(let r=0;r<n.selection.ranges.length;r++){let{$from:{pos:i},$to:{pos:s}}=n.selection.ranges[r];o.setBlockType(i,s,t,e)}r(o.scrollIntoView())}return!0}}function li(t,e=null){return function(n,r){let{$from:i,$to:s}=n.selection,l=i.blockRange(s);if(!l)return!1;let a=r?n.tr:null;return!!function(t,e,n,r=null){let i=!1,s=e,l=e.$from.doc;if(e.depth>=2&&e.$from.node(e.depth-1).type.compatibleContent(n)&&0==e.startIndex){if(0==e.$from.index(e.depth-1))return!1;let t=l.resolve(e.start-2);s=new O(t,t,e.depth),e.endIndex<e.parent.childCount&&(e=new O(e.$from,l.resolve(e.$to.end(e.depth)),e.depth)),i=!0}let a=At(s,n,r,e);if(!a)return!1;t&&function(t,e,n,r,i){let s=o.empty;for(let t=n.length-1;t>=0;t--)s=o.from(n[t].type.create(n[t].attrs,s));t.step(new Ct(e.start-(r?2:0),e.end,e.start,e.end,new h(s,0,0),n.length,!0));let l=0;for(let t=0;t<n.length;t++)n[t].type==i&&(l=t+1);let a=n.length-l,c=e.start+n.length-(r?2:0),d=e.parent;for(let n=e.startIndex,r=e.endIndex,o=!0;n<r;n++,o=!1)!o&&Pt(t.doc,c,a)&&(t.split(c,a),c+=2*a),c+=d.child(n).nodeSize}(t,e,a,i,n);return!0}(a,l,t,e)&&(r&&r(a.scrollIntoView()),!0)}}function ai(t){return function(e,n){let{$from:r,$to:i}=e.selection,s=r.blockRange(i,(e=>e.childCount>0&&e.firstChild.type==t));return!!s&&(!n||(r.node(s.depth-1).type==t?function(t,e,n,r){let i=t.tr,s=r.end,l=r.$to.end(r.depth);s<l&&(i.step(new Ct(s-1,l,s,l,new h(o.from(n.create(null,r.parent.copy())),1,0),1,!0)),r=new O(i.doc.resolve(r.$from.pos),i.doc.resolve(l),r.depth));const a=Nt(r);if(null==a)return!1;i.lift(r,a);let c=i.mapping.map(s,-1)-1;zt(i.doc,c)&&i.join(c);return e(i.scrollIntoView()),!0}(e,n,t,s):function(t,e,n){let r=t.tr,i=n.parent;for(let t=n.end,e=n.endIndex-1,o=n.startIndex;e>o;e--)t-=i.child(e).nodeSize,r.delete(t-1,t+1);let s=r.doc.resolve(n.start),l=s.nodeAfter;if(r.mapping.map(n.end)!=n.start+s.nodeAfter.nodeSize)return!1;let a=0==n.startIndex,c=n.endIndex==i.childCount,d=s.node(-1),p=s.index(-1);if(!d.canReplace(p+(a?0:1),p+1,l.content.append(c?o.empty:o.from(i))))return!1;let u=s.pos,f=u+l.nodeSize;return r.step(new Ct(u-(a?1:0),f+(c?1:0),u+1,f-1,new h((a?o.empty:o.from(i.copy(o.empty))).append(c?o.empty:o.from(i.copy(o.empty))),a?0:1,c?0:1),a?0:1)),e(r.scrollIntoView()),!0}(e,n,s)))}}function ci(t){const{state:e,transaction:n}=t;let{selection:r}=n,{doc:o}=n,{storedMarks:i}=n;return{...e,apply:e.apply.bind(e),applyTransaction:e.applyTransaction.bind(e),plugins:e.plugins,schema:e.schema,reconfigure:e.reconfigure.bind(e),toJSON:e.toJSON.bind(e),get storedMarks(){return i},get selection(){return r},get doc(){return o},get tr(){return r=n.selection,o=n.doc,i=n.storedMarks,n}}}"undefined"!=typeof navigator?/Mac|iP(hone|[oa]d)/.test(navigator.platform):"undefined"!=typeof os&&os.platform&&os.platform();class hi{constructor(t){this.editor=t.editor,this.rawCommands=this.editor.extensionManager.commands,this.customState=t.state}get hasCustomState(){return!!this.customState}get state(){return this.customState||this.editor.state}get commands(){const{rawCommands:t,editor:e,state:n}=this,{view:r}=e,{tr:o}=n,i=this.buildProps(o);return Object.fromEntries(Object.entries(t).map((([t,e])=>[t,(...t)=>{const n=e(...t)(i);return o.getMeta("preventDispatch")||this.hasCustomState||r.dispatch(o),n}])))}get chain(){return()=>this.createChain()}get can(){return()=>this.createCan()}createChain(t,e=!0){const{rawCommands:n,editor:r,state:o}=this,{view:i}=r,s=[],l=!!t,a=t||o.tr,c={...Object.fromEntries(Object.entries(n).map((([t,n])=>[t,(...t)=>{const r=this.buildProps(a,e),o=n(...t)(r);return s.push(o),c}]))),run:()=>(l||!e||a.getMeta("preventDispatch")||this.hasCustomState||i.dispatch(a),s.every((t=>!0===t)))};return c}createCan(t){const{rawCommands:e,state:n}=this,r=!1,o=t||n.tr,i=this.buildProps(o,r),s=Object.fromEntries(Object.entries(e).map((([t,e])=>[t,(...t)=>e(...t)({...i,dispatch:void 0})])));return{...s,chain:()=>this.createChain(o,r)}}buildProps(t,e=!0){const{rawCommands:n,editor:r,state:o}=this,{view:i}=r,s={tr:t,editor:r,view:i,state:ci({state:o,transaction:t}),dispatch:e?()=>{}:void 0,chain:()=>this.createChain(t,e),can:()=>this.createCan(t),get commands(){return Object.fromEntries(Object.entries(n).map((([t,e])=>[t,(...t)=>e(...t)(s)])))}};return s}}class di{constructor(){this.callbacks={}}on(t,e){return this.callbacks[t]||(this.callbacks[t]=[]),this.callbacks[t].push(e),this}emit(t,...e){const n=this.callbacks[t];return n&&n.forEach((t=>t.apply(this,e))),this}off(t,e){const n=this.callbacks[t];return n&&(e?this.callbacks[t]=n.filter((t=>t!==e)):delete this.callbacks[t]),this}once(t,e){const n=(...r)=>{this.off(t,n),e.apply(this,r)};return this.on(t,n)}removeAllListeners(){this.callbacks={}}}function pi(t,e,n){if(void 0===t.config[e]&&t.parent)return pi(t.parent,e,n);if("function"==typeof t.config[e]){return t.config[e].bind({...n,parent:t.parent?pi(t.parent,e,n):null})}return t.config[e]}function ui(t){return{baseExtensions:t.filter((t=>"extension"===t.type)),nodeExtensions:t.filter((t=>"node"===t.type)),markExtensions:t.filter((t=>"mark"===t.type))}}function fi(t){const e=[],{nodeExtensions:n,markExtensions:r}=ui(t),o=[...n,...r],i={default:null,rendered:!0,renderHTML:null,parseHTML:null,keepOnSplit:!0,isRequired:!1};return t.forEach((t=>{const n=pi(t,"addGlobalAttributes",{name:t.name,options:t.options,storage:t.storage,extensions:o});if(!n)return;n().forEach((t=>{t.types.forEach((n=>{Object.entries(t.attributes).forEach((([t,r])=>{e.push({type:n,name:t,attribute:{...i,...r}})}))}))}))})),o.forEach((t=>{const n={name:t.name,options:t.options,storage:t.storage},r=pi(t,"addAttributes",n);if(!r)return;const o=r();Object.entries(o).forEach((([n,r])=>{const o={...i,...r};"function"==typeof(null==o?void 0:o.default)&&(o.default=o.default()),(null==o?void 0:o.isRequired)&&void 0===(null==o?void 0:o.default)&&delete o.default,e.push({type:t.name,name:n,attribute:o})}))})),e}function mi(t,e){if("string"==typeof t){if(!e.nodes[t])throw Error(`There is no node type named '${t}'. Maybe you forgot to add the extension?`);return e.nodes[t]}return t}function gi(...t){return t.filter((t=>!!t)).reduce(((t,e)=>{const n={...t};return Object.entries(e).forEach((([t,e])=>{if(n[t])if("class"===t){const r=e?e.split(" "):[],o=n[t]?n[t].split(" "):[],i=r.filter((t=>!o.includes(t)));n[t]=[...o,...i].join(" ")}else if("style"===t){const r=e?e.split(";").map((t=>t.trim())).filter(Boolean):[],o=n[t]?n[t].split(";").map((t=>t.trim())).filter(Boolean):[],i=new Map;o.forEach((t=>{const[e,n]=t.split(":").map((t=>t.trim()));i.set(e,n)})),r.forEach((t=>{const[e,n]=t.split(":").map((t=>t.trim()));i.set(e,n)})),n[t]=Array.from(i.entries()).map((([t,e])=>`${t}: ${e}`)).join("; ")}else n[t]=e;else n[t]=e})),n}),{})}function yi(t,e){return e.filter((e=>e.type===t.type.name)).filter((t=>t.attribute.rendered)).map((e=>e.attribute.renderHTML?e.attribute.renderHTML(t.attrs)||{}:{[e.name]:t.attrs[e.name]})).reduce(((t,e)=>gi(t,e)),{})}function wi(t){return"function"==typeof t}function vi(t,e=void 0,...n){return wi(t)?e?t.bind(e)(...n):t(...n):t}function ki(t,e){return"style"in t?t:{...t,getAttrs:n=>{const r=t.getAttrs?t.getAttrs(n):t.attrs;if(!1===r)return!1;const o=e.reduce(((t,e)=>{const r=e.attribute.parseHTML?e.attribute.parseHTML(n):function(t){return"string"!=typeof t?t:t.match(/^[+-]?(?:\d*\.)?\d+$/)?Number(t):"true"===t||"false"!==t&&t}(n.getAttribute(e.name));return null==r?t:{...t,[e.name]:r}}),{});return{...r,...o}}}}function bi(t){return Object.fromEntries(Object.entries(t).filter((([t,e])=>("attrs"!==t||!function(t={}){return 0===Object.keys(t).length&&t.constructor===Object}(e))&&null!=e)))}function xi(t,e){return e.nodes[t]||e.marks[t]||null}function Si(t,e){return Array.isArray(e)?e.some((e=>("string"==typeof e?e:e.name)===t.name)):e}function Mi(t,e){const n=it.fromSchema(e).serializeFragment(t),r=document.implementation.createHTMLDocument().createElement("div");return r.appendChild(n),r.innerHTML}function Ci(t){return"[object RegExp]"===Object.prototype.toString.call(t)}class Oi{constructor(t){this.find=t.find,this.handler=t.handler}}function Ti(t){var e;const{editor:n,from:r,to:o,text:i,rules:s,plugin:l}=t,{view:a}=n;if(a.composing)return!1;const c=a.state.doc.resolve(r);if(c.parent.type.spec.code||(null===(e=c.nodeBefore||c.nodeAfter)||void 0===e?void 0:e.marks.find((t=>t.type.spec.code))))return!1;let h=!1;const d=((t,e=500)=>{let n="";const r=t.parentOffset;return t.parent.nodesBetween(Math.max(0,r-e),r,((t,e,o,i)=>{var s,l;const a=(null===(l=(s=t.type.spec).toText)||void 0===l?void 0:l.call(s,{node:t,pos:e,parent:o,index:i}))||t.textContent||"%leaf%";n+=t.isAtom&&!t.isText?a:a.slice(0,Math.max(0,r-e))})),n})(c)+i;return s.forEach((t=>{if(h)return;const e=((t,e)=>{if(Ci(e))return e.exec(t);const n=e(t);if(!n)return null;const r=[n.text];return r.index=n.index,r.input=t,r.data=n.data,n.replaceWith&&(n.text.includes(n.replaceWith)||console.warn('[tiptap warn]: "inputRuleMatch.replaceWith" must be part of "inputRuleMatch.text".'),r.push(n.replaceWith)),r})(d,t.find);if(!e)return;const s=a.state.tr,c=ci({state:a.state,transaction:s}),p={from:r-(e[0].length-i.length),to:o},{commands:u,chain:f,can:m}=new hi({editor:n,state:c});null!==t.handler({state:c,range:p,match:e,commands:u,chain:f,can:m})&&s.steps.length&&(s.setMeta(l,{transform:s,from:r,to:o,text:i}),a.dispatch(s),h=!0)})),h}function Ei(t){const{editor:e,rules:n}=t,r=new we({state:{init:()=>null,apply(t,i,s){const l=t.getMeta(r);if(l)return l;const a=t.getMeta("applyInputRules");return!!a&&setTimeout((()=>{let{text:t}=a;"string"==typeof t||(t=Mi(o.from(t),s.schema));const{from:i}=a,l=i+t.length;Ti({editor:e,from:i,to:l,text:t,rules:n,plugin:r})})),t.selectionSet||t.docChanged?null:i}},props:{handleTextInput:(t,o,i,s)=>Ti({editor:e,from:o,to:i,text:s,rules:n,plugin:r}),handleDOMEvents:{compositionend:t=>(setTimeout((()=>{const{$cursor:o}=t.state.selection;o&&Ti({editor:e,from:o.pos,to:o.pos,text:"",rules:n,plugin:r})})),!1)},handleKeyDown(t,o){if("Enter"!==o.key)return!1;const{$cursor:i}=t.state.selection;return!!i&&Ti({editor:e,from:i.pos,to:i.pos,text:"\n",rules:n,plugin:r})}},isInputRules:!0});return r}function Ni(t){return"Object"===function(t){return Object.prototype.toString.call(t).slice(8,-1)}(t)&&(t.constructor===Object&&Object.getPrototypeOf(t)===Object.prototype)}function Ai(t,e){const n={...t};return Ni(t)&&Ni(e)&&Object.keys(e).forEach((r=>{Ni(e[r])&&Ni(t[r])?n[r]=Ai(t[r],e[r]):n[r]=e[r]})),n}class Di{constructor(t={}){this.type="mark",this.name="mark",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...t},this.name=this.config.name,t.defaultOptions&&Object.keys(t.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=vi(pi(this,"addOptions",{name:this.name}))),this.storage=vi(pi(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(t={}){return new Di(t)}configure(t={}){const e=this.extend({...this.config,addOptions:()=>Ai(this.options,t)});return e.name=this.name,e.parent=this.parent,e}extend(t={}){const e=new Di(t);return e.parent=this,this.child=e,e.name=t.name?t.name:e.parent.name,t.defaultOptions&&Object.keys(t.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${e.name}".`),e.options=vi(pi(e,"addOptions",{name:e.name})),e.storage=vi(pi(e,"addStorage",{name:e.name,options:e.options})),e}static handleExit({editor:t,mark:e}){const{tr:n}=t.state,r=t.state.selection.$from;if(r.pos===r.end()){const o=r.marks();if(!!!o.find((t=>(null==t?void 0:t.type.name)===e.name)))return!1;const i=o.find((t=>(null==t?void 0:t.type.name)===e.name));return i&&n.removeStoredMark(i),n.insertText(" ",r.pos),t.view.dispatch(n),!0}return!1}}class Ri{constructor(t){this.find=t.find,this.handler=t.handler}}function Ii(t){const{editor:e,state:n,from:r,to:o,rule:i,pasteEvent:s,dropEvent:l}=t,{commands:a,chain:c,can:h}=new hi({editor:e,state:n}),d=[];n.doc.nodesBetween(r,o,((t,e)=>{if(!t.isTextblock||t.type.spec.code)return;const p=Math.max(r,e),u=Math.min(o,e+t.content.size),f=((t,e,n)=>{if(Ci(e))return[...t.matchAll(e)];const r=e(t,n);return r?r.map((e=>{const n=[e.text];return n.index=e.index,n.input=t,n.data=e.data,e.replaceWith&&(e.text.includes(e.replaceWith)||console.warn('[tiptap warn]: "pasteRuleMatch.replaceWith" must be part of "pasteRuleMatch.text".'),n.push(e.replaceWith)),n})):[]})(t.textBetween(p-e,u-e,void 0,"￼"),i.find,s);f.forEach((t=>{if(void 0===t.index)return;const e=p+t.index+1,r=e+t[0].length,o={from:n.tr.mapping.map(e),to:n.tr.mapping.map(r)},u=i.handler({state:n,range:o,match:t,commands:a,chain:c,can:h,pasteEvent:s,dropEvent:l});d.push(u)}))}));return d.every((t=>null!==t))}let Pi=null;function zi(t){const{editor:e,rules:n}=t;let r,i=null,s=!1,l=!1,a="undefined"!=typeof ClipboardEvent?new ClipboardEvent("paste"):null;try{r="undefined"!=typeof DragEvent?new DragEvent("drop"):null}catch(t){r=null}const c=({state:t,from:n,to:o,rule:i,pasteEvt:s})=>{const l=t.tr,c=ci({state:t,transaction:l});if(Ii({editor:e,state:c,from:Math.max(n-1,0),to:o.b-1,rule:i,pasteEvent:s,dropEvent:r})&&l.steps.length){try{r="undefined"!=typeof DragEvent?new DragEvent("drop"):null}catch(t){r=null}return a="undefined"!=typeof ClipboardEvent?new ClipboardEvent("paste"):null,l}};return n.map((t=>new we({view(t){const n=n=>{var r;i=(null===(r=t.dom.parentElement)||void 0===r?void 0:r.contains(n.target))?t.dom.parentElement:null,i&&(Pi=e)},r=()=>{Pi&&(Pi=null)};return window.addEventListener("dragstart",n),window.addEventListener("dragend",r),{destroy(){window.removeEventListener("dragstart",n),window.removeEventListener("dragend",r)}}},props:{handleDOMEvents:{drop:(t,e)=>{if(l=i===t.dom.parentElement,r=e,!l){const t=Pi;t&&setTimeout((()=>{const e=t.state.selection;e&&t.commands.deleteRange({from:e.from,to:e.to})}),10)}return!1},paste:(t,e)=>{var n;const r=null===(n=e.clipboardData)||void 0===n?void 0:n.getData("text/html");return a=e,s=!!(null==r?void 0:r.includes("data-pm-slice")),!1}}},appendTransaction:(e,n,r)=>{const i=e[0],h="paste"===i.getMeta("uiEvent")&&!s,d="drop"===i.getMeta("uiEvent")&&!l,p=i.getMeta("applyPasteRules"),u=!!p;if(!h&&!d&&!u)return;if(u){let{text:e}=p;"string"==typeof e||(e=Mi(o.from(e),r.schema));const{from:n}=p,i=n+e.length,s=(t=>{var e;const n=new ClipboardEvent("paste",{clipboardData:new DataTransfer});return null===(e=n.clipboardData)||void 0===e||e.setData("text/html",t),n})(e);return c({rule:t,state:r,from:n,to:{b:i},pasteEvt:s})}const f=n.doc.content.findDiffStart(r.doc.content),m=n.doc.content.findDiffEnd(r.doc.content);return"number"==typeof f&&m&&f!==m.b?c({rule:t,state:r,from:f,to:m,pasteEvt:a}):void 0}})))}class Li{constructor(t,e){this.splittableMarks=[],this.editor=e,this.extensions=Li.resolve(t),this.schema=function(t,e){var n;const r=fi(t),{nodeExtensions:o,markExtensions:i}=ui(t),s=null===(n=o.find((t=>pi(t,"topNode"))))||void 0===n?void 0:n.name,l=Object.fromEntries(o.map((n=>{const o=r.filter((t=>t.type===n.name)),i={name:n.name,options:n.options,storage:n.storage,editor:e},s=bi({...t.reduce(((t,e)=>{const r=pi(e,"extendNodeSchema",i);return{...t,...r?r(n):{}}}),{}),content:vi(pi(n,"content",i)),marks:vi(pi(n,"marks",i)),group:vi(pi(n,"group",i)),inline:vi(pi(n,"inline",i)),atom:vi(pi(n,"atom",i)),selectable:vi(pi(n,"selectable",i)),draggable:vi(pi(n,"draggable",i)),code:vi(pi(n,"code",i)),whitespace:vi(pi(n,"whitespace",i)),linebreakReplacement:vi(pi(n,"linebreakReplacement",i)),defining:vi(pi(n,"defining",i)),isolating:vi(pi(n,"isolating",i)),attrs:Object.fromEntries(o.map((t=>{var e;return[t.name,{default:null===(e=null==t?void 0:t.attribute)||void 0===e?void 0:e.default}]})))}),l=vi(pi(n,"parseHTML",i));l&&(s.parseDOM=l.map((t=>ki(t,o))));const a=pi(n,"renderHTML",i);a&&(s.toDOM=t=>a({node:t,HTMLAttributes:yi(t,o)}));const c=pi(n,"renderText",i);return c&&(s.toText=c),[n.name,s]}))),a=Object.fromEntries(i.map((n=>{const o=r.filter((t=>t.type===n.name)),i={name:n.name,options:n.options,storage:n.storage,editor:e},s=bi({...t.reduce(((t,e)=>{const r=pi(e,"extendMarkSchema",i);return{...t,...r?r(n):{}}}),{}),inclusive:vi(pi(n,"inclusive",i)),excludes:vi(pi(n,"excludes",i)),group:vi(pi(n,"group",i)),spanning:vi(pi(n,"spanning",i)),code:vi(pi(n,"code",i)),attrs:Object.fromEntries(o.map((t=>{var e;return[t.name,{default:null===(e=null==t?void 0:t.attribute)||void 0===e?void 0:e.default}]})))}),l=vi(pi(n,"parseHTML",i));l&&(s.parseDOM=l.map((t=>ki(t,o))));const a=pi(n,"renderHTML",i);return a&&(s.toDOM=t=>a({mark:t,HTMLAttributes:yi(t,o)})),[n.name,s]})));return new _({topNode:s,nodes:l,marks:a})}(this.extensions,e),this.setupExtensions()}static resolve(t){const e=Li.sort(Li.flatten(t)),n=function(t){const e=t.filter(((e,n)=>t.indexOf(e)!==n));return Array.from(new Set(e))}(e.map((t=>t.name)));return n.length&&console.warn(`[tiptap warn]: Duplicate extension names found: [${n.map((t=>`'${t}'`)).join(", ")}]. This can lead to issues.`),e}static flatten(t){return t.map((t=>{const e=pi(t,"addExtensions",{name:t.name,options:t.options,storage:t.storage});return e?[t,...this.flatten(e())]:t})).flat(10)}static sort(t){return t.sort(((t,e)=>{const n=pi(t,"priority")||100,r=pi(e,"priority")||100;return n>r?-1:n<r?1:0}))}get commands(){return this.extensions.reduce(((t,e)=>{const n=pi(e,"addCommands",{name:e.name,options:e.options,storage:e.storage,editor:this.editor,type:xi(e.name,this.schema)});return n?{...t,...n()}:t}),{})}get plugins(){const{editor:t}=this,e=Li.sort([...this.extensions].reverse()),n=[],r=[],o=e.map((e=>{const o={name:e.name,options:e.options,storage:e.storage,editor:t,type:xi(e.name,this.schema)},i=[],s=pi(e,"addKeyboardShortcuts",o);let l={};if("mark"===e.type&&pi(e,"exitable",o)&&(l.ArrowRight=()=>Di.handleExit({editor:t,mark:e})),s){const e=Object.fromEntries(Object.entries(s()).map((([e,n])=>[e,()=>n({editor:t})])));l={...l,...e}}const a=new we({props:{handleKeyDown:Uo(l)}});i.push(a);const c=pi(e,"addInputRules",o);Si(e,t.options.enableInputRules)&&c&&n.push(...c());const h=pi(e,"addPasteRules",o);Si(e,t.options.enablePasteRules)&&h&&r.push(...h());const d=pi(e,"addProseMirrorPlugins",o);if(d){const t=d();i.push(...t)}return i})).flat();return[Ei({editor:t,rules:n}),...zi({editor:t,rules:r}),...o]}get attributes(){return fi(this.extensions)}get nodeViews(){const{editor:t}=this,{nodeExtensions:e}=ui(this.extensions);return Object.fromEntries(e.filter((t=>!!pi(t,"addNodeView"))).map((e=>{const n=this.attributes.filter((t=>t.type===e.name)),r={name:e.name,options:e.options,storage:e.storage,editor:t,type:mi(e.name,this.schema)},o=pi(e,"addNodeView",r);if(!o)return[];return[e.name,(r,i,s,l,a)=>{const c=yi(r,n);return o()({node:r,view:i,getPos:s,decorations:l,innerDecorations:a,editor:t,extension:e,HTMLAttributes:c})}]})))}setupExtensions(){this.extensions.forEach((t=>{var e;this.editor.extensionStorage[t.name]=t.storage;const n={name:t.name,options:t.options,storage:t.storage,editor:this.editor,type:xi(t.name,this.schema)};if("mark"===t.type){(null===(e=vi(pi(t,"keepOnSplit",n)))||void 0===e||e)&&this.splittableMarks.push(t.name)}const r=pi(t,"onBeforeCreate",n),o=pi(t,"onCreate",n),i=pi(t,"onUpdate",n),s=pi(t,"onSelectionUpdate",n),l=pi(t,"onTransaction",n),a=pi(t,"onFocus",n),c=pi(t,"onBlur",n),h=pi(t,"onDestroy",n);r&&this.editor.on("beforeCreate",r),o&&this.editor.on("create",o),i&&this.editor.on("update",i),s&&this.editor.on("selectionUpdate",s),l&&this.editor.on("transaction",l),a&&this.editor.on("focus",a),c&&this.editor.on("blur",c),h&&this.editor.on("destroy",h)}))}}class $i{constructor(t={}){this.type="extension",this.name="extension",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...t},this.name=this.config.name,t.defaultOptions&&Object.keys(t.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=vi(pi(this,"addOptions",{name:this.name}))),this.storage=vi(pi(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(t={}){return new $i(t)}configure(t={}){const e=this.extend({...this.config,addOptions:()=>Ai(this.options,t)});return e.name=this.name,e.parent=this.parent,e}extend(t={}){const e=new $i({...this.config,...t});return e.parent=this,this.child=e,e.name=t.name?t.name:e.parent.name,t.defaultOptions&&Object.keys(t.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${e.name}".`),e.options=vi(pi(e,"addOptions",{name:e.name})),e.storage=vi(pi(e,"addStorage",{name:e.name,options:e.options})),e}}function Bi(t,e,n){const{from:r,to:o}=e,{blockSeparator:i="\n\n",textSerializers:s={}}=n||{};let l="";return t.nodesBetween(r,o,((t,n,a,c)=>{var h;t.isBlock&&n>r&&(l+=i);const d=null==s?void 0:s[t.type.name];if(d)return a&&(l+=d({node:t,pos:n,parent:a,index:c,range:e})),!1;t.isText&&(l+=null===(h=null==t?void 0:t.text)||void 0===h?void 0:h.slice(Math.max(r,n)-n,o-n))})),l}function Fi(t){return Object.fromEntries(Object.entries(t.nodes).filter((([,t])=>t.spec.toText)).map((([t,e])=>[t,e.spec.toText])))}const Vi=$i.create({name:"clipboardTextSerializer",addOptions:()=>({blockSeparator:void 0}),addProseMirrorPlugins(){return[new we({key:new be("clipboardTextSerializer"),props:{clipboardTextSerializer:()=>{const{editor:t}=this,{state:e,schema:n}=t,{doc:r,selection:o}=e,{ranges:i}=o,s=Math.min(...i.map((t=>t.$from.pos))),l=Math.max(...i.map((t=>t.$to.pos))),a=Fi(n);return Bi(r,{from:s,to:l},{...void 0!==this.options.blockSeparator?{blockSeparator:this.options.blockSeparator}:{},textSerializers:a})}}})]}});function ji(t,e,n={strict:!0}){const r=Object.keys(e);return!r.length||r.every((r=>n.strict?e[r]===t[r]:Ci(e[r])?e[r].test(t[r]):e[r]===t[r]))}function Hi(t,e,n={}){return t.find((t=>t.type===e&&ji(Object.fromEntries(Object.keys(n).map((e=>[e,t.attrs[e]]))),n)))}function Wi(t,e,n={}){return!!Hi(t,e,n)}function qi(t,e,n){var r;if(!t||!e)return;let o=t.parent.childAfter(t.parentOffset);if(o.node&&o.node.marks.some((t=>t.type===e))||(o=t.parent.childBefore(t.parentOffset)),!o.node||!o.node.marks.some((t=>t.type===e)))return;n=n||(null===(r=o.node.marks[0])||void 0===r?void 0:r.attrs);if(!Hi([...o.node.marks],e,n))return;let i=o.index,s=t.start()+o.offset,l=i+1,a=s+o.node.nodeSize;for(;i>0&&Wi([...t.parent.child(i-1).marks],e,n);)i-=1,s-=t.parent.child(i).nodeSize;for(;l<t.parent.childCount&&Wi([...t.parent.child(l).marks],e,n);)a+=t.parent.child(l).nodeSize,l+=1;return{from:s,to:a}}function Ji(t,e){if("string"==typeof t){if(!e.marks[t])throw Error(`There is no mark type named '${t}'. Maybe you forgot to add the extension?`);return e.marks[t]}return t}function Ki(t){return t instanceof re}function _i(t=0,e=0,n=0){return Math.min(Math.max(t,e),n)}function Ui(t,e=null){if(!e)return null;const n=Zt.atStart(t),r=Zt.atEnd(t);if("start"===e||!0===e)return n;if("end"===e)return r;const o=n.from,i=r.to;return"all"===e?re.create(t,_i(0,o,i),_i(t.content.size,o,i)):re.create(t,_i(e,o,i),_i(e,o,i))}const Gi=t=>{const e=t.childNodes;for(let n=e.length-1;n>=0;n-=1){const r=e[n];3===r.nodeType&&r.nodeValue&&/^(\n\s\s|\n)$/.test(r.nodeValue)?t.removeChild(r):1===r.nodeType&&Gi(r)}return t};function Qi(t){const e=`<body>${t}</body>`,n=(new window.DOMParser).parseFromString(e,"text/html").body;return Gi(n)}function Yi(t,e,n){if(t instanceof E||t instanceof o)return t;n={slice:!0,parseOptions:{},...n};const r="string"==typeof t;if("object"==typeof t&&null!==t)try{if(Array.isArray(t)&&t.length>0)return o.fromArray(t.map((t=>e.nodeFromJSON(t))));const r=e.nodeFromJSON(t);return n.errorOnInvalidContent&&r.check(),r}catch(r){if(n.errorOnInvalidContent)throw new Error("[tiptap error]: Invalid JSON content",{cause:r});return console.warn("[tiptap warn]: Invalid content.","Passed value:",t,"Error:",r),Yi("",e,n)}if(r){if(n.errorOnInvalidContent){let r=!1,o="";const i=new _({topNode:e.spec.topNode,marks:e.spec.marks,nodes:e.spec.nodes.append({__tiptap__private__unknown__catch__all__node:{content:"inline*",group:"block",parseDOM:[{tag:"*",getAttrs:t=>(r=!0,o="string"==typeof t?t:t.outerHTML,null)}]}})});if(n.slice?G.fromSchema(i).parseSlice(Qi(t),n.parseOptions):G.fromSchema(i).parse(Qi(t),n.parseOptions),n.errorOnInvalidContent&&r)throw new Error("[tiptap error]: Invalid HTML content",{cause:new Error(`Invalid element found: ${o}`)})}const r=G.fromSchema(e);return n.slice?r.parseSlice(Qi(t),n.parseOptions).content:r.parse(Qi(t),n.parseOptions)}return Yi("",e,n)}function Xi(){return["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document}function Zi(){return"undefined"!=typeof navigator&&/Mac/.test(navigator.platform)}function ts(t,e,n={}){const{from:r,to:o,empty:i}=t.selection,s=e?mi(e,t.schema):null,l=[];t.doc.nodesBetween(r,o,((t,e)=>{if(t.isText)return;const n=Math.max(r,e),i=Math.min(o,e+t.nodeSize);l.push({node:t,from:n,to:i})}));const a=o-r,c=l.filter((t=>!s||s.name===t.node.type.name)).filter((t=>ji(t.node.attrs,n,{strict:!1})));if(i)return!!c.length;return c.reduce(((t,e)=>t+e.to-e.from),0)>=a}function es(t,e){return e.nodes[t]?"node":e.marks[t]?"mark":null}function ns(t,e){const n="string"==typeof e?[e]:e;return Object.keys(t).reduce(((e,r)=>(n.includes(r)||(e[r]=t[r]),e)),{})}function rs(t,e,n={},r={}){return Yi(t,e,{slice:!1,parseOptions:n,errorOnInvalidContent:r.errorOnInvalidContent})}function is(t,e){const n=Ji(e,t.schema),{from:r,to:o,empty:i}=t.selection,s=[];i?(t.storedMarks&&s.push(...t.storedMarks),s.push(...t.selection.$head.marks())):t.doc.nodesBetween(r,o,(t=>{s.push(...t.marks)}));const l=s.find((t=>t.type.name===n.name));return l?{...l.attrs}:{}}function ss(t){return e=>function(t,e){for(let n=t.depth;n>0;n-=1){const r=t.node(n);if(e(r))return{pos:n>0?t.before(n):0,start:t.start(n),depth:n,node:r}}}(e.$from,t)}function ls(t,e){const n=es("string"==typeof e?e:e.name,t.schema);return"node"===n?function(t,e){const n=mi(e,t.schema),{from:r,to:o}=t.selection,i=[];t.doc.nodesBetween(r,o,(t=>{i.push(t)}));const s=i.reverse().find((t=>t.type.name===n.name));return s?{...s.attrs}:{}}(t,e):"mark"===n?is(t,e):{}}function as(t){const e=function(t,e=JSON.stringify){const n={};return t.filter((t=>{const r=e(t);return!Object.prototype.hasOwnProperty.call(n,r)&&(n[r]=!0)}))}(t);return 1===e.length?e:e.filter(((t,n)=>{const r=e.filter(((t,e)=>e!==n));return!r.some((e=>t.oldRange.from>=e.oldRange.from&&t.oldRange.to<=e.oldRange.to&&t.newRange.from>=e.newRange.from&&t.newRange.to<=e.newRange.to))}))}function cs(t,e,n){const r=[];return t===e?n.resolve(t).marks().forEach((e=>{const o=qi(n.resolve(t),e.type);o&&r.push({mark:e,...o})})):n.nodesBetween(t,e,((t,e)=>{t&&void 0!==(null==t?void 0:t.nodeSize)&&r.push(...t.marks.map((n=>({from:e,to:e+t.nodeSize,mark:n}))))})),r}function hs(t,e,n){return Object.fromEntries(Object.entries(n).filter((([n])=>{const r=t.find((t=>t.type===e&&t.name===n));return!!r&&r.attribute.keepOnSplit})))}function ds(t,e,n={}){const{empty:r,ranges:o}=t.selection,i=e?Ji(e,t.schema):null;if(r)return!!(t.storedMarks||t.selection.$from.marks()).filter((t=>!i||i.name===t.type.name)).find((t=>ji(t.attrs,n,{strict:!1})));let s=0;const l=[];if(o.forEach((({$from:e,$to:n})=>{const r=e.pos,o=n.pos;t.doc.nodesBetween(r,o,((t,e)=>{if(!t.isText&&!t.marks.length)return;const n=Math.max(r,e),i=Math.min(o,e+t.nodeSize);s+=i-n,l.push(...t.marks.map((t=>({mark:t,from:n,to:i}))))}))})),0===s)return!1;const a=l.filter((t=>!i||i.name===t.mark.type.name)).filter((t=>ji(t.mark.attrs,n,{strict:!1}))).reduce(((t,e)=>t+e.to-e.from),0),c=l.filter((t=>!i||t.mark.type!==i&&t.mark.type.excludes(i))).reduce(((t,e)=>t+e.to-e.from),0);return(a>0?a+c:a)>=s}function ps(t,e){const{nodeExtensions:n}=ui(e),r=n.find((e=>e.name===t));if(!r)return!1;const o=vi(pi(r,"group",{name:r.name,options:r.options,storage:r.storage}));return"string"==typeof o&&o.split(" ").includes("list")}function us(t,{checkChildren:e=!0,ignoreWhitespace:n=!1}={}){var r;if(n){if("hardBreak"===t.type.name)return!0;if(t.isText)return/^\s*$/m.test(null!==(r=t.text)&&void 0!==r?r:"")}if(t.isText)return!t.text;if(t.isAtom||t.isLeaf)return!1;if(0===t.content.childCount)return!0;if(e){let r=!0;return t.content.forEach((t=>{!1!==r&&(us(t,{ignoreWhitespace:n,checkChildren:e})||(r=!1))})),r}return!1}function fs(t,e){const n=t.storedMarks||t.selection.$to.parentOffset&&t.selection.$from.marks();if(n){const r=n.filter((t=>null==e?void 0:e.includes(t.type.name)));t.tr.ensureMarks(r)}}const ms=(t,e)=>{const n=ss((t=>t.type===e))(t.selection);if(!n)return!0;const r=t.doc.resolve(Math.max(0,n.pos-1)).before(n.depth);if(void 0===r)return!0;const o=t.doc.nodeAt(r);return n.node.type!==(null==o?void 0:o.type)||!zt(t.doc,n.pos)||(t.join(n.pos),!0)},gs=(t,e)=>{const n=ss((t=>t.type===e))(t.selection);if(!n)return!0;const r=t.doc.resolve(n.start).after(n.depth);if(void 0===r)return!0;const o=t.doc.nodeAt(r);return n.node.type!==(null==o?void 0:o.type)||!zt(t.doc,r)||(t.join(r),!0)};var ys=Object.freeze({__proto__:null,blur:()=>({editor:t,view:e})=>(requestAnimationFrame((()=>{var n;t.isDestroyed||(e.dom.blur(),null===(n=null===window||void 0===window?void 0:window.getSelection())||void 0===n||n.removeAllRanges())})),!0),clearContent:(t=!1)=>({commands:e})=>e.setContent("",t),clearNodes:()=>({state:t,tr:e,dispatch:n})=>{const{selection:r}=e,{ranges:o}=r;return!n||(o.forEach((({$from:n,$to:r})=>{t.doc.nodesBetween(n.pos,r.pos,((t,n)=>{if(t.type.isText)return;const{doc:r,mapping:o}=e,i=r.resolve(o.map(n)),s=r.resolve(o.map(n+t.nodeSize)),l=i.blockRange(s);if(!l)return;const a=Nt(l);if(t.type.isTextblock){const{defaultType:t}=i.parent.contentMatchAt(i.index());e.setNodeMarkup(l.start,t)}(a||0===a)&&e.lift(l,a)}))})),!0)},command:t=>e=>t(e),createParagraphNear:()=>({state:t,dispatch:e})=>((t,e)=>{let n=t.selection,{$from:r,$to:o}=n;if(n instanceof le||r.parent.inlineContent||o.parent.inlineContent)return!1;let i=ei(o.parent.contentMatchAt(o.indexAfter()));if(!i||!i.isTextblock)return!1;if(e){let n=(!r.parentOffset&&o.index()<o.parent.childCount?r:o).pos,s=t.tr.insert(n,i.createAndFill());s.setSelection(re.create(s.doc,n+1)),e(s.scrollIntoView())}return!0})(t,e),cut:(t,e)=>({editor:n,tr:r})=>{const{state:o}=n,i=o.doc.slice(t.from,t.to);r.deleteRange(t.from,t.to);const s=r.mapping.map(e);return r.insert(s,i.content),r.setSelection(new re(r.doc.resolve(s-1))),!0},deleteCurrentNode:()=>({tr:t,dispatch:e})=>{const{selection:n}=t,r=n.$anchor.node();if(r.content.size>0)return!1;const o=t.selection.$anchor;for(let n=o.depth;n>0;n-=1){if(o.node(n).type===r.type){if(e){const e=o.before(n),r=o.after(n);t.delete(e,r).scrollIntoView()}return!0}}return!1},deleteNode:t=>({tr:e,state:n,dispatch:r})=>{const o=mi(t,n.schema),i=e.selection.$anchor;for(let t=i.depth;t>0;t-=1){if(i.node(t).type===o){if(r){const n=i.before(t),r=i.after(t);e.delete(n,r).scrollIntoView()}return!0}}return!1},deleteRange:t=>({tr:e,dispatch:n})=>{const{from:r,to:o}=t;return n&&e.delete(r,o),!0},deleteSelection:()=>({state:t,dispatch:e})=>((t,e)=>!t.selection.empty&&(e&&e(t.tr.deleteSelection().scrollIntoView()),!0))(t,e),enter:()=>({commands:t})=>t.keyboardShortcut("Enter"),exitCode:()=>({state:t,dispatch:e})=>((t,e)=>{let{$head:n,$anchor:r}=t.selection;if(!n.parent.type.spec.code||!n.sameParent(r))return!1;let o=n.node(-1),i=n.indexAfter(-1),s=ei(o.contentMatchAt(i));if(!s||!o.canReplaceWith(i,i,s))return!1;if(e){let r=n.after(),o=t.tr.replaceWith(r,r,s.createAndFill());o.setSelection(Zt.near(o.doc.resolve(r),1)),e(o.scrollIntoView())}return!0})(t,e),extendMarkRange:(t,e={})=>({tr:n,state:r,dispatch:o})=>{const i=Ji(t,r.schema),{doc:s,selection:l}=n,{$from:a,from:c,to:h}=l;if(o){const t=qi(a,i,e);if(t&&t.from<=c&&t.to>=h){const e=re.create(s,t.from,t.to);n.setSelection(e)}}return!0},first:t=>e=>{const n="function"==typeof t?t(e):t;for(let t=0;t<n.length;t+=1)if(n[t](e))return!0;return!1},focus:(t=null,e={})=>({editor:n,view:r,tr:o,dispatch:i})=>{e={scrollIntoView:!0,...e};const s=()=>{r.dom.focus(),requestAnimationFrame((()=>{n.isDestroyed||(r.focus(),(null==e?void 0:e.scrollIntoView)&&n.commands.scrollIntoView())}))};if(r.hasFocus()&&null===t||!1===t)return!0;if(i&&null===t&&!Ki(n.state.selection))return s(),!0;const l=Ui(o.doc,t)||n.state.selection,a=n.state.selection.eq(l);return i&&(a||o.setSelection(l),a&&o.storedMarks&&o.setStoredMarks(o.storedMarks),s()),!0},forEach:(t,e)=>n=>t.every(((t,r)=>e(t,{...n,index:r}))),insertContent:(t,e)=>({tr:n,commands:r})=>r.insertContentAt({from:n.selection.from,to:n.selection.to},t,e),insertContentAt:(t,e,n)=>({tr:r,dispatch:i,editor:s})=>{var l;if(i){let i;n={parseOptions:s.options.parseOptions,updateSelection:!0,applyInputRules:!1,applyPasteRules:!1,...n};try{i=Yi(e,s.schema,{parseOptions:{preserveWhitespace:"full",...n.parseOptions},errorOnInvalidContent:null!==(l=n.errorOnInvalidContent)&&void 0!==l?l:s.options.enableContentCheck})}catch(t){return s.emit("contentError",{editor:s,error:t,disableCollaboration:()=>{s.storage.collaboration&&(s.storage.collaboration.isDisabled=!0)}}),!1}let{from:a,to:c}="number"==typeof t?{from:t,to:t}:{from:t.from,to:t.to},h=!0,d=!0;if(("type"in i?[i]:i).forEach((t=>{t.check(),h=!!h&&(t.isText&&0===t.marks.length),d=!!d&&t.isBlock})),a===c&&d){const{parent:t}=r.doc.resolve(a);t.isTextblock&&!t.type.spec.code&&!t.childCount&&(a-=1,c+=1)}let p;if(h){if(Array.isArray(e))p=e.map((t=>t.text||"")).join("");else if(e instanceof o){let t="";e.forEach((e=>{e.text&&(t+=e.text)})),p=t}else p="object"==typeof e&&e&&e.text?e.text:e;r.insertText(p,a,c)}else p=i,r.replaceWith(a,c,p);n.updateSelection&&function(t,e,n){const r=t.steps.length-1;if(r<e)return;const o=t.steps[r];if(!(o instanceof Mt||o instanceof Ct))return;const i=t.mapping.maps[r];let s=0;i.forEach(((t,e,n,r)=>{0===s&&(s=r)})),t.setSelection(Zt.near(t.doc.resolve(s),n))}(r,r.steps.length-1,-1),n.applyInputRules&&r.setMeta("applyInputRules",{from:a,text:p}),n.applyPasteRules&&r.setMeta("applyPasteRules",{from:a,text:p})}return!0},joinBackward:()=>({state:t,dispatch:e})=>((t,e,n)=>{let r=Go(t,n);if(!r)return!1;let o=Xo(r);if(!o){let n=r.blockRange(),o=n&&Nt(n);return null!=o&&(e&&e(t.tr.lift(n,o).scrollIntoView()),!0)}let i=o.nodeBefore;if(ni(t,o,e,-1))return!0;if(0==r.parent.content.size&&(Yo(i,"end")||ie.isSelectable(i)))for(let n=r.depth;;n--){let s=Bt(t.doc,r.before(n),r.after(n),h.empty);if(s&&s.slice.size<s.to-s.from){if(e){let n=t.tr.step(s);n.setSelection(Yo(i,"end")?Zt.findFrom(n.doc.resolve(n.mapping.map(o.pos,-1)),-1):ie.create(n.doc,o.pos-i.nodeSize)),e(n.scrollIntoView())}return!0}if(1==n||r.node(n-1).childCount>1)break}return!(!i.isAtom||o.depth!=r.depth-1||(e&&e(t.tr.delete(o.pos-i.nodeSize,o.pos).scrollIntoView()),0))})(t,e),joinDown:()=>({state:t,dispatch:e})=>((t,e)=>{let n,r=t.selection;if(r instanceof ie){if(r.node.isTextblock||!zt(t.doc,r.to))return!1;n=r.to}else if(n=$t(t.doc,r.to,1),null==n)return!1;return e&&e(t.tr.join(n).scrollIntoView()),!0})(t,e),joinForward:()=>({state:t,dispatch:e})=>((t,e,n)=>{let r=Zo(t,n);if(!r)return!1;let o=ti(r);if(!o)return!1;let i=o.nodeAfter;if(ni(t,o,e,1))return!0;if(0==r.parent.content.size&&(Yo(i,"start")||ie.isSelectable(i))){let n=Bt(t.doc,r.before(),r.after(),h.empty);if(n&&n.slice.size<n.to-n.from){if(e){let r=t.tr.step(n);r.setSelection(Yo(i,"start")?Zt.findFrom(r.doc.resolve(r.mapping.map(o.pos)),1):ie.create(r.doc,r.mapping.map(o.pos))),e(r.scrollIntoView())}return!0}}return!(!i.isAtom||o.depth!=r.depth-1||(e&&e(t.tr.delete(o.pos,o.pos+i.nodeSize).scrollIntoView()),0))})(t,e),joinItemBackward:()=>({state:t,dispatch:e,tr:n})=>{try{const r=$t(t.doc,t.selection.$from.pos,-1);return null!=r&&(n.join(r,2),e&&e(n),!0)}catch(t){return!1}},joinItemForward:()=>({state:t,dispatch:e,tr:n})=>{try{const r=$t(t.doc,t.selection.$from.pos,1);return null!=r&&(n.join(r,2),e&&e(n),!0)}catch(t){return!1}},joinTextblockBackward:()=>({state:t,dispatch:e})=>((t,e,n)=>{let r=Go(t,n);if(!r)return!1;let o=Xo(r);return!!o&&Qo(t,o,e)})(t,e),joinTextblockForward:()=>({state:t,dispatch:e})=>((t,e,n)=>{let r=Zo(t,n);if(!r)return!1;let o=ti(r);return!!o&&Qo(t,o,e)})(t,e),joinUp:()=>({state:t,dispatch:e})=>((t,e)=>{let n,r=t.selection,o=r instanceof ie;if(o){if(r.node.isTextblock||!zt(t.doc,r.from))return!1;n=r.from}else if(n=$t(t.doc,r.from,-1),null==n)return!1;if(e){let r=t.tr.join(n);o&&r.setSelection(ie.create(r.doc,n-t.doc.resolve(n).nodeBefore.nodeSize)),e(r.scrollIntoView())}return!0})(t,e),keyboardShortcut:t=>({editor:e,view:n,tr:r,dispatch:o})=>{const i=function(t){const e=t.split(/-(?!$)/);let n,r,o,i,s=e[e.length-1];"Space"===s&&(s=" ");for(let t=0;t<e.length-1;t+=1){const s=e[t];if(/^(cmd|meta|m)$/i.test(s))i=!0;else if(/^a(lt)?$/i.test(s))n=!0;else if(/^(c|ctrl|control)$/i.test(s))r=!0;else if(/^s(hift)?$/i.test(s))o=!0;else{if(!/^mod$/i.test(s))throw new Error(`Unrecognized modifier name: ${s}`);Xi()||Zi()?i=!0:r=!0}}return n&&(s=`Alt-${s}`),r&&(s=`Ctrl-${s}`),i&&(s=`Meta-${s}`),o&&(s=`Shift-${s}`),s}(t).split(/-(?!$)/),s=i.find((t=>!["Alt","Ctrl","Meta","Shift"].includes(t))),l=new KeyboardEvent("keydown",{key:"Space"===s?" ":s,altKey:i.includes("Alt"),ctrlKey:i.includes("Ctrl"),metaKey:i.includes("Meta"),shiftKey:i.includes("Shift"),bubbles:!0,cancelable:!0}),a=e.captureTransaction((()=>{n.someProp("handleKeyDown",(t=>t(n,l)))}));return null==a||a.steps.forEach((t=>{const e=t.map(r.mapping);e&&o&&r.maybeStep(e)})),!0},lift:(t,e={})=>({state:n,dispatch:r})=>!!ts(n,mi(t,n.schema),e)&&((t,e)=>{let{$from:n,$to:r}=t.selection,o=n.blockRange(r),i=o&&Nt(o);return null!=i&&(e&&e(t.tr.lift(o,i).scrollIntoView()),!0)})(n,r),liftEmptyBlock:()=>({state:t,dispatch:e})=>((t,e)=>{let{$cursor:n}=t.selection;if(!n||n.parent.content.size)return!1;if(n.depth>1&&n.after()!=n.end(-1)){let r=n.before();if(Pt(t.doc,r))return e&&e(t.tr.split(r).scrollIntoView()),!0}let r=n.blockRange(),o=r&&Nt(r);return null!=o&&(e&&e(t.tr.lift(r,o).scrollIntoView()),!0)})(t,e),liftListItem:t=>({state:e,dispatch:n})=>ai(mi(t,e.schema))(e,n),newlineInCode:()=>({state:t,dispatch:e})=>((t,e)=>{let{$head:n,$anchor:r}=t.selection;return!(!n.parent.type.spec.code||!n.sameParent(r)||(e&&e(t.tr.insertText("\n").scrollIntoView()),0))})(t,e),resetAttributes:(t,e)=>({tr:n,state:r,dispatch:o})=>{let i=null,s=null;const l=es("string"==typeof t?t:t.name,r.schema);return!!l&&("node"===l&&(i=mi(t,r.schema)),"mark"===l&&(s=Ji(t,r.schema)),o&&n.selection.ranges.forEach((t=>{r.doc.nodesBetween(t.$from.pos,t.$to.pos,((t,r)=>{i&&i===t.type&&n.setNodeMarkup(r,void 0,ns(t.attrs,e)),s&&t.marks.length&&t.marks.forEach((o=>{s===o.type&&n.addMark(r,r+t.nodeSize,s.create(ns(o.attrs,e)))}))}))})),!0)},scrollIntoView:()=>({tr:t,dispatch:e})=>(e&&t.scrollIntoView(),!0),selectAll:()=>({tr:t,dispatch:e})=>{if(e){const e=new le(t.doc);t.setSelection(e)}return!0},selectNodeBackward:()=>({state:t,dispatch:e})=>((t,e,n)=>{let{$head:r,empty:o}=t.selection,i=r;if(!o)return!1;if(r.parent.isTextblock){if(n?!n.endOfTextblock("backward",t):r.parentOffset>0)return!1;i=Xo(r)}let s=i&&i.nodeBefore;return!(!s||!ie.isSelectable(s)||(e&&e(t.tr.setSelection(ie.create(t.doc,i.pos-s.nodeSize)).scrollIntoView()),0))})(t,e),selectNodeForward:()=>({state:t,dispatch:e})=>((t,e,n)=>{let{$head:r,empty:o}=t.selection,i=r;if(!o)return!1;if(r.parent.isTextblock){if(n?!n.endOfTextblock("forward",t):r.parentOffset<r.parent.content.size)return!1;i=ti(r)}let s=i&&i.nodeAfter;return!(!s||!ie.isSelectable(s)||(e&&e(t.tr.setSelection(ie.create(t.doc,i.pos)).scrollIntoView()),0))})(t,e),selectParentNode:()=>({state:t,dispatch:e})=>((t,e)=>{let n,{$from:r,to:o}=t.selection,i=r.sharedDepth(o);return 0!=i&&(n=r.before(i),e&&e(t.tr.setSelection(ie.create(t.doc,n))),!0)})(t,e),selectTextblockEnd:()=>({state:t,dispatch:e})=>ii(t,e),selectTextblockStart:()=>({state:t,dispatch:e})=>oi(t,e),setContent:(t,e=!1,n={},r={})=>({editor:o,tr:i,dispatch:s,commands:l})=>{var a,c;const{doc:h}=i;if("full"!==n.preserveWhitespace){const l=rs(t,o.schema,n,{errorOnInvalidContent:null!==(a=r.errorOnInvalidContent)&&void 0!==a?a:o.options.enableContentCheck});return s&&i.replaceWith(0,h.content.size,l).setMeta("preventUpdate",!e),!0}return s&&i.setMeta("preventUpdate",!e),l.insertContentAt({from:0,to:h.content.size},t,{parseOptions:n,errorOnInvalidContent:null!==(c=r.errorOnInvalidContent)&&void 0!==c?c:o.options.enableContentCheck})},setMark:(t,e={})=>({tr:n,state:r,dispatch:o})=>{const{selection:i}=n,{empty:s,ranges:l}=i,a=Ji(t,r.schema);if(o)if(s){const t=is(r,a);n.addStoredMark(a.create({...t,...e}))}else l.forEach((t=>{const o=t.$from.pos,i=t.$to.pos;r.doc.nodesBetween(o,i,((t,r)=>{const s=Math.max(r,o),l=Math.min(r+t.nodeSize,i);t.marks.find((t=>t.type===a))?t.marks.forEach((t=>{a===t.type&&n.addMark(s,l,a.create({...t.attrs,...e}))})):n.addMark(s,l,a.create(e))}))}));return function(t,e,n){var r;const{selection:o}=e;let i=null;if(Ki(o)&&(i=o.$cursor),i){const e=null!==(r=t.storedMarks)&&void 0!==r?r:i.marks();return!!n.isInSet(e)||!e.some((t=>t.type.excludes(n)))}const{ranges:s}=o;return s.some((({$from:e,$to:r})=>{let o=0===e.depth&&t.doc.inlineContent&&t.doc.type.allowsMarkType(n);return t.doc.nodesBetween(e.pos,r.pos,((t,e,r)=>{if(o)return!1;if(t.isInline){const e=!r||r.type.allowsMarkType(n),i=!!n.isInSet(t.marks)||!t.marks.some((t=>t.type.excludes(n)));o=e&&i}return!o})),o}))}(r,n,a)},setMeta:(t,e)=>({tr:n})=>(n.setMeta(t,e),!0),setNode:(t,e={})=>({state:n,dispatch:r,chain:o})=>{const i=mi(t,n.schema);let s;return n.selection.$anchor.sameParent(n.selection.$head)&&(s=n.selection.$anchor.parent.attrs),i.isTextblock?o().command((({commands:t})=>!!si(i,{...s,...e})(n)||t.clearNodes())).command((({state:t})=>si(i,{...s,...e})(t,r))).run():(console.warn('[tiptap warn]: Currently "setNode()" only supports text block nodes.'),!1)},setNodeSelection:t=>({tr:e,dispatch:n})=>{if(n){const{doc:n}=e,r=_i(t,0,n.content.size),o=ie.create(n,r);e.setSelection(o)}return!0},setTextSelection:t=>({tr:e,dispatch:n})=>{if(n){const{doc:n}=e,{from:r,to:o}="number"==typeof t?{from:t,to:t}:t,i=re.atStart(n).from,s=re.atEnd(n).to,l=_i(r,i,s),a=_i(o,i,s),c=re.create(n,l,a);e.setSelection(c)}return!0},sinkListItem:t=>({state:e,dispatch:n})=>{const r=mi(t,e.schema);return(i=r,function(t,e){let{$from:n,$to:r}=t.selection,s=n.blockRange(r,(t=>t.childCount>0&&t.firstChild.type==i));if(!s)return!1;let l=s.startIndex;if(0==l)return!1;let a=s.parent,c=a.child(l-1);if(c.type!=i)return!1;if(e){let n=c.lastChild&&c.lastChild.type==a.type,r=o.from(n?i.create():null),l=new h(o.from(i.create(null,o.from(a.type.create(null,r)))),n?3:1,0),d=s.start,p=s.end;e(t.tr.step(new Ct(d-(n?3:1),p,d,p,l,1,!0)).scrollIntoView())}return!0})(e,n);var i},splitBlock:({keepMarks:t=!0}={})=>({tr:e,state:n,dispatch:r,editor:o})=>{const{selection:i,doc:s}=e,{$from:l,$to:a}=i,c=hs(o.extensionManager.attributes,l.node().type.name,l.node().attrs);if(i instanceof ie&&i.node.isBlock)return!(!l.parentOffset||!Pt(s,l.pos))&&(r&&(t&&fs(n,o.extensionManager.splittableMarks),e.split(l.pos).scrollIntoView()),!0);if(!l.parent.isBlock)return!1;const h=a.parentOffset===a.parent.content.size,d=0===l.depth?void 0:function(t){for(let e=0;e<t.edgeCount;e+=1){const{type:n}=t.edge(e);if(n.isTextblock&&!n.hasRequiredAttrs())return n}return null}(l.node(-1).contentMatchAt(l.indexAfter(-1)));let p=h&&d?[{type:d,attrs:c}]:void 0,u=Pt(e.doc,e.mapping.map(l.pos),1,p);if(p||u||!Pt(e.doc,e.mapping.map(l.pos),1,d?[{type:d}]:void 0)||(u=!0,p=d?[{type:d,attrs:c}]:void 0),r){if(u&&(i instanceof re&&e.deleteSelection(),e.split(e.mapping.map(l.pos),1,p),d&&!h&&!l.parentOffset&&l.parent.type!==d)){const t=e.mapping.map(l.before()),n=e.doc.resolve(t);l.node(-1).canReplaceWith(n.index(),n.index()+1,d)&&e.setNodeMarkup(e.mapping.map(l.before()),d)}t&&fs(n,o.extensionManager.splittableMarks),e.scrollIntoView()}return u},splitListItem:(t,e={})=>({tr:n,state:r,dispatch:i,editor:s})=>{var l;const a=mi(t,r.schema),{$from:c,$to:d}=r.selection,p=r.selection.node;if(p&&p.isBlock||c.depth<2||!c.sameParent(d))return!1;const u=c.node(-1);if(u.type!==a)return!1;const f=s.extensionManager.attributes;if(0===c.parent.content.size&&c.node(-1).childCount===c.indexAfter(-1)){if(2===c.depth||c.node(-3).type!==a||c.index(-2)!==c.node(-2).childCount-1)return!1;if(i){let t=o.empty;const r=c.index(-1)?1:c.index(-2)?2:3;for(let e=c.depth-r;e>=c.depth-3;e-=1)t=o.from(c.node(e).copy(t));const i=c.indexAfter(-1)<c.node(-2).childCount?1:c.indexAfter(-2)<c.node(-3).childCount?2:3,s={...hs(f,c.node().type.name,c.node().attrs),...e},d=(null===(l=a.contentMatch.defaultType)||void 0===l?void 0:l.createAndFill(s))||void 0;t=t.append(o.from(a.createAndFill(null,d)||void 0));const p=c.before(c.depth-(r-1));n.replace(p,c.after(-i),new h(t,4-r,0));let u=-1;n.doc.nodesBetween(p,n.doc.content.size,((t,e)=>{if(u>-1)return!1;t.isTextblock&&0===t.content.size&&(u=e+1)})),u>-1&&n.setSelection(re.near(n.doc.resolve(u))),n.scrollIntoView()}return!0}const m=d.pos===c.end()?u.contentMatchAt(0).defaultType:null,g={...hs(f,u.type.name,u.attrs),...e},y={...hs(f,c.node().type.name,c.node().attrs),...e};n.delete(c.pos,d.pos);const w=m?[{type:a,attrs:g},{type:m,attrs:y}]:[{type:a,attrs:g}];if(!Pt(n.doc,c.pos,2))return!1;if(i){const{selection:t,storedMarks:e}=r,{splittableMarks:o}=s.extensionManager,l=e||t.$to.parentOffset&&t.$from.marks();if(n.split(c.pos,2,w).scrollIntoView(),!l||!i)return!0;const a=l.filter((t=>o.includes(t.type.name)));n.ensureMarks(a)}return!0},toggleList:(t,e,n,r={})=>({editor:o,tr:i,state:s,dispatch:l,chain:a,commands:c,can:h})=>{const{extensions:d,splittableMarks:p}=o.extensionManager,u=mi(t,s.schema),f=mi(e,s.schema),{selection:m,storedMarks:g}=s,{$from:y,$to:w}=m,v=y.blockRange(w),k=g||m.$to.parentOffset&&m.$from.marks();if(!v)return!1;const b=ss((t=>ps(t.type.name,d)))(m);if(v.depth>=1&&b&&v.depth-b.depth<=1){if(b.node.type===u)return c.liftListItem(f);if(ps(b.node.type.name,d)&&u.validContent(b.node.content)&&l)return a().command((()=>(i.setNodeMarkup(b.pos,u),!0))).command((()=>ms(i,u))).command((()=>gs(i,u))).run()}return n&&k&&l?a().command((()=>{const t=h().wrapInList(u,r),e=k.filter((t=>p.includes(t.type.name)));return i.ensureMarks(e),!!t||c.clearNodes()})).wrapInList(u,r).command((()=>ms(i,u))).command((()=>gs(i,u))).run():a().command((()=>!!h().wrapInList(u,r)||c.clearNodes())).wrapInList(u,r).command((()=>ms(i,u))).command((()=>gs(i,u))).run()},toggleMark:(t,e={},n={})=>({state:r,commands:o})=>{const{extendEmptyMarkRange:i=!1}=n,s=Ji(t,r.schema);return ds(r,s,e)?o.unsetMark(s,{extendEmptyMarkRange:i}):o.setMark(s,e)},toggleNode:(t,e,n={})=>({state:r,commands:o})=>{const i=mi(t,r.schema),s=mi(e,r.schema),l=ts(r,i,n);let a;return r.selection.$anchor.sameParent(r.selection.$head)&&(a=r.selection.$anchor.parent.attrs),l?o.setNode(s,a):o.setNode(i,{...a,...n})},toggleWrap:(t,e={})=>({state:n,commands:r})=>{const o=mi(t,n.schema);return ts(n,o,e)?r.lift(o):r.wrapIn(o,e)},undoInputRule:()=>({state:t,dispatch:e})=>{const n=t.plugins;for(let r=0;r<n.length;r+=1){const o=n[r];let i;if(o.spec.isInputRules&&(i=o.getState(t))){if(e){const e=t.tr,n=i.transform;for(let t=n.steps.length-1;t>=0;t-=1)e.step(n.steps[t].invert(n.docs[t]));if(i.text){const n=e.doc.resolve(i.from).marks();e.replaceWith(i.from,i.to,t.schema.text(i.text,n))}else e.delete(i.from,i.to)}return!0}}return!1},unsetAllMarks:()=>({tr:t,dispatch:e})=>{const{selection:n}=t,{empty:r,ranges:o}=n;return r||e&&o.forEach((e=>{t.removeMark(e.$from.pos,e.$to.pos)})),!0},unsetMark:(t,e={})=>({tr:n,state:r,dispatch:o})=>{var i;const{extendEmptyMarkRange:s=!1}=e,{selection:l}=n,a=Ji(t,r.schema),{$from:c,empty:h,ranges:d}=l;if(!o)return!0;if(h&&s){let{from:t,to:e}=l;const r=null===(i=c.marks().find((t=>t.type===a)))||void 0===i?void 0:i.attrs,o=qi(c,a,r);o&&(t=o.from,e=o.to),n.removeMark(t,e,a)}else d.forEach((t=>{n.removeMark(t.$from.pos,t.$to.pos,a)}));return n.removeStoredMark(a),!0},updateAttributes:(t,e={})=>({tr:n,state:r,dispatch:o})=>{let i=null,s=null;const l=es("string"==typeof t?t:t.name,r.schema);return!!l&&("node"===l&&(i=mi(t,r.schema)),"mark"===l&&(s=Ji(t,r.schema)),o&&n.selection.ranges.forEach((t=>{const o=t.$from.pos,l=t.$to.pos;let a,c,h,d;n.selection.empty?r.doc.nodesBetween(o,l,((t,e)=>{i&&i===t.type&&(h=Math.max(e,o),d=Math.min(e+t.nodeSize,l),a=e,c=t)})):r.doc.nodesBetween(o,l,((t,r)=>{r<o&&i&&i===t.type&&(h=Math.max(r,o),d=Math.min(r+t.nodeSize,l),a=r,c=t),r>=o&&r<=l&&(i&&i===t.type&&n.setNodeMarkup(r,void 0,{...t.attrs,...e}),s&&t.marks.length&&t.marks.forEach((i=>{if(s===i.type){const a=Math.max(r,o),c=Math.min(r+t.nodeSize,l);n.addMark(a,c,s.create({...i.attrs,...e}))}})))})),c&&(void 0!==a&&n.setNodeMarkup(a,void 0,{...c.attrs,...e}),s&&c.marks.length&&c.marks.forEach((t=>{s===t.type&&n.addMark(h,d,s.create({...t.attrs,...e}))})))})),!0)},wrapIn:(t,e={})=>({state:n,dispatch:r})=>function(t,e=null){return function(n,r){let{$from:o,$to:i}=n.selection,s=o.blockRange(i),l=s&&At(s,t,e);return!!l&&(r&&r(n.tr.wrap(s,l).scrollIntoView()),!0)}}(mi(t,n.schema),e)(n,r),wrapInList:(t,e={})=>({state:n,dispatch:r})=>li(mi(t,n.schema),e)(n,r)});const ws=$i.create({name:"commands",addCommands:()=>({...ys})}),vs=$i.create({name:"drop",addProseMirrorPlugins(){return[new we({key:new be("tiptapDrop"),props:{handleDrop:(t,e,n,r)=>{this.editor.emit("drop",{editor:this.editor,event:e,slice:n,moved:r})}}})]}}),ks=$i.create({name:"editable",addProseMirrorPlugins(){return[new we({key:new be("editable"),props:{editable:()=>this.editor.options.editable}})]}}),bs=$i.create({name:"focusEvents",addProseMirrorPlugins(){const{editor:t}=this;return[new we({key:new be("focusEvents"),props:{handleDOMEvents:{focus:(e,n)=>{t.isFocused=!0;const r=t.state.tr.setMeta("focus",{event:n}).setMeta("addToHistory",!1);return e.dispatch(r),!1},blur:(e,n)=>{t.isFocused=!1;const r=t.state.tr.setMeta("blur",{event:n}).setMeta("addToHistory",!1);return e.dispatch(r),!1}}}})]}}),xs=$i.create({name:"keymap",addKeyboardShortcuts(){const t=()=>this.editor.commands.first((({commands:t})=>[()=>t.undoInputRule(),()=>t.command((({tr:e})=>{const{selection:n,doc:r}=e,{empty:o,$anchor:i}=n,{pos:s,parent:l}=i,a=i.parent.isTextblock&&s>0?e.doc.resolve(s-1):i,c=a.parent.type.spec.isolating,h=i.pos-i.parentOffset,d=c&&1===a.parent.childCount?h===i.pos:Zt.atStart(r).from===s;return!(!o||!l.type.isTextblock||l.textContent.length||!d||d&&"paragraph"===i.parent.type.name)&&t.clearNodes()})),()=>t.deleteSelection(),()=>t.joinBackward(),()=>t.selectNodeBackward()])),e=()=>this.editor.commands.first((({commands:t})=>[()=>t.deleteSelection(),()=>t.deleteCurrentNode(),()=>t.joinForward(),()=>t.selectNodeForward()])),n={Enter:()=>this.editor.commands.first((({commands:t})=>[()=>t.newlineInCode(),()=>t.createParagraphNear(),()=>t.liftEmptyBlock(),()=>t.splitBlock()])),"Mod-Enter":()=>this.editor.commands.exitCode(),Backspace:t,"Mod-Backspace":t,"Shift-Backspace":t,Delete:e,"Mod-Delete":e,"Mod-a":()=>this.editor.commands.selectAll()},r={...n},o={...n,"Ctrl-h":t,"Alt-Backspace":t,"Ctrl-d":e,"Ctrl-Alt-Backspace":e,"Alt-Delete":e,"Alt-d":e,"Ctrl-a":()=>this.editor.commands.selectTextblockStart(),"Ctrl-e":()=>this.editor.commands.selectTextblockEnd()};return Xi()||Zi()?o:r},addProseMirrorPlugins(){return[new we({key:new be("clearDocument"),appendTransaction:(t,e,n)=>{const r=t.some((t=>t.docChanged))&&!e.doc.eq(n.doc),o=t.some((t=>t.getMeta("preventClearDocument")));if(!r||o)return;const{empty:i,from:s,to:l}=e.selection,a=Zt.atStart(e.doc).from,c=Zt.atEnd(e.doc).to;if(i||!(s===a&&l===c))return;if(!us(n.doc))return;const h=n.tr,d=ci({state:n,transaction:h}),{commands:p}=new hi({editor:this.editor,state:d});return p.clearNodes(),h.steps.length?h:void 0}})]}}),Ss=$i.create({name:"paste",addProseMirrorPlugins(){return[new we({key:new be("tiptapPaste"),props:{handlePaste:(t,e,n)=>{this.editor.emit("paste",{editor:this.editor,event:e,slice:n})}}})]}}),Ms=$i.create({name:"tabindex",addProseMirrorPlugins(){return[new we({key:new be("tabindex"),props:{attributes:()=>this.editor.isEditable?{tabindex:"0"}:{}}})]}});class Cs{get name(){return this.node.type.name}constructor(t,e,n=!1,r=null){this.currentNode=null,this.actualDepth=null,this.isBlock=n,this.resolvedPos=t,this.editor=e,this.currentNode=r}get node(){return this.currentNode||this.resolvedPos.node()}get element(){return this.editor.view.domAtPos(this.pos).node}get depth(){var t;return null!==(t=this.actualDepth)&&void 0!==t?t:this.resolvedPos.depth}get pos(){return this.resolvedPos.pos}get content(){return this.node.content}set content(t){let e=this.from,n=this.to;if(this.isBlock){if(0===this.content.size)return void console.error(`You can’t set content on a block node. Tried to set content on ${this.name} at ${this.pos}`);e=this.from+1,n=this.to-1}this.editor.commands.insertContentAt({from:e,to:n},t)}get attributes(){return this.node.attrs}get textContent(){return this.node.textContent}get size(){return this.node.nodeSize}get from(){return this.isBlock?this.pos:this.resolvedPos.start(this.resolvedPos.depth)}get range(){return{from:this.from,to:this.to}}get to(){return this.isBlock?this.pos+this.size:this.resolvedPos.end(this.resolvedPos.depth)+(this.node.isText?0:1)}get parent(){if(0===this.depth)return null;const t=this.resolvedPos.start(this.resolvedPos.depth-1),e=this.resolvedPos.doc.resolve(t);return new Cs(e,this.editor)}get before(){let t=this.resolvedPos.doc.resolve(this.from-(this.isBlock?1:2));return t.depth!==this.depth&&(t=this.resolvedPos.doc.resolve(this.from-3)),new Cs(t,this.editor)}get after(){let t=this.resolvedPos.doc.resolve(this.to+(this.isBlock?2:1));return t.depth!==this.depth&&(t=this.resolvedPos.doc.resolve(this.to+3)),new Cs(t,this.editor)}get children(){const t=[];return this.node.content.forEach(((e,n)=>{const r=e.isBlock&&!e.isTextblock,o=e.isAtom&&!e.isText,i=this.pos+n+(o?0:1),s=this.resolvedPos.doc.resolve(i);if(!r&&s.depth<=this.depth)return;const l=new Cs(s,this.editor,r,r?e:null);r&&(l.actualDepth=this.depth+1),t.push(new Cs(s,this.editor,r,r?e:null))})),t}get firstChild(){return this.children[0]||null}get lastChild(){const t=this.children;return t[t.length-1]||null}closest(t,e={}){let n=null,r=this.parent;for(;r&&!n;){if(r.node.type.name===t)if(Object.keys(e).length>0){const t=r.node.attrs,n=Object.keys(e);for(let r=0;r<n.length;r+=1){const o=n[r];if(t[o]!==e[o])break}}else n=r;r=r.parent}return n}querySelector(t,e={}){return this.querySelectorAll(t,e,!0)[0]||null}querySelectorAll(t,e={},n=!1){let r=[];if(!this.children||0===this.children.length)return r;const o=Object.keys(e);return this.children.forEach((i=>{if(!(n&&r.length>0)){if(i.node.type.name===t){o.every((t=>e[t]===i.node.attrs[t]))&&r.push(i)}n&&r.length>0||(r=r.concat(i.querySelectorAll(t,e,n)))}})),r}setAttribute(t){const{tr:e}=this.editor.state;e.setNodeMarkup(this.from,void 0,{...this.node.attrs,...t}),this.editor.view.dispatch(e)}}function Os(t){return new Oi({find:t.find,handler:({state:e,range:n,match:r})=>{const o=vi(t.getAttributes,void 0,r);if(!1===o||null===o)return null;const{tr:i}=e,s=r[r.length-1],l=r[0];if(s){const r=l.search(/\S/),a=n.from+l.indexOf(s),c=a+s.length;if(cs(n.from,n.to,e.doc).filter((e=>e.mark.type.excluded.find((n=>n===t.type&&n!==e.mark.type)))).filter((t=>t.to>a)).length)return null;c<n.to&&i.delete(c,n.to),a>n.from&&i.delete(n.from+r,a);const h=n.from+r+s.length;i.addMark(n.from+r,h,t.type.create(o||{})),i.removeStoredMark(t.type)}}})}function Ts(t){return new Oi({find:t.find,handler:({state:e,range:n,match:r,chain:o})=>{const i=vi(t.getAttributes,void 0,r)||{},s=e.tr.delete(n.from,n.to),l=s.doc.resolve(n.from).blockRange(),a=l&&At(l,t.type,i);if(!a)return null;if(s.wrap(l,a),t.keepMarks&&t.editor){const{selection:n,storedMarks:r}=e,{splittableMarks:o}=t.editor.extensionManager,i=r||n.$to.parentOffset&&n.$from.marks();if(i){const t=i.filter((t=>o.includes(t.type.name)));s.ensureMarks(t)}}if(t.keepAttributes){const e="bulletList"===t.type.name||"orderedList"===t.type.name?"listItem":"taskList";o().updateAttributes(e,i).run()}const c=s.doc.resolve(n.from-1).nodeBefore;c&&c.type===t.type&&zt(s.doc,n.from-1)&&(!t.joinPredicate||t.joinPredicate(r,c))&&s.join(n.from-1)}})}class Es{constructor(t={}){this.type="node",this.name="node",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...t},this.name=this.config.name,t.defaultOptions&&Object.keys(t.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=vi(pi(this,"addOptions",{name:this.name}))),this.storage=vi(pi(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(t={}){return new Es(t)}configure(t={}){const e=this.extend({...this.config,addOptions:()=>Ai(this.options,t)});return e.name=this.name,e.parent=this.parent,e}extend(t={}){const e=new Es(t);return e.parent=this,this.child=e,e.name=t.name?t.name:e.parent.name,t.defaultOptions&&Object.keys(t.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${e.name}".`),e.options=vi(pi(e,"addOptions",{name:e.name})),e.storage=vi(pi(e,"addStorage",{name:e.name,options:e.options})),e}}function Ns(t){return new Ri({find:t.find,handler:({state:e,range:n,match:r,pasteEvent:o})=>{const i=vi(t.getAttributes,void 0,r,o);if(!1===i||null===i)return null;const{tr:s}=e,l=r[r.length-1],a=r[0];let c=n.to;if(l){const r=a.search(/\S/),o=n.from+a.indexOf(l),h=o+l.length;if(cs(n.from,n.to,e.doc).filter((e=>e.mark.type.excluded.find((n=>n===t.type&&n!==e.mark.type)))).filter((t=>t.to>o)).length)return null;h<n.to&&s.delete(h,n.to),o>n.from&&s.delete(n.from+r,o),c=n.from+r+l.length,s.addMark(n.from+r,c,t.type.create(i||{})),s.removeStoredMark(t.type)}}})}const As=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))$/,Ds=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))/g,Rs=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))$/,Is=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))/g,Ps=Di.create({name:"bold",addOptions:()=>({HTMLAttributes:{}}),parseHTML(){return[{tag:"strong"},{tag:"b",getAttrs:t=>"normal"!==t.style.fontWeight&&null},{style:"font-weight=400",clearMark:t=>t.type.name===this.name},{style:"font-weight",getAttrs:t=>/^(bold(er)?|[5-9]\d{2,})$/.test(t)&&null}]},renderHTML({HTMLAttributes:t}){return["strong",gi(this.options.HTMLAttributes,t),0]},addCommands(){return{setBold:()=>({commands:t})=>t.setMark(this.name),toggleBold:()=>({commands:t})=>t.toggleMark(this.name),unsetBold:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-b":()=>this.editor.commands.toggleBold(),"Mod-B":()=>this.editor.commands.toggleBold()}},addInputRules(){return[Os({find:As,type:this.type}),Os({find:Rs,type:this.type})]},addPasteRules(){return[Ns({find:Ds,type:this.type}),Ns({find:Is,type:this.type})]}}),zs="textStyle",Ls=/^\s*([-+*])\s$/,$s=Es.create({name:"bulletList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML:()=>[{tag:"ul"}],renderHTML({HTMLAttributes:t}){return["ul",gi(this.options.HTMLAttributes,t),0]},addCommands(){return{toggleBulletList:()=>({commands:t,chain:e})=>this.options.keepAttributes?e().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes("listItem",this.editor.getAttributes(zs)).run():t.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-8":()=>this.editor.commands.toggleBulletList()}},addInputRules(){let t=Ts({find:Ls,type:this.type});return(this.options.keepMarks||this.options.keepAttributes)&&(t=Ts({find:Ls,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:()=>this.editor.getAttributes(zs),editor:this.editor})),[t]}}),Bs=$i.create({name:"characterCount",addOptions:()=>({limit:null,mode:"textSize",textCounter:t=>t.length,wordCounter:t=>t.split(" ").filter((t=>""!==t)).length}),addStorage:()=>({characters:()=>0,words:()=>0}),onBeforeCreate(){this.storage.characters=t=>{const e=(null==t?void 0:t.node)||this.editor.state.doc;if("textSize"===((null==t?void 0:t.mode)||this.options.mode)){const t=e.textBetween(0,e.content.size,void 0," ");return this.options.textCounter(t)}return e.nodeSize},this.storage.words=t=>{const e=(null==t?void 0:t.node)||this.editor.state.doc,n=e.textBetween(0,e.content.size," "," ");return this.options.wordCounter(n)}},addProseMirrorPlugins(){let t=!1;return[new we({key:new be("characterCount"),appendTransaction:(e,n,r)=>{if(t)return;const o=this.options.limit;if(null==o||0===o)return void(t=!0);const i=this.storage.characters({node:r.doc});if(i>o){const e=0,n=i-o;console.warn(`[CharacterCount] Initial content exceeded limit of ${o} characters. Content was automatically trimmed.`);const s=r.tr.deleteRange(e,n);return t=!0,s}t=!0},filterTransaction:(t,e)=>{const n=this.options.limit;if(!t.docChanged||0===n||null==n)return!0;const r=this.storage.characters({node:e.doc}),o=this.storage.characters({node:t.doc});if(o<=n)return!0;if(r>n&&o>n&&o<=r)return!0;if(r>n&&o>n&&o>r)return!1;if(!t.getMeta("paste"))return!1;const i=t.selection.$head.pos,s=i-(o-n),l=i;t.deleteRange(s,l);return!(this.storage.characters({node:t.doc})>n)}})]}}),Fs=Es.create({name:"doc",topNode:!0,content:"block+"});var Vs=200,js=function(){};js.prototype.append=function(t){return t.length?(t=js.from(t),!this.length&&t||t.length<Vs&&this.leafAppend(t)||this.length<Vs&&t.leafPrepend(this)||this.appendInner(t)):this},js.prototype.prepend=function(t){return t.length?js.from(t).append(this):this},js.prototype.appendInner=function(t){return new Ws(this,t)},js.prototype.slice=function(t,e){return void 0===t&&(t=0),void 0===e&&(e=this.length),t>=e?js.empty:this.sliceInner(Math.max(0,t),Math.min(this.length,e))},js.prototype.get=function(t){if(!(t<0||t>=this.length))return this.getInner(t)},js.prototype.forEach=function(t,e,n){void 0===e&&(e=0),void 0===n&&(n=this.length),e<=n?this.forEachInner(t,e,n,0):this.forEachInvertedInner(t,e,n,0)},js.prototype.map=function(t,e,n){void 0===e&&(e=0),void 0===n&&(n=this.length);var r=[];return this.forEach((function(e,n){return r.push(t(e,n))}),e,n),r},js.from=function(t){return t instanceof js?t:t&&t.length?new Hs(t):js.empty};var Hs=function(t){function e(e){t.call(this),this.values=e}e.__proto__=t,e.prototype=Object.create(t.prototype),e.prototype.constructor=e;var n={length:{configurable:!0},depth:{configurable:!0}};return e.prototype.flatten=function(){return this.values},e.prototype.sliceInner=function(t,n){return 0==t&&n==this.length?this:new e(this.values.slice(t,n))},e.prototype.getInner=function(t){return this.values[t]},e.prototype.forEachInner=function(t,e,n,r){for(var o=e;o<n;o++)if(!1===t(this.values[o],r+o))return!1},e.prototype.forEachInvertedInner=function(t,e,n,r){for(var o=e-1;o>=n;o--)if(!1===t(this.values[o],r+o))return!1},e.prototype.leafAppend=function(t){if(this.length+t.length<=Vs)return new e(this.values.concat(t.flatten()))},e.prototype.leafPrepend=function(t){if(this.length+t.length<=Vs)return new e(t.flatten().concat(this.values))},n.length.get=function(){return this.values.length},n.depth.get=function(){return 0},Object.defineProperties(e.prototype,n),e}(js);js.empty=new Hs([]);var Ws=function(t){function e(e,n){t.call(this),this.left=e,this.right=n,this.length=e.length+n.length,this.depth=Math.max(e.depth,n.depth)+1}return e.__proto__=t,e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.prototype.flatten=function(){return this.left.flatten().concat(this.right.flatten())},e.prototype.getInner=function(t){return t<this.left.length?this.left.get(t):this.right.get(t-this.left.length)},e.prototype.forEachInner=function(t,e,n,r){var o=this.left.length;return!(e<o&&!1===this.left.forEachInner(t,e,Math.min(n,o),r))&&(!(n>o&&!1===this.right.forEachInner(t,Math.max(e-o,0),Math.min(this.length,n)-o,r+o))&&void 0)},e.prototype.forEachInvertedInner=function(t,e,n,r){var o=this.left.length;return!(e>o&&!1===this.right.forEachInvertedInner(t,e-o,Math.max(n,o)-o,r+o))&&(!(n<o&&!1===this.left.forEachInvertedInner(t,Math.min(e,o),n,r))&&void 0)},e.prototype.sliceInner=function(t,e){if(0==t&&e==this.length)return this;var n=this.left.length;return e<=n?this.left.slice(t,e):t>=n?this.right.slice(t-n,e-n):this.left.slice(t,n).append(this.right.slice(0,e-n))},e.prototype.leafAppend=function(t){var n=this.right.leafAppend(t);if(n)return new e(this.left,n)},e.prototype.leafPrepend=function(t){var n=this.left.leafPrepend(t);if(n)return new e(n,this.right)},e.prototype.appendInner=function(t){return this.left.depth>=Math.max(this.right.depth,t.depth)+1?new e(this.left,new e(this.right,t)):new e(this,t)},e}(js);class qs{constructor(t,e){this.items=t,this.eventCount=e}popEvent(t,e){if(0==this.eventCount)return null;let n,r,o=this.items.length;for(;;o--){if(this.items.get(o-1).selection){--o;break}}e&&(n=this.remapping(o,this.items.length),r=n.maps.length);let i,s,l=t.tr,a=[],c=[];return this.items.forEach(((t,e)=>{if(!t.step)return n||(n=this.remapping(o,e+1),r=n.maps.length),r--,void c.push(t);if(n){c.push(new Js(t.map));let e,o=t.step.map(n.slice(r));o&&l.maybeStep(o).doc&&(e=l.mapping.maps[l.mapping.maps.length-1],a.push(new Js(e,void 0,void 0,a.length+c.length))),r--,e&&n.appendMap(e,r)}else l.maybeStep(t.step);return t.selection?(i=n?t.selection.map(n.slice(r)):t.selection,s=new qs(this.items.slice(0,o).append(c.reverse().concat(a)),this.eventCount-1),!1):void 0}),this.items.length,0),{remaining:s,transform:l,selection:i}}addTransform(t,e,n,r){let o=[],i=this.eventCount,s=this.items,l=!r&&s.length?s.get(s.length-1):null;for(let n=0;n<t.steps.length;n++){let a,c=t.steps[n].invert(t.docs[n]),h=new Js(t.mapping.maps[n],c,e);(a=l&&l.merge(h))&&(h=a,n?o.pop():s=s.slice(0,s.length-1)),o.push(h),e&&(i++,e=void 0),r||(l=h)}let a=i-n.depth;return a>_s&&(s=function(t,e){let n;return t.forEach(((t,r)=>{if(t.selection&&0==e--)return n=r,!1})),t.slice(n)}(s,a),i-=a),new qs(s.append(o),i)}remapping(t,e){let n=new mt;return this.items.forEach(((e,r)=>{let o=null!=e.mirrorOffset&&r-e.mirrorOffset>=t?n.maps.length-e.mirrorOffset:void 0;n.appendMap(e.map,o)}),t,e),n}addMaps(t){return 0==this.eventCount?this:new qs(this.items.append(t.map((t=>new Js(t)))),this.eventCount)}rebased(t,e){if(!this.eventCount)return this;let n=[],r=Math.max(0,this.items.length-e),o=t.mapping,i=t.steps.length,s=this.eventCount;this.items.forEach((t=>{t.selection&&s--}),r);let l=e;this.items.forEach((e=>{let r=o.getMirror(--l);if(null==r)return;i=Math.min(i,r);let a=o.maps[r];if(e.step){let i=t.steps[r].invert(t.docs[r]),c=e.selection&&e.selection.map(o.slice(l+1,r));c&&s++,n.push(new Js(a,i,c))}else n.push(new Js(a))}),r);let a=[];for(let t=e;t<i;t++)a.push(new Js(o.maps[t]));let c=this.items.slice(0,r).append(a).append(n),h=new qs(c,s);return h.emptyItemCount()>500&&(h=h.compress(this.items.length-n.length)),h}emptyItemCount(){let t=0;return this.items.forEach((e=>{e.step||t++})),t}compress(t=this.items.length){let e=this.remapping(0,t),n=e.maps.length,r=[],o=0;return this.items.forEach(((i,s)=>{if(s>=t)r.push(i),i.selection&&o++;else if(i.step){let t=i.step.map(e.slice(n)),s=t&&t.getMap();if(n--,s&&e.appendMap(s,n),t){let l=i.selection&&i.selection.map(e.slice(n));l&&o++;let a,c=new Js(s.invert(),t,l),h=r.length-1;(a=r.length&&r[h].merge(c))?r[h]=a:r.push(c)}}else i.map&&n--}),this.items.length,0),new qs(js.from(r.reverse()),o)}}qs.empty=new qs(js.empty,0);class Js{constructor(t,e,n,r){this.map=t,this.step=e,this.selection=n,this.mirrorOffset=r}merge(t){if(this.step&&t.step&&!t.selection){let e=t.step.merge(this.step);if(e)return new Js(e.getMap().invert(),e,this.selection)}}}class Ks{constructor(t,e,n,r,o){this.done=t,this.undone=e,this.prevRanges=n,this.prevTime=r,this.prevComposition=o}}const _s=20;function Us(t){let e=[];for(let n=t.length-1;n>=0&&0==e.length;n--)t[n].forEach(((t,n,r,o)=>e.push(r,o)));return e}function Gs(t,e){if(!t)return null;let n=[];for(let r=0;r<t.length;r+=2){let o=e.map(t[r],1),i=e.map(t[r+1],-1);o<=i&&n.push(o,i)}return n}let Qs=!1,Ys=null;function Xs(t){let e=t.plugins;if(Ys!=e){Qs=!1,Ys=e;for(let t=0;t<e.length;t++)if(e[t].spec.historyPreserveItems){Qs=!0;break}}return Qs}const Zs=new be("history"),tl=new be("closeHistory");function el(t={}){return t={depth:t.depth||100,newGroupDelay:t.newGroupDelay||500},new we({key:Zs,state:{init:()=>new Ks(qs.empty,qs.empty,null,0,-1),apply:(e,n,r)=>function(t,e,n,r){let o,i=n.getMeta(Zs);if(i)return i.historyState;n.getMeta(tl)&&(t=new Ks(t.done,t.undone,null,0,-1));let s=n.getMeta("appendedTransaction");if(0==n.steps.length)return t;if(s&&s.getMeta(Zs))return s.getMeta(Zs).redo?new Ks(t.done.addTransform(n,void 0,r,Xs(e)),t.undone,Us(n.mapping.maps),t.prevTime,t.prevComposition):new Ks(t.done,t.undone.addTransform(n,void 0,r,Xs(e)),null,t.prevTime,t.prevComposition);if(!1===n.getMeta("addToHistory")||s&&!1===s.getMeta("addToHistory"))return(o=n.getMeta("rebased"))?new Ks(t.done.rebased(n,o),t.undone.rebased(n,o),Gs(t.prevRanges,n.mapping),t.prevTime,t.prevComposition):new Ks(t.done.addMaps(n.mapping.maps),t.undone.addMaps(n.mapping.maps),Gs(t.prevRanges,n.mapping),t.prevTime,t.prevComposition);{let o=n.getMeta("composition"),i=0==t.prevTime||!s&&t.prevComposition!=o&&(t.prevTime<(n.time||0)-r.newGroupDelay||!function(t,e){if(!e)return!1;if(!t.docChanged)return!0;let n=!1;return t.mapping.maps[0].forEach(((t,r)=>{for(let o=0;o<e.length;o+=2)t<=e[o+1]&&r>=e[o]&&(n=!0)})),n}(n,t.prevRanges)),l=s?Gs(t.prevRanges,n.mapping):Us(n.mapping.maps);return new Ks(t.done.addTransform(n,i?e.selection.getBookmark():void 0,r,Xs(e)),qs.empty,l,n.time,null==o?t.prevComposition:o)}}(n,r,e,t)},config:t,props:{handleDOMEvents:{beforeinput(t,e){let n=e.inputType,r="historyUndo"==n?rl:"historyRedo"==n?ol:null;return!!r&&(e.preventDefault(),r(t.state,t.dispatch))}}}})}function nl(t,e){return(n,r)=>{let o=Zs.getState(n);if(!o||0==(t?o.undone:o.done).eventCount)return!1;if(r){let i=function(t,e,n){let r=Xs(e),o=Zs.get(e).spec.config,i=(n?t.undone:t.done).popEvent(e,r);if(!i)return null;let s=i.selection.resolve(i.transform.doc),l=(n?t.done:t.undone).addTransform(i.transform,e.selection.getBookmark(),o,r),a=new Ks(n?l:i.remaining,n?i.remaining:l,null,0,-1);return i.transform.setSelection(s).setMeta(Zs,{redo:n,historyState:a})}(o,n,t);i&&r(e?i.scrollIntoView():i)}return!0}}const rl=nl(!1,!0),ol=nl(!0,!0),il=$i.create({name:"history",addOptions:()=>({depth:100,newGroupDelay:500}),addCommands:()=>({undo:()=>({state:t,dispatch:e})=>rl(t,e),redo:()=>({state:t,dispatch:e})=>ol(t,e)}),addProseMirrorPlugins(){return[el(this.options)]},addKeyboardShortcuts(){return{"Mod-z":()=>this.editor.commands.undo(),"Shift-Mod-z":()=>this.editor.commands.redo(),"Mod-y":()=>this.editor.commands.redo(),"Mod-я":()=>this.editor.commands.undo(),"Shift-Mod-я":()=>this.editor.commands.redo()}}}),sl=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))$/,ll=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))/g,al=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))$/,cl=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))/g,hl=Di.create({name:"italic",addOptions:()=>({HTMLAttributes:{}}),parseHTML(){return[{tag:"em"},{tag:"i",getAttrs:t=>"normal"!==t.style.fontStyle&&null},{style:"font-style=normal",clearMark:t=>t.type.name===this.name},{style:"font-style=italic"}]},renderHTML({HTMLAttributes:t}){return["em",gi(this.options.HTMLAttributes,t),0]},addCommands(){return{setItalic:()=>({commands:t})=>t.setMark(this.name),toggleItalic:()=>({commands:t})=>t.toggleMark(this.name),unsetItalic:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-i":()=>this.editor.commands.toggleItalic(),"Mod-I":()=>this.editor.commands.toggleItalic()}},addInputRules(){return[Os({find:sl,type:this.type}),Os({find:al,type:this.type})]},addPasteRules(){return[Ns({find:ll,type:this.type}),Ns({find:cl,type:this.type})]}}),dl=(t,e)=>{for(const n in e)t[n]=e[n];return t},pl="numeric",ul="ascii",fl="alpha",ml="asciinumeric",gl="alphanumeric",yl="domain",wl="emoji",vl="scheme",kl="slashscheme",bl="whitespace";function xl(t,e){return t in e||(e[t]=[]),e[t]}function Sl(t,e,n){e[pl]&&(e[ml]=!0,e[gl]=!0),e[ul]&&(e[ml]=!0,e[fl]=!0),e[ml]&&(e[gl]=!0),e[fl]&&(e[gl]=!0),e[gl]&&(e[yl]=!0),e[wl]&&(e[yl]=!0);for(const r in e){const e=xl(r,n);e.indexOf(t)<0&&e.push(t)}}function Ml(t=null){this.j={},this.jr=[],this.jd=null,this.t=t}Ml.groups={},Ml.prototype={accepts(){return!!this.t},go(t){const e=this,n=e.j[t];if(n)return n;for(let n=0;n<e.jr.length;n++){const r=e.jr[n][0],o=e.jr[n][1];if(o&&r.test(t))return o}return e.jd},has(t,e=!1){return e?t in this.j:!!this.go(t)},ta(t,e,n,r){for(let o=0;o<t.length;o++)this.tt(t[o],e,n,r)},tr(t,e,n,r){let o;return r=r||Ml.groups,e&&e.j?o=e:(o=new Ml(e),n&&r&&Sl(e,n,r)),this.jr.push([t,o]),o},ts(t,e,n,r){let o=this;const i=t.length;if(!i)return o;for(let e=0;e<i-1;e++)o=o.tt(t[e]);return o.tt(t[i-1],e,n,r)},tt(t,e,n,r){r=r||Ml.groups;const o=this;if(e&&e.j)return o.j[t]=e,e;const i=e;let s,l=o.go(t);if(l?(s=new Ml,dl(s.j,l.j),s.jr.push.apply(s.jr,l.jr),s.jd=l.jd,s.t=l.t):s=new Ml,i){if(r)if(s.t&&"string"==typeof s.t){const t=dl(function(t,e){const n={};for(const r in e)e[r].indexOf(t)>=0&&(n[r]=!0);return n}(s.t,r),n);Sl(i,t,r)}else n&&Sl(i,n,r);s.t=i}return o.j[t]=s,s}};const Cl=(t,e,n,r,o)=>t.ta(e,n,r,o),Ol=(t,e,n,r,o)=>t.tr(e,n,r,o),Tl=(t,e,n,r,o)=>t.ts(e,n,r,o),El=(t,e,n,r,o)=>t.tt(e,n,r,o),Nl="WORD",Al="UWORD",Dl="ASCIINUMERICAL",Rl="ALPHANUMERICAL",Il="LOCALHOST",Pl="TLD",zl="UTLD",Ll="SCHEME",$l="SLASH_SCHEME",Bl="NUM",Fl="WS",Vl="NL",jl="OPENBRACE",Hl="CLOSEBRACE",Wl="OPENBRACKET",ql="CLOSEBRACKET",Jl="OPENPAREN",Kl="CLOSEPAREN",_l="OPENANGLEBRACKET",Ul="CLOSEANGLEBRACKET",Gl="FULLWIDTHLEFTPAREN",Ql="FULLWIDTHRIGHTPAREN",Yl="LEFTCORNERBRACKET",Xl="RIGHTCORNERBRACKET",Zl="LEFTWHITECORNERBRACKET",ta="RIGHTWHITECORNERBRACKET",ea="FULLWIDTHLESSTHAN",na="FULLWIDTHGREATERTHAN",ra="AMPERSAND",oa="APOSTROPHE",ia="ASTERISK",sa="AT",la="BACKSLASH",aa="BACKTICK",ca="CARET",ha="COLON",da="COMMA",pa="DOLLAR",ua="DOT",fa="EQUALS",ma="EXCLAMATION",ga="HYPHEN",ya="PERCENT",wa="PIPE",va="PLUS",ka="POUND",ba="QUERY",xa="QUOTE",Sa="FULLWIDTHMIDDLEDOT",Ma="SEMI",Ca="SLASH",Oa="TILDE",Ta="UNDERSCORE",Ea="EMOJI",Na="SYM";var Aa=Object.freeze({__proto__:null,WORD:Nl,UWORD:Al,ASCIINUMERICAL:Dl,ALPHANUMERICAL:Rl,LOCALHOST:Il,TLD:Pl,UTLD:zl,SCHEME:Ll,SLASH_SCHEME:$l,NUM:Bl,WS:Fl,NL:Vl,OPENBRACE:jl,CLOSEBRACE:Hl,OPENBRACKET:Wl,CLOSEBRACKET:ql,OPENPAREN:Jl,CLOSEPAREN:Kl,OPENANGLEBRACKET:_l,CLOSEANGLEBRACKET:Ul,FULLWIDTHLEFTPAREN:Gl,FULLWIDTHRIGHTPAREN:Ql,LEFTCORNERBRACKET:Yl,RIGHTCORNERBRACKET:Xl,LEFTWHITECORNERBRACKET:Zl,RIGHTWHITECORNERBRACKET:ta,FULLWIDTHLESSTHAN:ea,FULLWIDTHGREATERTHAN:na,AMPERSAND:ra,APOSTROPHE:oa,ASTERISK:ia,AT:sa,BACKSLASH:la,BACKTICK:aa,CARET:ca,COLON:ha,COMMA:da,DOLLAR:pa,DOT:ua,EQUALS:fa,EXCLAMATION:ma,HYPHEN:ga,PERCENT:ya,PIPE:wa,PLUS:va,POUND:ka,QUERY:ba,QUOTE:xa,FULLWIDTHMIDDLEDOT:Sa,SEMI:Ma,SLASH:Ca,TILDE:Oa,UNDERSCORE:Ta,EMOJI:Ea,SYM:Na});const Da=/[a-z]/,Ra=/\p{L}/u,Ia=/\p{Emoji}/u,Pa=/\d/,za=/\s/;let La=null,$a=null;function Ba(t,e){const n=function(t){const e=[],n=t.length;let r=0;for(;r<n;){let o,i=t.charCodeAt(r),s=i<55296||i>56319||r+1===n||(o=t.charCodeAt(r+1))<56320||o>57343?t[r]:t.slice(r,r+2);e.push(s),r+=s.length}return e}(e.replace(/[A-Z]/g,(t=>t.toLowerCase()))),r=n.length,o=[];let i=0,s=0;for(;s<r;){let l=t,a=null,c=0,h=null,d=-1,p=-1;for(;s<r&&(a=l.go(n[s]));)l=a,l.accepts()?(d=0,p=0,h=l):d>=0&&(d+=n[s].length,p++),c+=n[s].length,i+=n[s].length,s++;i-=d,s-=p,c-=d,o.push({t:h.t,v:e.slice(i-c,i),s:i-c,e:i})}return o}function Fa(t,e,n,r,o){let i;const s=e.length;for(let n=0;n<s-1;n++){const s=e[n];t.j[s]?i=t.j[s]:(i=new Ml(r),i.jr=o.slice(),t.j[s]=i),t=i}return i=new Ml(n),i.jr=o.slice(),t.j[e[s-1]]=i,i}function Va(t){const e=[],n=[];let r=0;for(;r<t.length;){let o=0;for(;"0123456789".indexOf(t[r+o])>=0;)o++;if(o>0){e.push(n.join(""));for(let e=parseInt(t.substring(r,r+o),10);e>0;e--)n.pop();r+=o}else n.push(t[r]),r++}return e}const ja={defaultProtocol:"http",events:null,format:Wa,formatHref:Wa,nl2br:!1,tagName:"a",target:null,rel:null,validate:!0,truncate:1/0,className:null,attributes:null,ignoreTags:[],render:null};function Ha(t,e=null){let n=dl({},ja);t&&(n=dl(n,t instanceof Ha?t.o:t));const r=n.ignoreTags,o=[];for(let t=0;t<r.length;t++)o.push(r[t].toUpperCase());this.o=n,e&&(this.defaultRender=e),this.ignoreTags=o}function Wa(t){return t}function qa(t,e){this.t="token",this.v=t,this.tk=e}function Ja(t,e){class n extends qa{constructor(e,n){super(e,n),this.t=t}}for(const t in e)n.prototype[t]=e[t];return n.t=t,n}Ha.prototype={o:ja,ignoreTags:[],defaultRender:t=>t,check(t){return this.get("validate",t.toString(),t)},get(t,e,n){const r=null!=e;let o=this.o[t];return o?("object"==typeof o?(o=n.t in o?o[n.t]:ja[t],"function"==typeof o&&r&&(o=o(e,n))):"function"==typeof o&&r&&(o=o(e,n.t,n)),o):o},getObj(t,e,n){let r=this.o[t];return"function"==typeof r&&null!=e&&(r=r(e,n.t,n)),r},render(t){const e=t.render(this);return(this.get("render",null,t)||this.defaultRender)(e,t.t,t)}},qa.prototype={isLink:!1,toString(){return this.v},toHref(t){return this.toString()},toFormattedString(t){const e=this.toString(),n=t.get("truncate",e,this),r=t.get("format",e,this);return n&&r.length>n?r.substring(0,n)+"…":r},toFormattedHref(t){return t.get("formatHref",this.toHref(t.get("defaultProtocol")),this)},startIndex(){return this.tk[0].s},endIndex(){return this.tk[this.tk.length-1].e},toObject(t=ja.defaultProtocol){return{type:this.t,value:this.toString(),isLink:this.isLink,href:this.toHref(t),start:this.startIndex(),end:this.endIndex()}},toFormattedObject(t){return{type:this.t,value:this.toFormattedString(t),isLink:this.isLink,href:this.toFormattedHref(t),start:this.startIndex(),end:this.endIndex()}},validate(t){return t.get("validate",this.toString(),this)},render(t){const e=this,n=this.toHref(t.get("defaultProtocol")),r=t.get("formatHref",n,this),o=t.get("tagName",n,e),i=this.toFormattedString(t),s={},l=t.get("className",n,e),a=t.get("target",n,e),c=t.get("rel",n,e),h=t.getObj("attributes",n,e),d=t.getObj("events",n,e);return s.href=r,l&&(s.class=l),a&&(s.target=a),c&&(s.rel=c),h&&dl(s,h),{tagName:o,attributes:s,content:i,eventListeners:d}}};const Ka=Ja("email",{isLink:!0,toHref(){return"mailto:"+this.toString()}}),_a=Ja("text"),Ua=Ja("nl"),Ga=Ja("url",{isLink:!0,toHref(t=ja.defaultProtocol){return this.hasProtocol()?this.v:`${t}://${this.v}`},hasProtocol(){const t=this.tk;return t.length>=2&&t[0].t!==Il&&t[1].t===ha}}),Qa=t=>new Ml(t);function Ya(t,e,n){const r=n[0].s,o=n[n.length-1].e;return new t(e.slice(r,o),n)}const Xa="undefined"!=typeof console&&console&&console.warn||(()=>{}),Za={scanner:null,parser:null,tokenQueue:[],pluginQueue:[],customSchemes:[],initialized:!1};function tc(t,e=!1){if(Za.initialized&&Xa(`linkifyjs: already initialized - will not register custom scheme "${t}" until manual call of linkify.init(). Register all schemes and plugins before invoking linkify the first time.`),!/^[0-9a-z]+(-[0-9a-z]+)*$/.test(t))throw new Error('linkifyjs: incorrect scheme format.\n1. Must only contain digits, lowercase ASCII letters or "-"\n2. Cannot start or end with "-"\n3. "-" cannot repeat');Za.customSchemes.push([t,e])}function ec(){Za.scanner=function(t=[]){const e={};Ml.groups=e;const n=new Ml;null==La&&(La=Va("aaa1rp3bb0ott3vie4c1le2ogado5udhabi7c0ademy5centure6ountant0s9o1tor4d0s1ult4e0g1ro2tna4f0l1rica5g0akhan5ency5i0g1rbus3force5tel5kdn3l0ibaba4pay4lfinanz6state5y2sace3tom5m0azon4ericanexpress7family11x2fam3ica3sterdam8nalytics7droid5quan4z2o0l2partments8p0le4q0uarelle8r0ab1mco4chi3my2pa2t0e3s0da2ia2sociates9t0hleta5torney7u0ction5di0ble3o3spost5thor3o0s4w0s2x0a2z0ure5ba0by2idu3namex4d1k2r0celona5laycard4s5efoot5gains6seball5ketball8uhaus5yern5b0c1t1va3cg1n2d1e0ats2uty4er2ntley5rlin4st0buy5t2f1g1h0arti5i0ble3d1ke2ng0o3o1z2j1lack0friday9ockbuster8g1omberg7ue3m0s1w2n0pparibas9o0ats3ehringer8fa2m1nd2o0k0ing5sch2tik2on4t1utique6x2r0adesco6idgestone9oadway5ker3ther5ussels7s1t1uild0ers6siness6y1zz3v1w1y1z0h3ca0b1fe2l0l1vinklein9m0era3p2non3petown5ital0one8r0avan4ds2e0er0s4s2sa1e1h1ino4t0ering5holic7ba1n1re3c1d1enter4o1rn3f0a1d2g1h0anel2nel4rity4se2t2eap3intai5ristmas6ome4urch5i0priani6rcle4sco3tadel4i0c2y3k1l0aims4eaning6ick2nic1que6othing5ud3ub0med6m1n1o0ach3des3ffee4llege4ogne5m0mbank4unity6pany2re3uter5sec4ndos3struction8ulting7tact3ractors9oking4l1p2rsica5untry4pon0s4rses6pa2r0edit0card4union9icket5own3s1uise0s6u0isinella9v1w1x1y0mru3ou3z2dad1nce3ta1e1ing3sun4y2clk3ds2e0al0er2s3gree4livery5l1oitte5ta3mocrat6ntal2ist5si0gn4v2hl2iamonds6et2gital5rect0ory7scount3ver5h2y2j1k1m1np2o0cs1tor4g1mains5t1wnload7rive4tv2ubai3nlop4pont4rban5vag2r2z2earth3t2c0o2deka3u0cation8e1g1mail3erck5nergy4gineer0ing9terprises10pson4quipment8r0icsson6ni3s0q1tate5t1u0rovision8s2vents5xchange6pert3osed4ress5traspace10fage2il1rwinds6th3mily4n0s2rm0ers5shion4t3edex3edback6rrari3ero6i0delity5o2lm2nal1nce1ial7re0stone6mdale6sh0ing5t0ness6j1k1lickr3ghts4r2orist4wers5y2m1o0o0d1tball6rd1ex2sale4um3undation8x2r0ee1senius7l1ogans4ntier7tr2ujitsu5n0d2rniture7tbol5yi3ga0l0lery3o1up4me0s3p1rden4y2b0iz3d0n2e0a1nt0ing5orge5f1g0ee3h1i0ft0s3ves2ing5l0ass3e1obal2o4m0ail3bh2o1x2n1odaddy5ld0point6f2o0dyear5g0le4p1t1v2p1q1r0ainger5phics5tis4een3ipe3ocery4up4s1t1u0cci3ge2ide2tars5ru3w1y2hair2mburg5ngout5us3bo2dfc0bank7ealth0care8lp1sinki6re1mes5iphop4samitsu7tachi5v2k0t2m1n1ockey4ldings5iday5medepot5goods5s0ense7nda3rse3spital5t0ing5t0els3mail5use3w2r1sbc3t1u0ghes5yatt3undai7ibm2cbc2e1u2d1e0ee3fm2kano4l1m0amat4db2mo0bilien9n0c1dustries8finiti5o2g1k1stitute6urance4e4t0ernational10uit4vestments10o1piranga7q1r0ish4s0maili5t0anbul7t0au2v3jaguar4va3cb2e0ep2tzt3welry6io2ll2m0p2nj2o0bs1urg4t1y2p0morgan6rs3uegos4niper7kaufen5ddi3e0rryhotels6logistics9properties14fh2g1h1i0a1ds2m1ndle4tchen5wi3m1n1oeln3matsu5sher5p0mg2n2r0d1ed3uokgroup8w1y0oto4z2la0caixa5mborghini8er3ncaster6d0rover6xess5salle5t0ino3robe5w0yer5b1c1ds2ease3clerc5frak4gal2o2xus4gbt3i0dl2fe0insurance9style7ghting6ke2lly3mited4o2ncoln4k2psy3ve1ing5k1lc1p2oan0s3cker3us3l1ndon4tte1o3ve3pl0financial11r1s1t0d0a3u0ndbeck6xe1ury5v1y2ma0drid4if1son4keup4n0agement7go3p1rket0ing3s4riott5shalls7ttel5ba2c0kinsey7d1e0d0ia3et2lbourne7me1orial6n0u2rckmsd7g1h1iami3crosoft7l1ni1t2t0subishi9k1l0b1s2m0a2n1o0bi0le4da2e1i1m1nash3ey2ster5rmon3tgage6scow4to0rcycles9v0ie4p1q1r1s0d2t0n1r2u0seum3ic4v1w1x1y1z2na0b1goya4me2vy3ba2c1e0c1t0bank4flix4work5ustar5w0s2xt0direct7us4f0l2g0o2hk2i0co2ke1on3nja3ssan1y5l1o0kia3rton4w0ruz3tv4p1r0a1w2tt2u1yc2z2obi1server7ffice5kinawa6layan0group9lo3m0ega4ne1g1l0ine5oo2pen3racle3nge4g0anic5igins6saka4tsuka4t2vh3pa0ge2nasonic7ris2s1tners4s1y3y2ccw3e0t2f0izer5g1h0armacy6d1ilips5one2to0graphy6s4ysio5ics1tet2ures6d1n0g1k2oneer5zza4k1l0ace2y0station9umbing5s3m1n0c2ohl2ker3litie5rn2st3r0america6xi3ess3ime3o0d0uctions8f1gressive8mo2perties3y5tection8u0dential9s1t1ub2w0c2y2qa1pon3uebec3st5racing4dio4e0ad1lestate6tor2y4cipes5d0stone5umbrella9hab3ise0n3t2liance6n0t0als5pair3ort3ublican8st0aurant8view0s5xroth6ich0ardli6oh3l1o1p2o0cks3deo3gers4om3s0vp3u0gby3hr2n2w0e2yukyu6sa0arland6fe0ty4kura4le1on3msclub4ung5ndvik0coromant12ofi4p1rl2s1ve2xo3b0i1s2c0b1haeffler7midt4olarships8ol3ule3warz5ience5ot3d1e0arch3t2cure1ity6ek2lect4ner3rvices6ven3w1x0y3fr2g1h0angrila6rp3ell3ia1ksha5oes2p0ping5uji3w3i0lk2na1gles5te3j1k0i0n2y0pe4l0ing4m0art3ile4n0cf3o0ccer3ial4ftbank4ware6hu2lar2utions7ng1y2y2pa0ce3ort2t3r0l2s1t0ada2ples4r1tebank4farm7c0group6ockholm6rage3e3ream4udio2y3yle4u0cks3pplies3y2ort5rf1gery5zuki5v1watch4iss4x1y0dney4stems6z2tab1ipei4lk2obao4rget4tamotors6r2too4x0i3c0i2d0k2eam2ch0nology8l1masek5nnis4va3f1g1h0d1eater2re6iaa2ckets5enda4ps2res2ol4j0maxx4x2k0maxx5l1m0all4n1o0day3kyo3ols3p1ray3shiba5tal3urs3wn2yota3s3r0ade1ing4ining5vel0ers0insurance16ust3v2t1ube2i1nes3shu4v0s2w1z2ua1bank3s2g1k1nicom3versity8o2ol2ps2s1y1z2va0cations7na1guard7c1e0gas3ntures6risign5mögensberater2ung14sicherung10t2g1i0ajes4deo3g1king4llas4n1p1rgin4sa1ion4va1o3laanderen9n1odka3lvo3te1ing3o2yage5u2wales2mart4ter4ng0gou5tch0es6eather0channel12bcam3er2site5d0ding5ibo2r3f1hoswho6ien2ki2lliamhill9n0dows4e1ners6me2olterskluwer11odside6rk0s2ld3w2s1tc1f3xbox3erox4ihuan4n2xx2yz3yachts4hoo3maxun5ndex5e1odobashi7ga2kohama6u0tube6t1un3za0ppos4ra3ero3ip2m1one3uerich6w2")),null==$a&&($a=Va("ελ1υ2бг1ел3дети4ею2католик6ом3мкд2он1сква6онлайн5рг3рус2ф2сайт3рб3укр3қаз3հայ3ישראל5קום3ابوظبي5رامكو5لاردن4بحرين5جزائر5سعودية6عليان5مغرب5مارات5یران5بارت2زار4يتك3ھارت5تونس4سودان3رية5شبكة4عراق2ب2مان4فلسطين6قطر3كاثوليك6وم3مصر2ليسيا5وريتانيا7قع4همراه5پاکستان7ڀارت4कॉम3नेट3भारत0म्3ोत5संगठन5বাংলা5ভারত2ৰত4ਭਾਰਤ4ભારત4ଭାରତ4இந்தியா6லங்கை6சிங்கப்பூர்11భారత్5ಭಾರತ4ഭാരതം5ලංකා4คอม3ไทย3ລາວ3გე2みんな3アマゾン4クラウド4グーグル4コム2ストア3セール3ファッション6ポイント4世界2中信1国1國1文网3亚马逊3企业2佛山2信息2健康2八卦2公司1益2台湾1灣2商城1店1标2嘉里0大酒店5在线2大拿2天主教3娱乐2家電2广东2微博2慈善2我爱你3手机2招聘2政务1府2新加坡2闻2时尚2書籍2机构2淡马锡3游戏2澳門2点看2移动2组织机构4网址1店1站1络2联通2谷歌2购物2通販2集团2電訊盈科4飞利浦3食品2餐厅2香格里拉3港2닷넷1컴2삼성2한국2")),El(n,"'",oa),El(n,"{",jl),El(n,"}",Hl),El(n,"[",Wl),El(n,"]",ql),El(n,"(",Jl),El(n,")",Kl),El(n,"<",_l),El(n,">",Ul),El(n,"（",Gl),El(n,"）",Ql),El(n,"「",Yl),El(n,"」",Xl),El(n,"『",Zl),El(n,"』",ta),El(n,"＜",ea),El(n,"＞",na),El(n,"&",ra),El(n,"*",ia),El(n,"@",sa),El(n,"`",aa),El(n,"^",ca),El(n,":",ha),El(n,",",da),El(n,"$",pa),El(n,".",ua),El(n,"=",fa),El(n,"!",ma),El(n,"-",ga),El(n,"%",ya),El(n,"|",wa),El(n,"+",va),El(n,"#",ka),El(n,"?",ba),El(n,'"',xa),El(n,"/",Ca),El(n,";",Ma),El(n,"~",Oa),El(n,"_",Ta),El(n,"\\",la),El(n,"・",Sa);const r=Ol(n,Pa,Bl,{[pl]:!0});Ol(r,Pa,r);const o=Ol(r,Da,Dl,{[ml]:!0}),i=Ol(r,Ra,Rl,{[gl]:!0}),s=Ol(n,Da,Nl,{[ul]:!0});Ol(s,Pa,o),Ol(s,Da,s),Ol(o,Pa,o),Ol(o,Da,o);const l=Ol(n,Ra,Al,{[fl]:!0});Ol(l,Da),Ol(l,Pa,i),Ol(l,Ra,l),Ol(i,Pa,i),Ol(i,Da),Ol(i,Ra,i);const a=El(n,"\n",Vl,{[bl]:!0}),c=El(n,"\r",Fl,{[bl]:!0}),h=Ol(n,za,Fl,{[bl]:!0});El(n,"￼",h),El(c,"\n",a),El(c,"￼",h),Ol(c,za,h),El(h,"\r"),El(h,"\n"),Ol(h,za,h),El(h,"￼",h);const d=Ol(n,Ia,Ea,{[wl]:!0});El(d,"#"),Ol(d,Ia,d),El(d,"️",d);const p=El(d,"‍");El(p,"#"),Ol(p,Ia,d);const u=[[Da,s],[Pa,o]],f=[[Da,null],[Ra,l],[Pa,i]];for(let t=0;t<La.length;t++)Fa(n,La[t],Pl,Nl,u);for(let t=0;t<$a.length;t++)Fa(n,$a[t],zl,Al,f);Sl(Pl,{tld:!0,ascii:!0},e),Sl(zl,{utld:!0,alpha:!0},e),Fa(n,"file",Ll,Nl,u),Fa(n,"mailto",Ll,Nl,u),Fa(n,"http",$l,Nl,u),Fa(n,"https",$l,Nl,u),Fa(n,"ftp",$l,Nl,u),Fa(n,"ftps",$l,Nl,u),Sl(Ll,{scheme:!0,ascii:!0},e),Sl($l,{slashscheme:!0,ascii:!0},e),t=t.sort(((t,e)=>t[0]>e[0]?1:-1));for(let e=0;e<t.length;e++){const r=t[e][0],o=t[e][1]?{[vl]:!0}:{[kl]:!0};r.indexOf("-")>=0?o[yl]=!0:Da.test(r)?Pa.test(r)?o[ml]=!0:o[ul]=!0:o[pl]=!0,Tl(n,r,r,o)}return Tl(n,"localhost",Il,{ascii:!0}),n.jd=new Ml(Na),{start:n,tokens:dl({groups:e},Aa)}}(Za.customSchemes);for(let t=0;t<Za.tokenQueue.length;t++)Za.tokenQueue[t][1]({scanner:Za.scanner});Za.parser=function({groups:t}){const e=t.domain.concat([ra,ia,sa,la,aa,ca,pa,fa,ga,Bl,ya,wa,va,ka,Ca,Na,Oa,Ta]),n=[ha,da,ua,ma,ya,ba,xa,Ma,_l,Ul,jl,Hl,ql,Wl,Jl,Kl,Gl,Ql,Yl,Xl,Zl,ta,ea,na],r=[ra,oa,ia,la,aa,ca,pa,fa,ga,jl,Hl,ya,wa,va,ka,ba,Ca,Na,Oa,Ta],o=Qa(),i=El(o,Oa);Cl(i,r,i),Cl(i,t.domain,i);const s=Qa(),l=Qa(),a=Qa();Cl(o,t.domain,s),Cl(o,t.scheme,l),Cl(o,t.slashscheme,a),Cl(s,r,i),Cl(s,t.domain,s);const c=El(s,sa);El(i,sa,c),El(l,sa,c),El(a,sa,c);const h=El(i,ua);Cl(h,r,i),Cl(h,t.domain,i);const d=Qa();Cl(c,t.domain,d),Cl(d,t.domain,d);const p=El(d,ua);Cl(p,t.domain,d);const u=Qa(Ka);Cl(p,t.tld,u),Cl(p,t.utld,u),El(c,Il,u);const f=El(d,ga);El(f,ga,f),Cl(f,t.domain,d),Cl(u,t.domain,d),El(u,ua,p),El(u,ga,f);const m=El(u,ha);Cl(m,t.numeric,Ka);const g=El(s,ga),y=El(s,ua);El(g,ga,g),Cl(g,t.domain,s),Cl(y,r,i),Cl(y,t.domain,s);const w=Qa(Ga);Cl(y,t.tld,w),Cl(y,t.utld,w),Cl(w,t.domain,s),Cl(w,r,i),El(w,ua,y),El(w,ga,g),El(w,sa,c);const v=El(w,ha),k=Qa(Ga);Cl(v,t.numeric,k);const b=Qa(Ga),x=Qa();Cl(b,e,b),Cl(b,n,x),Cl(x,e,b),Cl(x,n,x),El(w,Ca,b),El(k,Ca,b);const S=El(l,ha),M=El(a,ha),C=El(M,Ca),O=El(C,Ca);Cl(l,t.domain,s),El(l,ua,y),El(l,ga,g),Cl(a,t.domain,s),El(a,ua,y),El(a,ga,g),Cl(S,t.domain,b),El(S,Ca,b),El(S,ba,b),Cl(O,t.domain,b),Cl(O,e,b),El(O,Ca,b);const T=[[jl,Hl],[Wl,ql],[Jl,Kl],[_l,Ul],[Gl,Ql],[Yl,Xl],[Zl,ta],[ea,na]];for(let t=0;t<T.length;t++){const[r,o]=T[t],i=El(b,r);El(x,r,i),El(i,o,b);const s=Qa(Ga);Cl(i,e,s);const l=Qa();Cl(i,n),Cl(s,e,s),Cl(s,n,l),Cl(l,e,s),Cl(l,n,l),El(s,o,b),El(l,o,b)}return El(o,Il,w),El(o,Vl,Ua),{start:o,tokens:Aa}}(Za.scanner.tokens);for(let t=0;t<Za.pluginQueue.length;t++)Za.pluginQueue[t][1]({scanner:Za.scanner,parser:Za.parser});return Za.initialized=!0,Za}function nc(t){return Za.initialized||ec(),function(t,e,n){let r=n.length,o=0,i=[],s=[];for(;o<r;){let l=t,a=null,c=null,h=0,d=null,p=-1;for(;o<r&&!(a=l.go(n[o].t));)s.push(n[o++]);for(;o<r&&(c=a||l.go(n[o].t));)a=null,l=c,l.accepts()?(p=0,d=l):p>=0&&p++,o++,h++;if(p<0)o-=h,o<r&&(s.push(n[o]),o++);else{s.length>0&&(i.push(Ya(_a,e,s)),s=[]),o-=p,h-=p;const t=d.t,r=n.slice(o-h,o);i.push(Ya(t,e,r))}}return s.length>0&&i.push(Ya(_a,e,s)),i}(Za.parser.start,t,Ba(Za.scanner.start,t))}function rc(t,e=null,n=null){if(e&&"object"==typeof e){if(n)throw Error(`linkifyjs: Invalid link type ${e}; must be a string`);n=e,e=null}const r=new Ha(n),o=nc(t),i=[];for(let t=0;t<o.length;t++){const n=o[t];!n.isLink||e&&n.t!==e||!r.check(n)||i.push(n.toFormattedObject(r))}return i}function oc(t){return new we({key:new be("autolink"),appendTransaction:(e,n,r)=>{const o=e.some((t=>t.docChanged))&&!n.doc.eq(r.doc),i=e.some((t=>t.getMeta("preventAutolink")));if(!o||i)return;const{tr:s}=r,l=function(t,e){const n=new Yt(t);return e.forEach((t=>{t.steps.forEach((t=>{n.step(t)}))})),n}(n.doc,[...e]),a=function(t){const{mapping:e,steps:n}=t,r=[];return e.maps.forEach(((t,o)=>{const i=[];if(t.ranges.length)t.forEach(((t,e)=>{i.push({from:t,to:e})}));else{const{from:t,to:e}=n[o];if(void 0===t||void 0===e)return;i.push({from:t,to:e})}i.forEach((({from:t,to:n})=>{const i=e.slice(o).map(t,-1),s=e.slice(o).map(n),l=e.invert().map(i,-1),a=e.invert().map(s);r.push({oldRange:{from:l,to:a},newRange:{from:i,to:s}})}))})),as(r)}(l);return a.forEach((({newRange:e})=>{const n=function(t,e,n){const r=[];return t.nodesBetween(e.from,e.to,((t,e)=>{n(t)&&r.push({node:t,pos:e})})),r}(r.doc,e,(t=>t.isTextblock));let o,i;if(n.length>1?(o=n[0],i=r.doc.textBetween(o.pos,o.pos+o.node.nodeSize,void 0," ")):n.length&&r.doc.textBetween(e.from,e.to," "," ").endsWith(" ")&&(o=n[0],i=r.doc.textBetween(o.pos,e.to,void 0," ")),o&&i){const e=i.split(" ").filter((t=>""!==t));if(e.length<=0)return!1;const n=e[e.length-1],a=o.pos+i.lastIndexOf(n);if(!n)return!1;const c=nc(n).map((e=>e.toObject(t.defaultProtocol)));if(!(1===(l=c).length?l[0].isLink:3===l.length&&l[1].isLink&&["()","[]"].includes(l[0].value+l[2].value)))return!1;c.filter((t=>t.isLink)).map((t=>({...t,from:a+t.start+1,to:a+t.end+1}))).filter((t=>!r.schema.marks.code||!r.doc.rangeHasMark(t.from,t.to,r.schema.marks.code))).filter((e=>t.validate(e.value))).filter((e=>t.shouldAutoLink(e.value))).forEach((e=>{cs(e.from,e.to,r.doc).some((e=>e.mark.type===t.type))||s.addMark(e.from,e.to,t.type.create({href:e.href}))}))}var l})),s.steps.length?s:void 0}})}nc.scan=Ba;const ic=/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g;function sc(t,e){const n=["http","https","ftp","ftps","mailto","tel","callto","sms","cid","xmpp"];return e&&e.forEach((t=>{const e="string"==typeof t?t:t.scheme;e&&n.push(e)})),!t||t.replace(ic,"").match(new RegExp(`^(?:(?:${n.join("|")}):|[^a-z]|[a-z+.-]+(?:[^a-z+.-:]|$))`,"i"))}const lc=Di.create({name:"link",priority:1e3,keepOnSplit:!1,exitable:!0,onCreate(){this.options.validate&&!this.options.shouldAutoLink&&(this.options.shouldAutoLink=this.options.validate,console.warn("The `validate` option is deprecated. Rename to the `shouldAutoLink` option instead.")),this.options.protocols.forEach((t=>{"string"!=typeof t?tc(t.scheme,t.optionalSlashes):tc(t)}))},onDestroy(){Ml.groups={},Za.scanner=null,Za.parser=null,Za.tokenQueue=[],Za.pluginQueue=[],Za.customSchemes=[],Za.initialized=!1},inclusive(){return this.options.autolink},addOptions:()=>({openOnClick:!0,linkOnPaste:!0,autolink:!0,protocols:[],defaultProtocol:"http",HTMLAttributes:{target:"_blank",rel:"noopener noreferrer nofollow",class:null},isAllowedUri:(t,e)=>!!sc(t,e.protocols),validate:t=>!!t,shouldAutoLink:t=>!!t}),addAttributes(){return{href:{default:null,parseHTML:t=>t.getAttribute("href")},target:{default:this.options.HTMLAttributes.target},rel:{default:this.options.HTMLAttributes.rel},class:{default:this.options.HTMLAttributes.class}}},parseHTML(){return[{tag:"a[href]",getAttrs:t=>{const e=t.getAttribute("href");return!(!e||!this.options.isAllowedUri(e,{defaultValidate:t=>!!sc(t,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol}))&&null}}]},renderHTML({HTMLAttributes:t}){return this.options.isAllowedUri(t.href,{defaultValidate:t=>!!sc(t,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?["a",gi(this.options.HTMLAttributes,t),0]:["a",gi(this.options.HTMLAttributes,{...t,href:""}),0]},addCommands(){return{setLink:t=>({chain:e})=>{const{href:n}=t;return!!this.options.isAllowedUri(n,{defaultValidate:t=>!!sc(t,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&e().setMark(this.name,t).setMeta("preventAutolink",!0).run()},toggleLink:t=>({chain:e})=>{const{href:n}=t;return!!this.options.isAllowedUri(n,{defaultValidate:t=>!!sc(t,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&e().toggleMark(this.name,t,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()},unsetLink:()=>({chain:t})=>t().unsetMark(this.name,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()}},addPasteRules(){return[Ns({find:t=>{const e=[];if(t){const{protocols:n,defaultProtocol:r}=this.options,o=rc(t).filter((t=>t.isLink&&this.options.isAllowedUri(t.value,{defaultValidate:t=>!!sc(t,n),protocols:n,defaultProtocol:r})));o.length&&o.forEach((t=>e.push({text:t.value,data:{href:t.href},index:t.start})))}return e},type:this.type,getAttributes:t=>{var e;return{href:null===(e=t.data)||void 0===e?void 0:e.href}}})]},addProseMirrorPlugins(){const t=[],{protocols:e,defaultProtocol:n}=this.options;var r;return this.options.autolink&&t.push(oc({type:this.type,defaultProtocol:this.options.defaultProtocol,validate:t=>this.options.isAllowedUri(t,{defaultValidate:t=>!!sc(t,e),protocols:e,defaultProtocol:n}),shouldAutoLink:this.options.shouldAutoLink})),!0===this.options.openOnClick&&t.push((r={type:this.type},new we({key:new be("handleClickLink"),props:{handleClick:(t,e,n)=>{var o,i;if(0!==n.button)return!1;if(!t.editable)return!1;let s=n.target;const l=[];for(;"DIV"!==s.nodeName;)l.push(s),s=s.parentNode;if(!l.find((t=>"A"===t.nodeName)))return!1;const a=ls(t.state,r.type.name),c=n.target,h=null!==(o=null==c?void 0:c.href)&&void 0!==o?o:a.href,d=null!==(i=null==c?void 0:c.target)&&void 0!==i?i:a.target;return!(!c||!h||(window.open(h,d),0))}}}))),this.options.linkOnPaste&&t.push(function(t){return new we({key:new be("handlePasteLink"),props:{handlePaste:(e,n,r)=>{const{state:o}=e,{selection:i}=o,{empty:s}=i;if(s)return!1;let l="";r.content.forEach((t=>{l+=t.textContent}));const a=rc(l,{defaultProtocol:t.defaultProtocol}).find((t=>t.isLink&&t.value===l));return!(!l||!a||(t.editor.commands.setMark(t.type,{href:a.href}),0))}}})}({editor:this.editor,defaultProtocol:this.options.defaultProtocol,type:this.type})),t}}),ac=Es.create({name:"listItem",addOptions:()=>({HTMLAttributes:{},bulletListTypeName:"bulletList",orderedListTypeName:"orderedList"}),content:"paragraph block*",defining:!0,parseHTML:()=>[{tag:"li"}],renderHTML({HTMLAttributes:t}){return["li",gi(this.options.HTMLAttributes,t),0]},addKeyboardShortcuts(){return{Enter:()=>this.editor.commands.splitListItem(this.name),Tab:()=>this.editor.commands.sinkListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)}}}),cc=Es.create({name:"paragraph",priority:1e3,addOptions:()=>({HTMLAttributes:{}}),group:"block",content:"inline*",parseHTML:()=>[{tag:"p"}],renderHTML({HTMLAttributes:t}){return["p",gi(this.options.HTMLAttributes,t),0]},addCommands(){return{setParagraph:()=>({commands:t})=>t.setNode(this.name)}},addKeyboardShortcuts(){return{"Mod-Alt-0":()=>this.editor.commands.setParagraph()}}}),hc=Es.create({name:"text",group:"inline"});return t.Bold=Ps,t.BulletList=$s,t.CharacterCount=Bs,t.Document=Fs,t.Editor=class extends di{constructor(t={}){super(),this.isFocused=!1,this.isInitialized=!1,this.extensionStorage={},this.options={element:document.createElement("div"),content:"",injectCSS:!0,injectNonce:void 0,extensions:[],autofocus:!1,editable:!0,editorProps:{},parseOptions:{},coreExtensionOptions:{},enableInputRules:!0,enablePasteRules:!0,enableCoreExtensions:!0,enableContentCheck:!1,onBeforeCreate:()=>null,onCreate:()=>null,onUpdate:()=>null,onSelectionUpdate:()=>null,onTransaction:()=>null,onFocus:()=>null,onBlur:()=>null,onDestroy:()=>null,onContentError:({error:t})=>{throw t},onPaste:()=>null,onDrop:()=>null},this.isCapturingTransaction=!1,this.capturedTransaction=null,this.setOptions(t),this.createExtensionManager(),this.createCommandManager(),this.createSchema(),this.on("beforeCreate",this.options.onBeforeCreate),this.emit("beforeCreate",{editor:this}),this.on("contentError",this.options.onContentError),this.createView(),this.injectCSS(),this.on("create",this.options.onCreate),this.on("update",this.options.onUpdate),this.on("selectionUpdate",this.options.onSelectionUpdate),this.on("transaction",this.options.onTransaction),this.on("focus",this.options.onFocus),this.on("blur",this.options.onBlur),this.on("destroy",this.options.onDestroy),this.on("drop",(({event:t,slice:e,moved:n})=>this.options.onDrop(t,e,n))),this.on("paste",(({event:t,slice:e})=>this.options.onPaste(t,e))),window.setTimeout((()=>{this.isDestroyed||(this.commands.focus(this.options.autofocus),this.emit("create",{editor:this}),this.isInitialized=!0)}),0)}get storage(){return this.extensionStorage}get commands(){return this.commandManager.commands}chain(){return this.commandManager.chain()}can(){return this.commandManager.can()}injectCSS(){this.options.injectCSS&&document&&(this.css=function(t,e){const n=document.querySelector("style[data-tiptap-style]");if(null!==n)return n;const r=document.createElement("style");return e&&r.setAttribute("nonce",e),r.setAttribute("data-tiptap-style",""),r.innerHTML=t,document.getElementsByTagName("head")[0].appendChild(r),r}('.ProseMirror {\n  position: relative;\n}\n\n.ProseMirror {\n  word-wrap: break-word;\n  white-space: pre-wrap;\n  white-space: break-spaces;\n  -webkit-font-variant-ligatures: none;\n  font-variant-ligatures: none;\n  font-feature-settings: "liga" 0; /* the above doesn\'t seem to work in Edge */\n}\n\n.ProseMirror [contenteditable="false"] {\n  white-space: normal;\n}\n\n.ProseMirror [contenteditable="false"] [contenteditable="true"] {\n  white-space: pre-wrap;\n}\n\n.ProseMirror pre {\n  white-space: pre-wrap;\n}\n\nimg.ProseMirror-separator {\n  display: inline !important;\n  border: none !important;\n  margin: 0 !important;\n  width: 0 !important;\n  height: 0 !important;\n}\n\n.ProseMirror-gapcursor {\n  display: none;\n  pointer-events: none;\n  position: absolute;\n  margin: 0;\n}\n\n.ProseMirror-gapcursor:after {\n  content: "";\n  display: block;\n  position: absolute;\n  top: -2px;\n  width: 20px;\n  border-top: 1px solid black;\n  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;\n}\n\n@keyframes ProseMirror-cursor-blink {\n  to {\n    visibility: hidden;\n  }\n}\n\n.ProseMirror-hideselection *::selection {\n  background: transparent;\n}\n\n.ProseMirror-hideselection *::-moz-selection {\n  background: transparent;\n}\n\n.ProseMirror-hideselection * {\n  caret-color: transparent;\n}\n\n.ProseMirror-focused .ProseMirror-gapcursor {\n  display: block;\n}\n\n.tippy-box[data-animation=fade][data-state=hidden] {\n  opacity: 0\n}',this.options.injectNonce))}setOptions(t={}){this.options={...this.options,...t},this.view&&this.state&&!this.isDestroyed&&(this.options.editorProps&&this.view.setProps(this.options.editorProps),this.view.updateState(this.state))}setEditable(t,e=!0){this.setOptions({editable:t}),e&&this.emit("update",{editor:this,transaction:this.state.tr})}get isEditable(){return this.options.editable&&this.view&&this.view.editable}get state(){return this.view.state}registerPlugin(t,e){const n=wi(e)?e(t,[...this.state.plugins]):[...this.state.plugins,t],r=this.state.reconfigure({plugins:n});return this.view.updateState(r),r}unregisterPlugin(t){if(this.isDestroyed)return;const e=this.state.plugins;let n=e;if([].concat(t).forEach((t=>{const r="string"==typeof t?`${t}$`:t.key;n=e.filter((t=>!t.key.startsWith(r)))})),e.length===n.length)return;const r=this.state.reconfigure({plugins:n});return this.view.updateState(r),r}createExtensionManager(){var t,e;const n=[...this.options.enableCoreExtensions?[ks,Vi.configure({blockSeparator:null===(e=null===(t=this.options.coreExtensionOptions)||void 0===t?void 0:t.clipboardTextSerializer)||void 0===e?void 0:e.blockSeparator}),ws,bs,xs,Ms,vs,Ss].filter((t=>"object"!=typeof this.options.enableCoreExtensions||!1!==this.options.enableCoreExtensions[t.name])):[],...this.options.extensions].filter((t=>["extension","node","mark"].includes(null==t?void 0:t.type)));this.extensionManager=new Li(n,this)}createCommandManager(){this.commandManager=new hi({editor:this})}createSchema(){this.schema=this.extensionManager.schema}createView(){var t;let e;try{e=rs(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:this.options.enableContentCheck})}catch(t){if(!(t instanceof Error&&["[tiptap error]: Invalid JSON content","[tiptap error]: Invalid HTML content"].includes(t.message)))throw t;this.emit("contentError",{editor:this,error:t,disableCollaboration:()=>{this.storage.collaboration&&(this.storage.collaboration.isDisabled=!0),this.options.extensions=this.options.extensions.filter((t=>"collaboration"!==t.name)),this.createExtensionManager()}}),e=rs(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:!1})}const n=Ui(e,this.options.autofocus);this.view=new Io(this.options.element,{...this.options.editorProps,attributes:{role:"textbox",...null===(t=this.options.editorProps)||void 0===t?void 0:t.attributes},dispatchTransaction:this.dispatchTransaction.bind(this),state:ge.create({doc:e,selection:n||void 0})});const r=this.state.reconfigure({plugins:this.extensionManager.plugins});this.view.updateState(r),this.createNodeViews(),this.prependClass();this.view.dom.editor=this}createNodeViews(){this.view.isDestroyed||this.view.setProps({nodeViews:this.extensionManager.nodeViews})}prependClass(){this.view.dom.className=`tiptap ${this.view.dom.className}`}captureTransaction(t){this.isCapturingTransaction=!0,t(),this.isCapturingTransaction=!1;const e=this.capturedTransaction;return this.capturedTransaction=null,e}dispatchTransaction(t){if(this.view.isDestroyed)return;if(this.isCapturingTransaction)return this.capturedTransaction?void t.steps.forEach((t=>{var e;return null===(e=this.capturedTransaction)||void 0===e?void 0:e.step(t)})):void(this.capturedTransaction=t);const e=this.state.apply(t),n=!this.state.selection.eq(e.selection);this.emit("beforeTransaction",{editor:this,transaction:t,nextState:e}),this.view.updateState(e),this.emit("transaction",{editor:this,transaction:t}),n&&this.emit("selectionUpdate",{editor:this,transaction:t});const r=t.getMeta("focus"),o=t.getMeta("blur");r&&this.emit("focus",{editor:this,event:r.event,transaction:t}),o&&this.emit("blur",{editor:this,event:o.event,transaction:t}),t.docChanged&&!t.getMeta("preventUpdate")&&this.emit("update",{editor:this,transaction:t})}getAttributes(t){return ls(this.state,t)}isActive(t,e){const n="string"==typeof t?t:null,r="string"==typeof t?e:t;return function(t,e,n={}){if(!e)return ts(t,null,n)||ds(t,null,n);const r=es(e,t.schema);return"node"===r?ts(t,e,n):"mark"===r&&ds(t,e,n)}(this.state,n,r)}getJSON(){return this.state.doc.toJSON()}getHTML(){return Mi(this.state.doc.content,this.schema)}getText(t){const{blockSeparator:e="\n\n",textSerializers:n={}}=t||{};return function(t,e){return Bi(t,{from:0,to:t.content.size},e)}(this.state.doc,{blockSeparator:e,textSerializers:{...Fi(this.schema),...n}})}get isEmpty(){return us(this.state.doc)}getCharacterCount(){return console.warn('[tiptap warn]: "editor.getCharacterCount()" is deprecated. Please use "editor.storage.characterCount.characters()" instead.'),this.state.doc.content.size-2}destroy(){if(this.emit("destroy"),this.view){const t=this.view.dom;t&&t.editor&&delete t.editor,this.view.destroy()}this.removeAllListeners()}get isDestroyed(){var t;return!(null===(t=this.view)||void 0===t?void 0:t.docView)}$node(t,e){var n;return(null===(n=this.$doc)||void 0===n?void 0:n.querySelector(t,e))||null}$nodes(t,e){var n;return(null===(n=this.$doc)||void 0===n?void 0:n.querySelectorAll(t,e))||null}$pos(t){const e=this.state.doc.resolve(t);return new Cs(e,this)}get $doc(){return this.$pos(0)}},t.History=il,t.Italic=hl,t.Link=lc,t.ListItem=ac,t.Paragraph=cc,t.Text=hc,t}({});
