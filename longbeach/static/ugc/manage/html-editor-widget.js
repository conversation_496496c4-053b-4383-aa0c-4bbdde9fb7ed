$(() => {
  const $description = $("#id_description");
  $(`
      <div id="description_editor">
        <div class="editor_body">
          <div class="editor_menu">
            <button
              id="menu_btn_bold"
              onclick="window.editor.chain().focus().toggleBold().run()"
              title="Bold"
              type="button"
            >
              <div>
                <svg
                  fill="none"
                  height="16"
                  stroke="currentColor"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  viewBox="0 0 24 24"
                  width="16"
                >
                  <path d="M6 4h8a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z" />
                  <path d="M6 12h9a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z" />
                </svg>
              </div>
            </button>
            <button
              id="menu_btn_italic"
              onclick="window.editor.chain().focus().toggleItalic().run()"
              title="Italic"
              type="button"
            >
              <div>
                <svg
                  fill="none"
                  height="16"
                  stroke="currentColor"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  viewBox="0 0 24 24"
                  width="16"
                >
                  <path d="M19 4h-9M14 20H5M14.7 4.7L9.2 19.4" />
                </svg>
              </div>
            </button>
            <button
              id="menu_btn_bulletList"
              onclick="window.editor.chain().focus().toggleBulletList().run()"
              title="Bullet list"
              type="button"
            >
              <div>
                <svg
                  fill="none"
                  height="16"
                  stroke="currentColor"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2.5"
                  viewBox="0 0 24 24"
                  width="16"
                >
                  <line x1="8" x2="21" y1="6" y2="6" />
                  <line x1="8" x2="21" y1="12" y2="12" />
                  <line x1="8" x2="21" y1="18" y2="18" />
                  <line x1="3" x2="3.01" y1="6" y2="6" />
                  <line x1="3" x2="3.01" y1="12" y2="12" />
                  <line x1="3" x2="3.01" y1="18" y2="18" />
                </svg>
              </div>
            </button>
            <button id="menu_btn_link" onclick="setLink()" type="button">
              <div>
                <svg width="17" height="17" fill="none">
                  <path
                    clip-rule="evenodd"
                    d="M11.688.096A5.278 5.278 0 0 0 7.93 1.65L6.16 3.418a.94.94 0 0 0 0 1.328.935.935 0 0 0 1.326-.002l1.769-1.769a3.44 3.44 0 0 1 5.75 1.542 3.438 3.438 0 0 1-.889 3.32l-1.769 1.77a.937.937 0 0 0 1.326 1.325l1.769-1.768v-.001a5.307 5.307 0 0 0 0-7.512A5.308 5.308 0 0 0 11.689.096ZM7.93 10.489l2.65-2.651a.937.937 0 1 0-1.325-1.326l-2.651 2.65a.937.937 0 1 0 1.326 1.326Zm1.324 4.862A5.276 5.276 0 0 1 5.5 16.904a5.311 5.311 0 0 1-5.314-5.31c.001-1.41.561-2.76 1.558-3.756l1.769-1.769a.94.94 0 0 1 1.326-.001.936.936 0 0 1 0 1.326l-1.77 1.769a3.438 3.438 0 1 0 4.862 4.862l1.77-1.768a.938.938 0 0 1 1.326 1.326h-.002L9.255 15.35Z"
                    fill-rule="evenodd"
                    fill="#000"
                  />
                </svg>
              </div>
            </button>
            <button onclick="window.editor.chain().focus().unsetLink().run()" type="button">
              <div>
                <svg width="17" height="17" fill="none" viewBox="8 7 18 18">
                  <path
                    d="M16.977 12.471c.066-.09.14-.175.22-.256l2.746-2.746c.396-.396.915-.663 1.475-.698a2.202 2.202 0 0 1 1.707.644c.452.452.685 1.065.645 1.707-.036.56-.303 1.08-.699 1.475l-2.746 2.746a2.27 2.27 0 0 1-.256.22c-.498.368-.532 1.102-.095 1.54.36.36.933.407 1.341.102.162-.123.319-.257.467-.405l2.8-2.8a4.251 4.251 0 0 0 1.25-3.022 4.246 4.246 0 0 0-1.253-3.02 4.27 4.27 0 0 0-6.04-.001l-2.8 2.8a4.42 4.42 0 0 0-.405.467 1.03 1.03 0 0 0 .102 1.341c.438.438 1.173.403 1.54-.094ZM16.692 18.941c-.067.09-.14.175-.22.256l-2.746 2.746c-.396.396-.916.663-1.475.699a2.202 2.202 0 0 1-1.707-.645 2.202 2.202 0 0 1-.645-1.707c.035-.56.302-1.079.699-1.475l2.745-2.746c.08-.08.167-.153.257-.22.498-.368.532-1.101.095-1.54a1.03 1.03 0 0 0-1.342-.101 4.308 4.308 0 0 0-.466.405l-2.8 2.8a4.25 4.25 0 0 0-1.251 3.02c0 1.096.417 2.188 1.252 3.02a4.27 4.27 0 0 0 6.04.003l2.8-2.8a4.42 4.42 0 0 0 .405-.467 1.03 1.03 0 0 0-.102-1.34c-.437-.44-1.172-.405-1.54.092ZM21.19 19.325a.522.522 0 0 0-.738.737l2.026 2.025a.521.521 0 0 0 .738-.738l-2.026-2.024ZM25.208 18.153l-2.828-.451a.522.522 0 0 0-.164 1.031l2.828.451a.522.522 0 1 0 .163-1.031ZM19.86 21.087a.523.523 0 0 0-1.03.165l.452 2.829a.521.521 0 1 0 1.03-.166l-.451-2.828ZM12.478 12.088a.52.52 0 1 0 .738-.738L11.19 9.325a.522.522 0 0 0-.738.738l2.025 2.025ZM13.808 10.325a.522.522 0 1 0 1.031-.165l-.45-2.83a.522.522 0 0 0-1.03.165l.449 2.83ZM8.46 13.258l2.83.451a.522.522 0 0 0 .164-1.03l-2.83-.45a.52.52 0 1 0-.164 1.03Z"
                    fill="#000"
                  />
                </svg>
              </div>
            </button>
          </div>
          <div class="editor_content">
          </div>
        </div>
        <div class="word_count">
        </div>
      </div>
    `).insertAfter($description);
  const $description_editor = $("#description_editor .editor_content");
  const $word_count = $("#description_editor .word_count");
  const checkActives = (editor) =>
    ["bold", "italic", "bulletList", "link"].forEach((btn) =>
      $(`#menu_btn_${btn}`)[editor.isActive(btn) ? "addClass" : "removeClass"](
        "active",
      ),
    );

  window.editor = new Tiptap.Editor({
    element: $description_editor[0],
    extensions: [
      Tiptap.Bold,
      Tiptap.BulletList,
      Tiptap.CharacterCount,
      Tiptap.Document,
      Tiptap.Italic,
      Tiptap.History,
      Tiptap.Link.configure({
        openOnClick: false,
        autolink: true,
        defaultProtocol: "https",
        protocols: ["http", "https"],
        isAllowedUri: (url, ctx) => {
          try {
            const parsedUrl = url.includes(":")
              ? new URL(url)
              : new URL(`${ctx.defaultProtocol}://${url}`);

            if (!ctx.defaultValidate(parsedUrl.href)) {
              return false;
            }

            const disallowedProtocols = ["ftp", "file", "mailto"];
            const protocol = parsedUrl.protocol.replace(":", "");

            if (disallowedProtocols.includes(protocol)) {
              return false;
            }

            // only allow protocols specified in ctx.protocols
            const allowedProtocols = ctx.protocols.map((p) =>
              typeof p === "string" ? p : p.scheme,
            );

            if (!allowedProtocols.includes(protocol)) {
              return false;
            }

            return true;
          } catch {
            return false;
          }
        },
        shouldAutoLink: (url) => {
          try {
            const parsedUrl = url.includes(":")
              ? new URL(url)
              : new URL(`https://${url}`);

            return true;
          } catch {
            return false;
          }
        },
      }).extend({
        addKeyboardShortcuts() {
          return {
            "Mod-k": () => {
              setLink();
              return true;
            },
          };
        },
      }),
      Tiptap.ListItem,
      Tiptap.Paragraph,
      Tiptap.Text,
    ],
    content: $description.val(),
    onCreate: ({ editor }) => {
      const wc = editor.storage.characterCount.words();
      $word_count.text(`${wc} word${wc === 1 ? "" : "s"}`);
      checkActives(editor);
    },
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      const wc = editor.storage.characterCount.words();
      $description.val(wc ? html : "");
      $word_count.text(`${wc} word${wc === 1 ? "" : "s"}`);
      checkActives(editor);
    },
    onSelectionUpdate({ editor }) {
      checkActives(editor);
    },
  });

  window.setLink = () => {
    const previousUrl = editor.getAttributes("link").href;
    const url = window.prompt("URL", previousUrl);

    // cancelled
    if (url === null) {
      return;
    }

    // empty
    if (url === "") {
      editor.chain().focus().extendMarkRange("link").unsetLink().run();

      return;
    }

    // update link
    try {
      const href = url.includes(":") ? url : `https://${url}`;
      if (editor.state.selection.empty) {
        editor.commands.insertContent(`<a href="${href}">${url}</a>`);
      } else {
        editor.chain().focus().extendMarkRange("link").setLink({ href }).run();
      }
    } catch (e) {
      alert(e.message);
    }
  };
});
