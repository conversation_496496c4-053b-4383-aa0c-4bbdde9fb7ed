#description_editor {
  margin-top: 1rem;
}

.editor_body {
  margin-top: 1rem;
  border: 1px solid #ccc;
  width: 30rem;
  border-radius: 4px;
}
.editor_menu {
  display: flex;
  gap: 0.5rem;
  border: 1px solid #ccc;
  border-width: 0 0 1px 0;
}
.editor_menu button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  background: none;
}
.editor_menu button > div {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
}
.editor_menu button.active > div {
  background-color: rgba(219, 234, 254, 1);
}
.word_count {
  color: rgba(107, 114, 128, 1);
}
.tiptap {
  padding: 1rem;
}
.tiptap:focus {
  outline: none;
}
.tiptap p {
  margin: 1em 0;
}
.tiptap ul {
  margin: 0 0 0 1em;
}
.tiptap ul li {
  list-style: disc;
}
