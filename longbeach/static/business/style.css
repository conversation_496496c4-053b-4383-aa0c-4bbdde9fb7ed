#business-list-menu { margin: 0 0 20px 0px; min-width: 754px; }
#business-list-menu::after { display: table; content: ""; clear: both; }
#business-list-menu .business-list-menu-item { float: left; }

#business-search-form { width: 280px; margin: 0; position: relative; }
#business-search-form .search-icon { position: absolute; left: 254px; top: 5px; }
#business-search-form .search-icon a.close { margin-top: -3px; margin-left: 3px; }
#business-search-query { width: 244px; padding-left: 10px; padding-right: 24px; }


#business-list-filter .button-overflow { max-width: 149px; }
#business-list-filter .business-list-filter-count { color: #ccc; }
#business-list-filter a:hover .business-list-filter-count, #business-list-filter li.active .business-list-filter-count { color: #46a8da; }

#business-list-count, #business-list-page { margin-top: 5px; white-space: nowrap; }
#business-list-count { width: 120px; text-align: right; }
#business-list-menu.business-list-unfiltered #business-list-pagination { margin-left: 160px; }

#business-list-buttons.business-list-menu-item { float: right; }

#business-list-menu-container {
    background-color: #f5f5f5;
    width: 100%;
    left:0;
    right:0;
    height: 53px;
    margin: -18px 0 0 0 !important;
    border-bottom: 1px solid #e1e1e8;
    padding-top: 1px;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    text-align: center;
    position: fixed;
    z-index: 999;
}

#business-list-menu {
    display: inline-block;
    boz-sizing: border-box;
    -moz-box-sizing: border-box;
    margin-top: 11px;
    margin-bottom: 11px;
    text-align: left;
    height: 29px;
}

#business-list-submenu {
    margin-top: 35px;
    margin-bottom: 5px;
}
#business-list-submenu .nav-pills li:first-child.disabled a {
    padding-left: 8px;
}
#business-list-submenu .dropdown-menu {
    z-index: 998;
}
#business-list-submenu .dropdown-menu .option-count {
    color: #c6c6c6;
    padding-left: 2px;
    display: none; /* Hide counts until we have a better facet
                      implementation for elasticsearch */
}

#business-list-submenu-count {
    float: right;
    margin-top: 10px;
    line-height: 14px;
    color: #999;
}

.business-list-pagination-arrows {
    margin-left: 10px;
}

.dropdown-menu .dropdown-scroll {
    margin: 0;
    border: 0;
    padding: 0;
    max-height: 500px;
    overflow-y: scroll;
}

#sub-filters, #tag-filter-container {
    display: inline-block;
    vertical-align: middle;
    height: 26px;
    margin-bottom: 19px;
    margin-top: 8px;
}
#tag-filter-container {
    float: right;
}
#tag-filter {
    width: 350px;
    margin-top: 2px;
}

#tag-filter > .tags-input {
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    width: 340px;
    height: 24px !important;
    border-color:#ddd;
}

.nav-tag {
    color: #fff;
    background-color: #08c;
    vertical-align: middle;
    padding: 4px 12px;
    margin-top: 2px;
    margin-bottom: 2px;
    font-weight: normal;
    line-height: 14px;
    margin:0;
    font-size: 13px;
    margin: 0 1px;
}
.nav-tag > span {
    line-height: 14px;
    font-size: 13px;
    text-shadow: none;
}
.nav-tag > a {
    margin-left: 1em;
    opacity: 1 !important;
}
.dropdown.active > a{
    background-color: #FFF !important;
    color: #d35136 !important;
}

.active > a > .caret {
border-top-color: #d35136 !important;
border-bottom-color: #d35136 !important;
}

@media (max-width: 1199px) {
    #business-list-menu .business-list-menu-item { margin-left: 20px; }
}

@media (max-width: 979px) {
    #business-list-menu .business-list-menu-item { margin-left: 15px; }
    #business-search-form { width: 200px; }
    #business-search-form .search-icon { left: 174px; }
    #business-search-query { width: 164px; }
    #business-list-count { display: none; }
    #business-list-pagination.business-list-menu-item { margin-left: 55px; }
}

#business-list { white-space: nowrap; }

#business-list .col-numeric { text-align: right; }
#business-list .col-centered { text-align: center; }

.container-overflow { overflow: hidden; -webkit-mask: -webkit-linear-gradient(right, rgba(0,0,0,0) 0px, rgba(0,0,0,1) 16px); }
.button-overflow {
    overflow: hidden; white-space: nowrap; float: left; padding-right: 5px;
    -webkit-mask: -webkit-linear-gradient(right, rgba(0,0,0,0) 0px, rgba(0,0,0,0) 1px, rgba(0,0,0,1) 5px);
}

.pagination {
    margin-bottom: 0;
}
.pagination ul {
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}
.pagination ul>li>a {
    border: 0;
}
.pagination ul>li.active>a {
    background-color: #fff;
    color: #333;
    font-weight: bold;
}

.select2-error .select2-choice,
.select2-error.select2-drop-active {
    -webkit-box-shadow: 0 0 6px #d59392;
    -moz-box-shadow   : 0 0 6px #d59392;
    -o-box-shadow     : 0 0 6px #d59392;
    box-shadow        : 0 0 6px #d59392;
    border-color: #953b39;
    outline: none;
}
.select2-error .select2-choice span {
    color: #953b39;
}
.select2-error .select2-search {
  margin-top: 4px;
}

.dropdown-filtered input {
    width: 100%;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    height: 30px;
    border-left: 0;
    border-right: 0;
    border-top: 0;
    border-radius: 6px;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.dropdown-filtered .dropdown-menu {
    padding-top: 0;
}
.option-active > a {
    color: #FFF !important;
    background-color: #0081c2;
}

    .right {float: right;}
    .novmargin {margin: 0 10px;}
