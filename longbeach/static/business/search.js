/* jshint indent:2, jquery:true, quotmark:false */
$(function() {
  'use strict';
  var $queryNode = $('#business-search-query');

  var getSearchMap = function() {
    var urlSearch = window.location.search.substr(1);
    var map = {},
    attribute,
    i,
    l,
    attributes;
    if (urlSearch !== '') {
      attributes = urlSearch.split('&');
      for (i = 0, l = attributes.length; i < l; i++) {
        attribute = attributes[i].split('=');
        if (attribute[0] !== 'page' && attribute[0] !== 'order_by') {
          map[attribute[0]] = attribute[1];
        }
      }
    }
    return map;
  };

  var getSearchUrl = function(map) {
    var value,
    attribute,
    attributeArray = [];

    var compareAttributes = (function() {
      var getName = function(attr) {
        return attr.substr(0, attr.indexOf('='));
      };
      return function(a, b) {
        return (getName(a) < getName(b)) ? -1 : 1;
      };
    }());

    for (attribute in map) {
      value = map[attribute];
      if (value) {
        attributeArray.push(attribute + '=' + value);
      }
    }

    attributeArray.sort(compareAttributes);
    return '?' + attributeArray.join('&');
  };

  var updateUrl = function(attribute, value) {
    var map = getSearchMap();
    if (value === 'ALL') {
      value = null;
    }
    map[attribute] = value;
    if (attribute !== 'q') {
      map.q = $queryNode.val();
    }
    window.location = getSearchUrl(map);
  };

  $('#business-list-submenu').on('click', '.dropdown-menu a', function(evt) {
    evt.preventDefault();
    evt.stopPropagation();
    $(this).dropdown('toggle');
    var path = $(this).attr('href').substr(1);
    var attribute = path.split('=');
    updateUrl(attribute[0], attribute[1]);
  });

  var setupFilteredDropdown = function(selector) {
    var options = [],
        selectedOption,
        prevOption,
        optionsCount;
    var $el = $(selector);
    var $input = $el.find('input');
    var $toggle = $el.find('.dropdown-toggle');

    $el.find("[data-option]").each(function(index) {
      var $this = $(this);
      var option = {
        index: index,
        text: $this.attr('data-option').toLowerCase(),
        $el: $this,
        selected: $this.attr('data-selected'),
        visible: true
      };
      options.push(option);
      if (selectedOption === undefined && option['selected'] === 'selected') {
        selectedOption = option;
      }
    });

    if (selectedOption === undefined) {
      selectedOption = options[0];
    }

    $el.on('hover', '[data-option]', function () {
      if (prevOption) {
        prevOption.$el.removeClass('option-active');
        prevOption = undefined;
      }
    });

    //Cache this because it will never change!
    optionsCount = options.length;

    var setActiveOption = function(option) {
      var $option = option.$el,
          $prevOption;
      if (prevOption !== undefined) {
        $prevOption = prevOption.$el;
      }
      if ($prevOption === undefined || $option.get(0) !== $prevOption.get(0)) {
        $option.addClass("option-active");
        if ($prevOption !== undefined) {
          $prevOption.removeClass("option-active");
        }
        prevOption = option;
      }
    };

    var filterOptions = function(query) {
      var option, i, l;
      query = query.toLowerCase();
      if (prevOption) {
        prevOption.$el.removeClass("option-active");
        prevOption = undefined;
      }
      for (i = 0, l = optionsCount; i < l; i++) {
        option = options[i];
        var match = (option.text.indexOf(query) !== -1);
        option.$el.toggle(match);
        option.visible = match;
      }
    };
    var getNextOption = function (startOption) {
      var startIndex = startOption.index + 1;
      var i, option;
      if (startIndex === optionsCount) {
        startIndex = 0;
      }
      for (i = startIndex; i < optionsCount; i++){
        option = options[i];
        if (option.visible) {
          return option;
        }
      }
      for (i = 0; i < startIndex; i++) {
        option = options[i];
        if (option.visible) {
          return option;
        }
      }
      return null;
    };

    var getPreviousOption = function (startOption) {
      var startIndex = startOption.index - 1;
      var i, option;
      if (startIndex < 0) {
        startIndex = optionsCount - 1;
      }
      for (i = startIndex; i >= 0; i--) {
        option = options[i];
        if (option.visible) {
          return option;
        }
      }
      for (i = optionsCount - 1; i > startIndex; i--) {
        option = options[i];
        if (option.visible) {
          return option;
        }
      }
      return null;
    };


    $toggle.on("click focus", function() {
      if ($input.val()) {
        $input.val('');
        filterOptions('');
      }
      setActiveOption(selectedOption);
      setTimeout(function() {
        $input[0].focus();
      }, 0);

    });

    $input.on('click focus', function(evt) {
      evt.stopPropagation();
      evt.preventDefault();
    });

    $input.on('keydown', function(evt) {
      var _this = this, option;
      if (evt.which === 13) {
        evt.preventDefault();
      }
      setTimeout(function() {
        var value = _this.value;
        switch (evt.which) {
        case 13:
          var href = prevOption.$el.find('a').attr('href');
          updateUrl(href.substring(1, href.indexOf('=')), href.substr(href.indexOf('=') + 1));
          break;
        case 40:
          option = getNextOption(prevOption || options[0]);
          if (option !== null) {
            setActiveOption(option);
          }
          break;
        case 38:
          option = getPreviousOption(prevOption || options[optionsCount - 1]);
          if (option !== null) {
            setActiveOption(option);
          }
          break;
        default:
          filterOptions(value);
          if (value.length === 0) {
            setActiveOption(selectedOption);
          } else {
            option = getNextOption(options[0]);
            if (option !== null) {
              setActiveOption(option);
            }
          }
          break;
        }
      }, 0);
    });
  };
  setupFilteredDropdown("#tags-control");
  setupFilteredDropdown("#category-control");
  setupFilteredDropdown("#site-control");
});
