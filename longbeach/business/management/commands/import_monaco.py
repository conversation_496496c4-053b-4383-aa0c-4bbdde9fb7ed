import urllib.error
import urllib.parse
import urllib.request

from django.conf import settings
from django.core.files import File
from django.core.files.temp import NamedTemporaryFile
from django.core.management.base import BaseCommand
from django.db import connections

from longbeach.business.models import Business, BusinessCategory, Image


def dictfetchall(cursor):
    """Returns all rows from a cursor as a dict"""
    desc = cursor.description
    return [
        dict(list(zip([col[0] for col in desc], row)))
        for row in cursor.fetchall()
    ]


IMAGE_HOST = "http://pitcrews-monaco-prod.s3.amazonaws.com/"
UPLOAD = False
USER_ID = 1
ORG_ID = 1


images_done = 0


def save_image_field(obj, field_name, url):
    global images_done
    images_done += 1
    if UPLOAD:
        print("Migrating image %s for %s / %s" % (images_done, obj, url))
        img_temp = NamedTemporaryFile(delete=True)
        try:
            url = urllib.parse.quote(url)
            img_temp.write(urllib.request.urlopen(IMAGE_HOST + url).read())
            img_temp.seek(0)
            getattr(obj, field_name).save(url.split("/")[-1], File(img_temp))
        except Exception:
            import pdb

            pdb.set_trace()
            print("Image failed for %s / %s" % (obj, url))
            return False

    else:
        setattr(obj, field_name, url)
        obj.save()
    return True


class Command(BaseCommand):
    def handle(self, **options):
        # raw_input("Deleting all businesses - hit enter to continue")
        # Business.objects.all().delete()

        settings.DATABASES["monaco"] = {
            # Ends with "postgresql_psycopg2", "mysql", "sqlite3" or "oracle".
            "ENGINE": "django.db.backends.postgresql",
            # DB name or path to database file if using sqlite3.
            "NAME": "monaco",
            # Not used with sqlite3.
            "USER": "postgres",
            # Not used with sqlite3.
            "PASSWORD": "postgres",
            # Set to empty string for localhost. Not used with sqlite3.
            "HOST": "localhost",
            # Set to empty string for default. Not used with sqlite3.
            "PORT": "",
        }

        cursor = connections["monaco"].cursor()
        cursor.execute(
            "SELECT * FROM business_localbusiness "
            "JOIN business_localbusinessgeocode ON "
            "business_localbusinessgeocode.local_business_id = "
            "business_localbusiness.id"
        )
        for row in dictfetchall(cursor):
            # We only want to import monaco business w/ descriptions.
            if not row["description"].strip():
                continue
            initial = {"name": row["name"]}
            try:
                business = Business.objects.get(**initial)
            except Business.DoesNotExist:
                initial.update(
                    {
                        "created_by_id": USER_ID,
                        "updated_by_id": USER_ID,
                        "organization_id": ORG_ID,
                    }
                )
                business = Business(**initial)
            business.url = row["url"]
            business.description = row["description"]
            business.business_hours = row["opening_hours"]
            business.address = row["lookup_address"] or ""
            business.latitude = row["latitude"]
            business.longitude = row["longitude"]
            business.is_active = True
            business.telephone = row["telephone"]
            business.mobile = row["mobile"]
            business.fax = row["fax_number"]
            business.email = row["email"]
            business.monaco_id = row["id"]

            if row["image"] and not business.logo:
                save_image_field(business, "logo", row["image"])

            business.save()

            # Images
            cursor.execute(
                "SELECT * FROM business_localbusinessimage "
                "WHERE business_id = %s" % row["id"]
            )
            images = dictfetchall(cursor)
            for image in images:
                if image["image"]:
                    img = Image(
                        business=business,
                        caption=image["caption"],
                        order=image["ordering"],
                    )
                    if save_image_field(img, "image", image["image"]):
                        img.save()

            # Categories
            cursor.execute(
                "SELECT * FROM business_category "
                "JOIN business_localbusiness_categories ON "
                "business_category.id = category_id AND "
                "localbusiness_id = %s" % row["id"]
            )
            cats = dictfetchall(cursor)
            roots = set([cat["slug"].split("/")[0] for cat in cats])
            if len(roots) > 1:
                print()
                print(
                    "Multiple cats found for %s - %s"
                    % (business.name, business.address)
                )
                for cat in cats:
                    print(cat["slug"])
            elif roots:
                slug = cats[0]["slug"].split("/")[0]
                cursor.execute(
                    "SELECT name FROM business_category "
                    "WHERE slug = '%s'" % slug
                )
                name = cursor.fetchone()[0]
                category, _ = BusinessCategory.objects.get_or_create(name=name)
                business.category = category
                business.save()
