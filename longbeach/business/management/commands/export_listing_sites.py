import csv

from django.core.management.base import BaseCommand
from django.utils.encoding import smart_str
from django.utils.timezone import now as datetime_now
from haystack.query import SearchQuerySet
from haystack.utils.geo import D, Point

from longbeach.business.manage.utils import get_sites
from longbeach.business.models import Business


def get_listing_details_for_all_business_listings():
    """
    Build a list of Business search querysets for all of the mastheads.

    NOTE: Defining this function here rather than business.manage.utils due to
          it becoming irrelevant outside of this command after AWC-152.
    """
    mastheads = get_sites()
    sqs = SearchQuerySet().models(Business).load_all()
    all_listing_details = {}
    for masthead in list(mastheads.values()):
        point = Point(masthead["longitude"], masthead["latitude"])
        dist = D(km=masthead["radius"])
        sqs = sqs.dwithin("point", point, dist).distance("point", point)
        if sqs:
            for business in sqs:
                if business:
                    business_pk = int(business.pk)
                    # In case there are mastheads with duplicate names, get the ID
                    masthead_id_and_name = "(id: {}) {}".format(
                        masthead["id"], masthead["name"]
                    )
                    # Since the original SQS wasn't returning all of the data we just define here
                    listing_details = all_listing_details.get(int(business.pk))
                    if listing_details is None:
                        listing_details = {
                            "id": business_pk,
                            "name": business.name,
                            "account_type": business.account_type,
                            "mastheads": [masthead_id_and_name],
                            "has_website_content_ad": business._object.has_website_content_ad,
                            "masthead_count": 0,
                            "listing_client_id": business.listing_client_id,
                            "email": business._object.email,
                            "address": business.address,
                            "telephone": business._object.telephone,
                            "telephone_two": business._object.telephone_two,
                        }
                        all_listing_details[business_pk] = listing_details
                    elif (
                        masthead_id_and_name
                        not in listing_details["mastheads"]
                    ):
                        listing_details["mastheads"].append(
                            masthead_id_and_name
                        )
    return all_listing_details


class Command(BaseCommand):
    """
    Generate a report for the lat/long-based masthead associations.

    This will be used to determine the next course of action for AWC-152 to
    replace the lat/long associations with explicitly defined mastheads.

    ./manage.py export_listing_sites
    """

    def handle(self, *args, **options):
        columns = [
            "Business Name",
            "Account Type",
            "Mastheads (by location)",
            "Has Web Content ad?",
            "No. of mastheads (by location)",
            "Business Number",
            "Salesforce Opportunity ID",
            "email",
            "address",
            "Telephone",
            "Telephone two",
        ]
        rows = []
        all_business_listings = get_listing_details_for_all_business_listings()
        for business_id, listing_details in all_business_listings.items():
            row = [
                listing_details["name"],
                listing_details["account_type"],
                ", ".join(listing_details["mastheads"]),
                listing_details["has_website_content_ad"],
                len(listing_details["mastheads"]),
                business_id,
                listing_details["listing_client_id"],
                listing_details["email"],
                listing_details["address"],
                listing_details["telephone"],
                listing_details["telephone_two"],
            ]
            rows.append(row)
        now = datetime_now()
        filename = "listing-sites-{}.csv".format(
            now.strftime("%d-%m-%Y-%H-%M-%S"),
        )
        with open(filename, "wb") as f:
            report_writer = csv.writer(f)
            report_writer.writerow(columns)
            for row in rows:
                clean_row = [
                    smart_str(i, encoding="ascii", errors="ignore")
                    for i in row
                ]
                report_writer.writerow(clean_row)
