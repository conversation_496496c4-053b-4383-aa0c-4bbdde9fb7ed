# Everyday check and delete all sponsored jobs from our database and
# Keep the jobs received from Adzuna XML feed.
# Thanks to @<PERSON> from Adzuna for the nice & friendly API
# https://fairfaxmedia.atlassian.net/browse/AWC-196
# https://fairfaxregional.atlassian.net/wiki/spaces/newsnow/pages/156336152/Classifieds+-+Jobs+Page
# Url	https://feeds.adzuna.co.uk/acmregional/jobs_AU_8417.xml
# Url compressed https://feeds.adzuna.co.uk/acmregional/jobs_AU_8417.xml.gz


from xml.etree.ElementTree import ElementTree, fromstring

import requests
from django.core.management import BaseCommand

from longbeach.localads.models import SponsoredJob


class Command(BaseCommand):
    def handle(self, *args, **options):
        url = "https://feeds.adzuna.co.uk/acmregional/jobs_AU_8417.xml"
        r = requests.get(
            url, stream=True
        )  # stream=True is necessary for larger response.
        if r.status_code == 200:
            jobs_xml = r.content
            tree = ElementTree(fromstring(jobs_xml))
            root = tree.getroot()
            if (
                len(root) >= 2
            ):  # We need at least two jobs, otherwise not a good idea to wipe out existing jobs.
                SponsoredJob.objects.all().delete()

            for child in root:
                location = child.find("location")
                if location:
                    location = location.find("location").text
                kwargs = {
                    "title": child.find("title").text,
                    "description": child.find("description").text,
                    "url": child.find("url").text,
                    "image": child.find("smallimage").text,
                    "location": location,
                }
                SponsoredJob.objects.create(**kwargs)
            print("SUCCESS")
        else:
            print("ERROR: Adzuna feed isn't working. Check the url")
