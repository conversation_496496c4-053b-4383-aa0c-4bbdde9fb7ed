# Everyday check for impact accounts whose subscription ends and make their account type as listing account
# For ex:
# If today is Jan-31-2017 then all impact accounts that are
# - Classified and listing_start less than 2016-11-01 (13 weeks)
# - Forward Rev and listing_start less than 2016-01-31 (12 months)
# will be queried and put back to 'listing'
#       - Subscription account don't have end dates, https://fairfaxmedia.atlassian.net/browse/AWC-216

import traceback

import pendulum
from django.core.management import BaseCommand
from django.db.models import Q

from longbeach.business.models import Business


class Command(BaseCommand):
    def handle(self, *args, **options):
        today = pendulum.today()
        classified_start = today.subtract(weeks=13)
        forward_rev_start = today.subtract(months=12)

        # account_type=2 means impact, account_type=1 means listing
        q1 = Q(
            impact_subtype="Classified",
            listing_start__lt=classified_start.date(),
        )
        q2 = Q(
            impact_subtype="Forward Rev",
            listing_start__lt=forward_rev_start.date(),
        )

        try:
            Business.objects.filter(account_type=2).filter(q1 | q2).update(
                account_type=1
            )
        except Exception:
            print(traceback.format_exc())
