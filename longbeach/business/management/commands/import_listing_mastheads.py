import csv
import os

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction

from longbeach.business.models import Business, BusinessMasthead


class Command(BaseCommand):
    """
    ./manage.py import_listing_mastheads [env (staging, production)] [run_type (live, test, verify)]

    Import the listing mastheads for the new listing mastheads field to replace lat/long.
    """

    def add_arguments(self, parser):
        parser.add_argument("env", type=str)
        parser.add_argument("run_type", type=str)

    @transaction.atomic
    def handle(self, *args, **options):
        env = options["env"]
        run_type = options["run_type"]
        filename = f"{env}_suzuka_masthead_business_export.csv"
        filepath = os.path.join("includes", "import_data", filename)
        try:
            infile = open(filepath)
        except OSError:
            raise CommandError(
                "Invalid environemnt specified, available options: [staging, production]"
            )
        if run_type == "verify":
            VERIFY = True
            TESTING = False
        else:
            VERIFY = False
            try:
                TESTING = {"live": False, "test": True}[run_type]
            except KeyError:
                raise CommandError(
                    "Specify if this is a live or test run, available options: [live, test]"
                )
        reader = csv.DictReader(infile)
        rows = [row for row in reader]
        for row in rows:
            business_id = int(row["Business Id"])
            business_name = row["Business Name"]
            try:
                business = Business.objects.get(id=business_id)
            except Business.DoesNotExist:
                raise CommandError(
                    f"The business id={business_id}, name={business_name} does not exist..."
                )
            if not TESTING and not VERIFY:
                business.listing_mastheads.clear()
            sites = row["Sites"].split(",")
            for site in sites:
                _site = site.split(":")
                site_id = int(_site[0].replace("#", "").strip())
                site_name = _site[1]
                try:
                    business_masthead = BusinessMasthead.objects.get(
                        site_id=site_id
                    )
                except BusinessMasthead.DoesNotExist:
                    if VERIFY:
                        raise CommandError(
                            f"The BusinessMasthead for Site id={site_id}, name={site_name} did not exist, "
                            "if you ran the live command already, this may be an issue..."
                        )
                    if not TESTING:
                        # Test mode is just for debugging what live run will look like without any DB changes
                        business_masthead = BusinessMasthead.objects.create(
                            site_id=site_id
                        )
                if VERIFY:
                    business_mastheads = business.listing_mastheads.all()
                    if business_masthead not in business_mastheads:
                        raise CommandError(
                            f"The Site id={site_id}, name={site_name} does not exist in the mastheads site_id list "
                            f"for Business id={business_id}, name={business_name}, "
                            "if you ran the live command already, this may be an issue..."
                        )
                elif not TESTING:
                    business.listing_mastheads.add(business_masthead)
                    business.save()
                print(
                    f"Site id={site_id}, name={site_name} assigned to Business id={business_id}, name={business_name}"
                )
        if TESTING:
            print("Test successful.")
        elif VERIFY:
            print("Verification successful.")
        else:
            print("Live run successful.")
