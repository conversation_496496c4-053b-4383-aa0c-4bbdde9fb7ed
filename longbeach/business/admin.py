from django.contrib import admin
from shared_orgs_client.utils import OrgsAdminMixin

from longbeach.business.forms import BusinessForm
from longbeach.business.models import (
    Business,
    BusinessCategory,
    BusinessFeatureAd,
    BusinessMasthead,
    Image,
)


class ImageInLine(admin.TabularInline):
    model = Image


class BusinessAdmin(OrgsAdminMixin, admin.ModelAdmin):
    list_display = ("id", "name", "address", "category")
    list_editable = ("name", "address")
    list_filter = ("category",)
    inlines = (ImageInLine,)
    search_fields = ("name", "category__name", "description", "address")
    fieldsets = (
        (
            None,
            {
                "fields": (
                    "name",
                    "description",
                    "logo",
                    "hero",
                    "account_type",
                    "impact_subtype",
                )
            },
        ),
        (
            "Meta data",
            {
                "fields": (
                    "is_active",
                    ("listing_start", "listing_end"),
                    "listing_client_id",
                    "organization",
                    "category",
                    "tags",
                    "listing_mastheads",
                )
            },
        ),
        (
            "Details",
            {
                "fields": (
                    "address",
                    "telephone",
                    "mobile",
                    "fax",
                    "email",
                    "url",
                    "business_hours",
                )
            },
        ),
    )
    form = BusinessForm

    def get_queryset(self, request):
        """Optimize the query."""
        queryset = super().get_queryset(request)

        return queryset.select_related("category")

    def save_form(self, request, form, change):
        obj = form.save(commit=False)
        if obj.id is None:
            obj.created_by = request.user
        obj.updated_by = request.user
        return super().save_form(request, form, change)


class BusinessCategoryAdmin(admin.ModelAdmin):
    pass


class BusinessMastheadAdmin(admin.ModelAdmin):
    pass


class BusinessFeatureAdAdmin(admin.ModelAdmin):
    list_display = ("ad_title", "created_on")
    readonly_fields = ("created_by", "created_on", "updated_by", "updated_on")


admin.site.register(Business, BusinessAdmin)
admin.site.register(BusinessCategory, BusinessCategoryAdmin)
admin.site.register(BusinessMasthead, BusinessMastheadAdmin)
admin.site.register(BusinessFeatureAd, BusinessFeatureAdAdmin)
