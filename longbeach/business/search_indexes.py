from haystack import fields, indexes

from longbeach.business.models import Business, BusinessFeatureAd


class BusinessIndex(indexes.SearchIndex, indexes.Indexable):
    name = indexes.CharField(model_attr="name")
    name_sort = indexes.FacetCharField(stored=False)
    text = indexes.Char<PERSON>ield(
        model_attr="description", document=True, stored=False
    )

    is_active = indexes.BooleanField(model_attr="is_active")
    listing_start = fields.FacetDateField(
        model_attr="listing_start", null=True
    )
    listing_end = fields.FacetDateField(model_attr="listing_end", null=True)
    listing_client_id = indexes.FacetCharField(null=True)
    category = indexes.FacetIntegerField(model_attr="category_id", null=True)
    tags = fields.FacetMultiValueField()
    tags_text = fields.MultiValueField(stored=False)

    address = indexes.CharField(model_attr="address")
    point = indexes.LocationField(null=True)

    organization = indexes.FacetIntegerField(model_attr="organization")

    created_by = indexes.FacetIntegerField(model_attr="created_by_id")
    created_on = indexes.FacetDateTimeField(model_attr="created_on")
    updated_by = indexes.FacetIntegerField(model_attr="updated_by_id")
    updated_on = indexes.FacetDateTimeField(model_attr="updated_on")

    mastheads = fields.FacetMultiValueField(model_attr="mastheads")

    account_type = indexes.FacetIntegerField(model_attr="account_type")
    has_business_feature_ad = indexes.FacetIntegerField(null=True)
    has_website_content_ad = indexes.FacetIntegerField(null=True)

    listing_masthead_site_ids = indexes.MultiValueField()

    def get_model(self):
        return Business

    def prepare_listing_client_id(self, obj):
        if obj.listing_client_id.strip():
            return obj.listing_client_id.strip()

    def prepare_listing_masthead_site_ids(self, obj):
        return [site.site_id for site in obj.listing_mastheads.all()]

    def prepare_name_sort(self, obj):
        """
        Prepare `name` field for sorting.
        """
        name = obj.name.lower().strip()  # case-insensitive
        if len(name) >= 1:
            if name[:1].isdigit():
                name = "{%s" % name  # digits after letters
            elif not name[:1].isalpha():
                name = "}%s" % name  # symbols after digits
        return name

    def prepare_point(self, obj):
        if obj.latitude is None or obj.longitude is None:
            return None
        return "%s,%s" % (obj.latitude, obj.longitude)

    def prepare_tags(self, obj):
        return [t.name for t in obj.tags.all()]

    def prepare_tags_text(self, obj):
        return self.prepare_tags(obj)

    def prepare_has_business_feature_ad(self, obj):
        if obj.has_business_feature_ads:
            return 1
        return 0

    def prepare_has_website_content_ad(self, obj):
        if obj.mastheads:
            return 1
        return 0


class BusinessFeatureAdIndex(indexes.ModelSearchIndex, indexes.Indexable):
    class Meta:
        model = BusinessFeatureAd
        fields = ["tag", "ad_title", "ad_copy", "expiration_date"]
