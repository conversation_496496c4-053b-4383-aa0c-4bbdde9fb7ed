import datetime

from autoslug import AutoSlugField
from django.contrib.auth.models import User
from django.contrib.postgres.fields import ArrayField
from django.db import models
from django.utils.text import slugify
from django.utils.timezone import now as datetime_now
from django.utils.translation import gettext_lazy as _
from geocode.models import GeocodeBase
from shared_orgs_client.utils import OrgsMixin
from taggit.managers import TaggableManager
from valencia_storage import ValenciaStorage

from longbeach.business.manage.utils import get_sites
from longbeach.business.managers import (
    BusinessFeatureAdManager,
    BusinessOrgsManager,
    BusinessQuerySet,
)

valencia_fs = ValenciaStorage()


class BusinessCategory(models.Model):  # type: ignore[django-manager-missing]
    name = models.CharField(max_length=1000, db_index=True)
    slug = AutoSlugField(
        max_length=250, editable=False, null=True, populate_from="name"
    )
    image = models.ImageField(
        storage=valencia_fs, upload_to="images", null=True
    )

    class Meta:
        verbose_name = _("Category")
        verbose_name_plural = _("Categories")
        ordering = ["name"]

    def __str__(self):
        return self.name


class ESOVTracker(models.Model):
    business = models.ForeignKey("Business", on_delete=models.deletion.CASCADE)
    site_id = models.IntegerField()
    last_displayed = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = (("business", "site_id"),)


class BusinessMasthead(models.Model):  # type: ignore[django-manager-missing]
    """
    An object for associating Suzuka mastheads with business products.
    """

    site_id = models.IntegerField(unique=True, db_index=True)

    def __str__(self):
        return f"Business Masthead {self.site_id}"

    @property
    def site_name(self):
        site = get_sites().get(self.site_id)
        return site["name"] if site else None


class Business(OrgsMixin, GeocodeBase):
    """
    A Business listing profile for business products.

    This object serves a dual-role of existing both as a Business Listing and
    a Business "profile" for managing associated products (such as Business Feature Ads).

    The two business products are:
        - Website Content Ad of account type Impact or Premium (fields on model)
        - Business Feature Ad (ForeignKey relationship)
    """

    ACCOUNT_INACTIVE = 0
    ACCOUNT_LISTING = 1
    ACCOUNT_IMPACT = 2
    ACCOUNT_PREMIUM = 3
    ACCOUNT_UNAPPROVED = 4
    ACCOUNT_TYPE_CHOICES = (
        (ACCOUNT_INACTIVE, "Inactive"),
        (ACCOUNT_LISTING, "Listing"),
        (ACCOUNT_IMPACT, "Impact"),
        (ACCOUNT_PREMIUM, "Premium"),
        (ACCOUNT_UNAPPROVED, "Unapproved"),
    )

    IMPACT_SUBTYPES = (
        ("Subscription", "Subscription"),
        ("Classified", "Classified"),
        ("Forward Rev", "Forward Rev"),
    )

    name = models.CharField(max_length=1000)
    is_active = models.BooleanField(default=True)
    account_type = models.IntegerField(
        default=ACCOUNT_LISTING, db_index=True, choices=ACCOUNT_TYPE_CHOICES
    )
    impact_subtype = models.CharField(
        max_length=20, default="Subscription", choices=IMPACT_SUBTYPES
    )
    description = models.TextField(blank=True)
    logo = models.ImageField(
        storage=valencia_fs, upload_to="images", null=True, blank=True
    )
    hero = models.ImageField(
        storage=valencia_fs, upload_to="images", null=True, blank=True
    )
    listing_start = models.DateField(null=True)
    listing_end = models.DateField(blank=True, null=True)
    listing_client_id = models.CharField(
        verbose_name="Salesforce Opportunity ID", max_length=100, blank=True
    )
    video_montage = models.CharField(
        verbose_name="Youtube shared link", max_length=100, blank=True
    )
    category = models.ForeignKey(
        BusinessCategory,
        null=True,
        related_name="businesses",
        on_delete=models.deletion.CASCADE,
    )
    tags = TaggableManager(blank=True)
    address = models.CharField(
        max_length=1000,
        verbose_name="Location Details",
        help_text="Help users locate your ad by entering your full address",
    )
    telephone = models.CharField(
        max_length=500, blank=True, verbose_name="Telephone no"
    )
    telephone_two = models.CharField(
        max_length=500, blank=True, verbose_name="Telephone no. 2"
    )
    mobile = models.CharField(max_length=500, blank=True)
    fax = models.CharField(max_length=500, blank=True)
    email = models.CharField(max_length=2000, blank=True)
    url = models.URLField(blank=True)
    additional_hours = models.CharField(
        max_length=500,
        blank=True,
        help_text="Tell your customers if you are available outside normal business hours",
    )
    business_hours = models.TextField(max_length=1000, blank=True)
    created_by = models.ForeignKey(
        User,
        related_name="business",
        editable=False,
        on_delete=models.deletion.CASCADE,
    )
    created_on = models.DateTimeField(auto_now_add=True)
    updated_by = models.ForeignKey(
        User,
        related_name="business_updated",
        editable=False,
        on_delete=models.deletion.CASCADE,
    )
    updated_on = models.DateTimeField(auto_now=True)
    monaco_id = models.IntegerField(editable=False, null=True)
    meta_title = models.CharField(blank=True, max_length=200)
    meta_description = models.TextField(blank=True)
    facebook = models.CharField(max_length=500, blank=True)
    google_plus = models.CharField(max_length=500, blank=True)
    twitter = models.CharField(max_length=500, blank=True)
    youtube = models.CharField(max_length=500, blank=True)
    instagram = models.CharField(max_length=500, blank=True)
    linkedin = models.CharField(max_length=500, blank=True)
    listing_mastheads = models.ManyToManyField(
        BusinessMasthead, related_name="business_listings"
    )
    contact_name = models.CharField(max_length=100, blank=True, default="")
    welcome_email_sent = models.BooleanField(default=False)

    # Website Content Ad product details

    mastheads = ArrayField(
        models.IntegerField(),
        db_index=False,
        default=list,
        null=True,
        blank=True,
    )
    ad_title = models.CharField(
        max_length=24,
        verbose_name="Title",
        help_text="Max Characters allowed is 24.",
        blank=True,
    )
    ad_copy = models.TextField(
        verbose_name="Body Copy",
        max_length=62,
        help_text="Max Characters allowed is 62.",
        blank=True,
    )
    ad_image = models.ImageField(
        verbose_name="Image",
        storage=valencia_fs,
        upload_to="ad_images",
        blank=True,
    )
    ad_contact_me = models.BooleanField(
        default=False,
        verbose_name="Contact Me",
        help_text="On mobile layouts a call button is made visible.",
    )
    ad_last_accessed = models.DateTimeField(
        default=datetime_now, editable=False
    )

    objects = BusinessQuerySet.as_manager()
    orgs_manager = BusinessOrgsManager()  # type: ignore[var-annotated]

    class Meta:
        verbose_name = _("Business")
        verbose_name_plural = _("Businesses")
        ordering = ["name"]

    def __str__(self):
        return self.name

    def get_lookup_address(self):
        return self.address

    @property
    def is_expired(self):
        return self.listing_end and (self.listing_end < datetime.date.today())

    @property
    def is_pending(self):
        return self.listing_start and (
            self.listing_start > datetime.date.today()
        )

    @property
    def has_business_feature_ads(self):
        return self.business_feature_ads.exists()

    @property
    def business_feature_ad(self):
        return self.business_feature_ads.first()

    @property
    def has_website_content_ad(self):
        return bool(self.mastheads)

    def esov_response(self):
        return {
            "account_type": self.account_type,
            "category_slug": self.category.slug,
            "ad_image": self.ad_image.url if self.ad_image else None,
            "telephone": self.telephone,
            "ad_title": self.ad_title,
            "ad_copy": self.ad_copy,
            "slug": slugify(self.name),
            "category_name": self.category.name,
            "name": self.name,
            "id": self.id,
            "ad_contact_me": self.ad_contact_me,
            "profile_url": "/{}/{}/{}/{}/".format(
                "business", self.category.slug, slugify(self.name), self.id
            ),
            "business_url": self.url,
        }


class Image(models.Model):
    image = models.ImageField(storage=valencia_fs, upload_to="images")
    business = models.ForeignKey(
        Business, related_name="images", on_delete=models.deletion.CASCADE
    )
    order = models.IntegerField(default=0)
    caption = models.CharField(max_length=200, default="")

    def __str__(self):
        return self.image.name


WEEKDAYS = [
    (1, _("Monday")),
    (2, _("Tuesday")),
    (3, _("Wednesday")),
    (4, _("Thursday")),
    (5, _("Friday")),
    (6, _("Saturday")),
    (7, _("Sunday")),
]


class OpeningHours(models.Model):
    business = models.ForeignKey(
        Business,
        related_name="opening_hours",
        on_delete=models.deletion.CASCADE,
    )
    weekday = models.IntegerField(choices=WEEKDAYS, default=1)
    from_hour = models.CharField(max_length=20)
    to_hour = models.CharField(max_length=20)

    class Meta:
        unique_together = (("business", "weekday"),)


AD_TITLE_MAX_LEN = 24
AD_COPY_MAX_LEN = 62
AD_URL_MAX_LEN = 256


class BusinessFeatureAd(models.Model):
    """
    An ad to be displayed on a business feature page.

    Placement determined by mastheads and tag defintions.
    """

    created_by = models.ForeignKey(
        User,
        related_name="business_feature_ad",
        editable=False,
        on_delete=models.deletion.CASCADE,
    )
    created_on = models.DateTimeField(auto_now_add=True)
    updated_by = models.ForeignKey(
        User,
        related_name="business_feature_ad_updated",
        editable=False,
        on_delete=models.deletion.CASCADE,
    )
    updated_on = models.DateTimeField(auto_now=True)

    mastheads = models.ManyToManyField(
        BusinessMasthead, related_name="business_feature_ads"
    )
    business = models.ForeignKey(
        Business,
        related_name="business_feature_ads",
        on_delete=models.deletion.CASCADE,
    )
    tag = models.CharField(max_length=255)
    ad_title = models.CharField(
        max_length=AD_TITLE_MAX_LEN,
        verbose_name="Title",
        help_text="Max Characters allowed is 24.",
        blank=True,
    )
    ad_copy = models.TextField(verbose_name="Body Copy", blank=True)
    ad_url = models.URLField(
        verbose_name="URL",
        max_length=AD_URL_MAX_LEN,
        help_text="ad URL that the ad image links to",
        blank=True,
    )
    ad_image = models.ImageField(
        verbose_name="Image",
        storage=valencia_fs,
        upload_to="ad_images",
        blank=True,
    )
    ad_contact_me = models.BooleanField(
        default=False,
        verbose_name="Contact Me",
        help_text="On mobile layouts a call button is made visible.",
    )
    publication_date = models.DateField(blank=True, null=True)
    expiration_date = models.DateField(blank=True, null=True)
    last_accessed = models.DateTimeField(auto_now=True, null=True)

    objects = BusinessFeatureAdManager()

    def __str__(self):
        return f"Business Feature for {self.business}"

    def save(self, *args, **kwargs):
        # Hack: classifieds import from feed always have this business name and do not want to alter expiration date
        if self.business.name != "ACM Classifieds":
            self.expiration_date = self.publication_date + datetime.timedelta(
                days=90
            )
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        """Delete the image."""
        if self.ad_image:
            self.ad_image.delete()

        return super().delete(*args, **kwargs)
