from django import template

from longbeach.business.models import Business

register = template.Library()


@register.filter
def form_field_lookup(form, name):
    for field in form:
        if field.name == name:
            return field
    return None


@register.filter
def account_type_name(value):
    for id, name in Business.ACCOUNT_TYPE_CHOICES:
        if id == value:
            return name
    return value


@register.simple_tag
def get_site_url(field, form=None, sites=""):
    # Gets the site url from the forms widget field
    # specified. NOTE: Only returns the first site. The widget field must be that
    # of ArrayFieldSiteSelectMultiple.
    field_choices = form.Meta.widgets.get(field)
    if form and field_choices:
        try:
            sites_map = dict(field_choices.sites_domain())
        except ValueError:
            return ""
        try:
            sites_list = [int(n) for n in sites.split(",")]
        except ValueError:
            return ""
        if len(sites_list) and (sites_list[0] in list(sites_map.keys())):
            return sites_map.get(sites_list[0], "")
    return ""


@register.simple_tag
def get_masthead_names_for_feature_ad(feature_ad):
    mastheads = feature_ad.mastheads.all()
    if not mastheads:
        return ""
    masthead_names = [masthead.site_name for masthead in mastheads]
    if len(masthead_names) > 1:
        masthead_names = ", ".join(masthead_names)
    else:
        masthead_names = masthead_names[0]
    return masthead_names
