import decimal

from django import template
from django.contrib.gis.measure import Distance
from django.template import (
    Node,
    TemplateSyntaxError,
    Variable,
    VariableDoesNotExist,
)
from django.template.base import kwarg_re
from django.template.defaultfilters import floatformat

register = template.Library()


@register.filter
def distanceformat(value):
    """
    Return the distance value as a human-readable string.

    """
    if not isinstance(value, Distance):
        return value

    metres = int(round(value.m))
    if metres < 1000:
        return "%s m" % metres

    if round(value.km, 2) < 10:
        kilometres = floatformat(round(value.km, 2), -2)
    elif round(value.km, 1) < 100:
        kilometres = floatformat(round(value.km, 1), -1)
    else:
        kilometres = floatformat(round(value.km, 0), 0)
    return "%s km" % kilometres


def _page_url(url, get_args, kwarg_list=[], **kwargs):
    get_args = get_args.copy()

    for key, value, prefix in kwarg_list:
        if prefix == "append":
            get_args.appendlist(key, value)
        elif prefix == "delete":
            if key in get_args:
                if value is not None:
                    values = get_args.getlist(key)
                    while values:
                        try:
                            values.remove(value)
                        except ValueError:
                            break
                    if values:
                        get_args.setlist(key, values)
                    else:
                        del get_args[key]
                else:
                    del get_args[key]
        else:
            get_args[key] = value

    for key, value in list(kwargs.items()):
        if value is None:
            if key in get_args:
                del get_args[key]
        else:
            get_args[key] = value

    if len(get_args):
        url = "%s?%s" % (url, get_args.urlencode())

    return url


class PageURLNode(Node):
    def __init__(self, kwarg_list, url=None, context_var=None):
        self.kwarg_list = kwarg_list
        self.url = url
        self.context_var = context_var

    def render(self, context):
        request = context["request"]
        page_url = self.url or request.path

        kwarg_list = []
        for key, value, prefix in self.kwarg_list:
            if value is not None:
                value = str(value.resolve(context))
            kwarg_list.append((key, value, prefix))

        page_url = _page_url(page_url, request.GET, kwarg_list)

        if self.context_var:
            context[self.context_var] = page_url
            return ""
        return page_url


def page_url(parser, token):
    bits = token.split_contents()
    tag = bits[0]

    url = None
    context_var = None
    kwarg_list = []
    keys = []

    remaining_bits = bits[1:]
    while remaining_bits:
        bit = remaining_bits.pop(0)

        if bit == "for":
            if kwarg_list:
                raise TemplateSyntaxError(
                    '"%r" must be the first argument to %r' % (bit, tag)
                )
            if url:
                raise TemplateSyntaxError("%r accepts only one url" % tag)
            try:
                url = parser.compile_filter(remaining_bits.pop(0))
            except IndexError:
                raise TemplateSyntaxError(
                    '%r expects a url argument after "%r"' % (tag, bit)
                )
            continue

        if bit == "as":
            try:
                context_var = remaining_bits.pop(0)
            except IndexError:
                raise TemplateSyntaxError(
                    "%r expects a context_variable "
                    'argument after "%r"' % (tag, bit)
                )
            if remaining_bits:
                raise TemplateSyntaxError(
                    '"%r" must be the second-last argument to %r' % (bit, tag)
                )
            continue

        if bit in ("append", "delete"):
            prefix = bit
            try:
                bit = remaining_bits.pop(0)
            except Exception:
                raise TemplateSyntaxError(
                    '%r expects an argument after "%r"' % (tag, prefix)
                )

        else:
            prefix = None

        match = kwarg_re.match(bit)
        if match and match.group(1):
            key, value = match.groups()
            if prefix != "append" and key in keys:
                raise TemplateSyntaxError(
                    'multiple values for "%r" in %r' % (key, tag)
                )
            kwarg_list.append((key, parser.compile_filter(value), prefix))
            keys.append(key)
        elif prefix == "delete":
            kwarg_list.append((bit, None, prefix))
        elif prefix:
            raise TemplateSyntaxError(
                'unknown argument "%r" to %r, expected '
                'keyword argument after "%r"' % (bit, tag, prefix)
            )
        else:
            kwarg_list.append((bit, None, "delete"))

    return PageURLNode(kwarg_list, url, context_var)


page_url = register.tag(page_url)


class PaginatorPageListNode(Node):
    def __init__(
        self,
        context_var,
        paginator=None,
        page_obj=None,
        before=5,
        after=4,
        minimum=10,
        all=False,
        previous_link=None,
        next_link=None,
    ):
        self.paginator = paginator
        self.context_var = context_var
        self.page_obj = page_obj
        self.before = before
        self.after = after
        self.minimum = minimum
        self.all = all
        self.previous_link = previous_link
        self.next_link = next_link

    def render(self, context):
        try:
            request = context["request"]
            paginator = (
                self.paginator
                and self.paginator.resolve(context)
                or context["paginator"]
            )
            page_obj = (
                self.page_obj
                and self.page_obj.resolve(context)
                or context["page_obj"]
            )
        except KeyError:
            # request, paginator, or page object not found in the template
            # context so stop rendering here
            context[self.context_var] = []
            return ""

        if self.all:
            first_page = 1
            last_page = paginator.num_pages
        else:
            minimum = min(self.minimum, paginator.num_pages)
            first_page = max(page_obj.number - self.before, 1)
            last_page = min(page_obj.number + self.after, paginator.num_pages)
            extra = max(0, minimum - (last_page - first_page + 1))
            first_page = max(1, first_page - extra)
            extra = max(0, minimum - (last_page - first_page + 1))
            last_page = min(paginator.num_pages, last_page + extra)

        links = []

        def _add_link(label, page_num):
            links.append(
                {
                    "number": page_num,
                    "label": label,
                    "url": _page_url(
                        request.path,
                        request.GET,
                        page=page_num > 1 and page_num or None,
                    ),
                    "is_current": page_num == page_obj.number,
                }
            )

        if self.previous_link and page_obj.number > 1:
            _add_link(self.previous_link, page_obj.number - 1)
        for num in range(first_page, last_page + 1):
            _add_link(num, num)
        if self.next_link and page_obj.number < paginator.num_pages:
            _add_link(self.next_link, page_obj.number + 1)

        context[self.context_var] = links
        return ""


def paginator_page_list(parser, token):
    bits = token.split_contents()
    tag = bits[0]
    remaining_bits = bits[1:]

    if len(remaining_bits) < 2:
        raise TemplateSyntaxError("%r requires at least two arguments" % tag)

    if remaining_bits.pop(-2) != "as":
        raise TemplateSyntaxError(
            'The second-last argument to %r must be "as"' % tag
        )
    context_var = remaining_bits.pop()

    kwargs = {}
    while remaining_bits:
        bit = remaining_bits.pop(0)

        if bit in (
            "before",
            "after",
            "minimum",
            "all",
            "previous_link",
            "next_link",
        ):
            if bit in kwargs:
                raise TemplateSyntaxError(
                    'multiple values for "%r" in %r' % (bit, tag)
                )

            if bit in ("all",):
                kwargs[bit] = True
            else:
                if not remaining_bits:
                    raise TemplateSyntaxError(
                        '%r expects an argument after "%r"' % (tag, bit)
                    )
                value = remaining_bits.pop(0)
                if bit in ("before", "after", "minimum"):
                    try:
                        value = int(value)
                    except ValueError:
                        raise TemplateSyntaxError(
                            'argument "%r" to %r must '
                            "be an integer" % (bit, tag)
                        )
                elif bit in ("previous_link", "next_link"):
                    try:
                        value = Variable(value).resolve({})
                    except VariableDoesNotExist:
                        value = None
                kwargs[bit] = value

        elif not kwargs:
            kwargs["paginator"] = parser.compile_filter(bit)

        elif "paginator" in kwargs and len(kwargs) == 1:
            kwargs["page_obj"] = parser.compile_filter(bit)

        else:
            raise TemplateSyntaxError(
                'unknown argument "%r" to %r' % (bit, tag)
            )

    if "all" in kwargs and [
        x for x in kwargs if x in ("before", "after", "minimum")
    ]:
        raise TemplateSyntaxError(
            '%r cannot accept "all" if "before", '
            '"after" or "minimum" is specified' % tag
        )

    return PaginatorPageListNode(context_var, **kwargs)


paginator_page_list = register.tag(paginator_page_list)


@register.filter(is_safe=True)
def precision(number, precision):
    """
    Return `number` rounded down to `precision` digits.

    """
    c = decimal.Context(prec=max(1, precision), rounding=decimal.ROUND_DOWN)
    return type(number)(c.create_decimal(number))


@register.tag(name="organization_for_id")
def do_org_for_id(parser, token):
    """
    Obtain the org for the given ID from the given dict of orgs.

    Usage:
        {% org_for_id <org_id> <organizations> [as <context_var>] %}

    HACK: This is a temporary performance workaround pending migration
    of Silverstone to django-shared-orgs-client.

    """
    bits = token.split_contents()
    tag = bits.pop(0)

    if len(bits) not in (2, 4):
        raise TemplateSyntaxError(
            "%s accepts either two or four arguments." % tag
        )

    org_id = parser.compile_filter(bits[0])
    organizations = parser.compile_filter(bits[1])
    context_var = None

    if len(bits) == 4:
        if bits[2] != "as":
            raise TemplateSyntaxError(
                "The second-last argument to %s must be 'as'." % tag
            )
        context_var = bits[3]

    return OrgNode(org_id, organizations, context_var)


class OrgNode(Node):
    """
    Node implementation for `org_for_id` template tag.

    HACK: This is a temporary performance workaround pending migration
    of Silverstone to django-shared-orgs-client.

    """

    def __init__(self, org_id, organizations, context_var=None):
        self.org_id = org_id
        self.organizations = organizations
        self.context_var = context_var

    def render(self, context):
        org_id = self.org_id.resolve(context)
        organizations = self.organizations.resolve(context)

        try:
            org = organizations.get(int(org_id))
        except (TypeError, ValueError):
            org = None

        if self.context_var:
            context[self.context_var] = org
            return ""

        return org or ""
