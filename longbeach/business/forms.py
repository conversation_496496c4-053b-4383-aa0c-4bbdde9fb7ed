import re
from typing import Optional

from django import forms
from django.utils.translation import gettext
from phonenumber_field.formfields import PhoneNumber<PERSON>ield

from longbeach.business.manage.utils import get_sites
from longbeach.business.models import Business, BusinessMasthead
from longbeach.localads.utils import ArrayFieldSiteSelectMultiple


class BusinessForm(forms.ModelForm):
    url = forms.URLField(required=False)
    email = forms.EmailField(required=False)
    telephone = PhoneNumberField(required=False)
    telephone_two = PhoneNumberField(required=False)
    fax = PhoneNumberField(required=False)

    class Meta:
        model = Business
        fields = "__all__"
        widgets = {
            "mastheads": ArrayFieldSiteSelectMultiple(
                attrs={"data-placeholder": "Select Masthead(s)"}
            ),
            "account_type": forms.RadioSelect,
            "organization": forms.HiddenInput(),
            "is_active": forms.HiddenInput(),
            "listing_mastheads": forms.SelectMultiple(
                attrs={"data-placeholder": "Select Masthead(s)"}
            ),
        }
        # Not official or 3rd party django fieldsets but
        # rather a simple way to divvy the fields into groups
        fieldsets: tuple[tuple[Optional[str], dict], ...] = (
            (
                None,
                {
                    "fields": [
                        "name",
                        "account_type",
                        "impact_subtype",
                        "listing_start",
                        "listing_client_id",
                        "video_montage",
                        "category",
                        "description",
                        "logo",
                        "hero",
                        "listing_mastheads",
                    ]
                },
            ),
            (
                "Contact Details",
                {
                    "fields": [
                        "contact_name",
                        "email",
                        "telephone",
                        "telephone_two",
                        "fax",
                        "url",
                        "address",
                    ]
                },
            ),
            ("Opening Hours", {"fields": ["additional_hours"]}),
            ("Photo Gallery", {"fields": []}),
            (
                "Social Presence",
                {
                    "fields": [
                        "facebook",
                        "google_plus",
                        "twitter",
                        "youtube",
                        "instagram",
                        "linkedin",
                    ]
                },
            ),
            (
                "Website Content Ad",
                {
                    "fields": [
                        "mastheads",
                        "ad_title",
                        "ad_copy",
                        "ad_image",
                        "ad_contact_me",
                    ]
                },
            ),
            (
                "Seo Optimisation",
                {"fields": ["meta_title", "meta_description", "tags"]},
            ),
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        sites = get_sites()
        listing_masthead_choices = []
        mastheads = {m.site_id: m for m in BusinessMasthead.objects.all()}
        for k, v in sites.items():
            try:
                business_masthead = mastheads[v["id"]]
            except KeyError:
                business_masthead = BusinessMasthead.objects.create(
                    site_id=v["id"]
                )
            choice = (business_masthead.id, v["name"])
            listing_masthead_choices.append(choice)
        self.fields["listing_mastheads"].choices = listing_masthead_choices
        self.fields["listing_mastheads"].required = False

        try:
            self.fields["ad_contact_me"].initial = True
        except KeyError:
            pass
        if not self.instance.pk:
            for id, name in self.fields.get("organization").choices:
                if name == "National Sales Teams":
                    self.initial["organization"] = id
                    return
        else:
            self.fields[
                "listing_mastheads"
            ].initial = self.instance.listing_mastheads.all().values_list(
                "id", flat=True
            )

    def clean(self):
        cleaned_data = super().clean()
        account_type = cleaned_data.get("account_type")
        mastheads = cleaned_data.get("mastheads")
        ad_title = cleaned_data.get("ad_title")
        ad_copy = cleaned_data.get("ad_copy")
        listing_client_id = cleaned_data.get("listing_client_id")
        logo = cleaned_data.get("logo")
        description = cleaned_data.get("description")
        meta_title = cleaned_data.get("meta_title")
        meta_description = cleaned_data.get("meta_description")
        name = cleaned_data.get("name")
        video_montage = cleaned_data.get("video_montage")

        video_montage_re = re.compile(r"^https://youtu\.be/[A-Za-z0-9_-]{11}$")
        if video_montage and not video_montage_re.search(video_montage):
            self._errors["video_montage"] = self.error_class(
                [
                    gettext(
                        "Please input a valid youtube shared link or just leave it blank."
                    )
                ]
            )

        if account_type in [Business.ACCOUNT_IMPACT, Business.ACCOUNT_PREMIUM]:
            if not listing_client_id:
                self._errors["listing_client_id"] = self.error_class(
                    [gettext("A salesforce opportunity ID is required.")]
                )
            if not logo:
                self._errors["logo"] = self.error_class(
                    [gettext("A logo is required.")]
                )
            if not description:
                self._errors["description"] = self.error_class(
                    [gettext("A description is required.")]
                )
        if mastheads and not all([ad_title, ad_copy]):
            self._errors["mastheads"] = self.error_class(
                [
                    gettext(
                        "A Title and Body Copy is required in order for an ad to be published for a site."
                    )
                ]
            )
        if not meta_title:
            cleaned_data["meta_title"] = name
        if not meta_description:
            cleaned_data["meta_description"] = description

        return cleaned_data

    def save(self, commit=True):
        instance = super().save(commit=False)
        # Default the masthead display image as the logo if it is unset
        if not instance.ad_image and instance.logo:
            instance.ad_image = instance.logo
        if commit:
            instance.save()
            instance.listing_mastheads.clear()
            listing_mastheads = self.cleaned_data.get("listing_mastheads")
            if listing_mastheads:
                for site_id in listing_mastheads:
                    masthead, created = BusinessMasthead.objects.get_or_create(
                        site_id=site_id
                    )
                    instance.listing_mastheads.add(masthead)
            instance.save()
        return instance
