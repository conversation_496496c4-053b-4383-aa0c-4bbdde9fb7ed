import csv
from datetime import datetime

from django.http import HttpResponse, StreamingHttpResponse
from django.utils.encoding import smart_str
from pendulum import Date

from longbeach.business.manage.utils import get_sites
from longbeach.business.models import Business
from longbeach.localads.templatetags.image_tags import transform_url


def chunks(lst, n):
    """Yield n-sized chunks from a list."""
    for i in range(0, len(lst), n):
        yield lst[i : i + n]


class BusinessCSVReport:
    """
    Base report class for CSV reports.
    """

    def get_http_response(self, rows):
        response = HttpResponse(content_type="text/csv")
        response["Content-Disposition"] = 'attachment; filename="report.csv"'
        report_writer = csv.writer(response)
        for row in rows:
            clean_row = [smart_str(i, errors="ignore") for i in row]
            report_writer.writerow(clean_row)
        return response


class Echo:
    """A dummy writer for streaming CSVs."""

    def write(self, value):
        """Write the value by returning it, instead of storing in a buffer."""
        return value


class StreamingCSVReport:
    """
    Build a CSV and stream the results.

    https://docs.djangoproject.com/en/1.8/howto/outputting-csv/#streaming-large-csv-files
    """

    filename = "report"

    heading_row = None

    def get_row(self, obj):
        """Process a single object into a row for the CSV."""
        raise NotImplementedError

    def get_queryset(self):
        """Get the queryset to iterate through."""
        raise NotImplementedError

    def csv_generator(self):
        """A generator that yields the heading row and each object."""
        if self.heading_row:
            yield self.heading_row

        for obj in self.get_queryset():
            yield tuple(smart_str(d) for d in self.get_row(obj))

    def generate_csv_response(self):
        """Generate the CSV and stream it."""
        pseudo_buffer = Echo()
        writer = csv.writer(pseudo_buffer)

        if self.heading_row:
            writer.writerow(self.heading_row)

        response = StreamingHttpResponse(
            (writer.writerow(row) for row in self.csv_generator()),
            content_type="text/csv",
        )

        response["Content-Disposition"] = (
            'attachment; filename="%s.csv"' % self.filename
        )

        return response


class BusinessReport(StreamingCSVReport):
    """A report of all business listings data."""

    heading_row = (
        "Business ID",
        "Name",
        "Active?",
        "Account type",
        "Impact subtype",
        "Description",
        "Logo",
        "Hero",
        "Listing start date",
        "Listing end date",
        "Salesforce Opportunity ID",
        "Category",
        "Tags",
        "Contact name",
        "Location details",
        "Telephone",
        "Telephone 2",
        "Mobile",
        "Fax",
        "Email",
        "URL",
        "Additional hours",
        "Business hours",
        "Monaco ID",
        "SEO title",
        "SEO description",
        "Facebook",
        "Google+",
        "Twitter",
        "YouTube",
        "Instagram",
        "LinkedIn",
        "Listing mastheads",
        "Mastheads",
        "Ad title",
        "Ad body copy",
        "Ad image",
        "Ad contact me",
        "Ad last accessed",
    )

    def __init__(self, *args, **kwargs):
        """Build a list of site names by ID."""
        super().__init__(*args, **kwargs)

        self.sites = {
            key: value["name"] for key, value in list(get_sites().items())
        }

    def get_image_url(self, image):
        """Get the URL for an image."""
        return transform_url({}, image=image, width=300, height=300)

    def get_row(self, obj):
        """Process the business into a row."""
        return (
            obj.pk,
            obj.name,
            obj.is_active,
            obj.get_account_type_display(),
            obj.get_impact_subtype_display(),
            obj.description,
            self.get_image_url(obj.logo) if obj.logo else "",
            self.get_image_url(obj.hero) if obj.hero else "",
            obj.listing_start,
            obj.listing_end,
            obj.listing_client_id,
            obj.category.name,
            ", ".join(tag.name for tag in obj.tags.all()),
            obj.contact_name,
            obj.address,
            obj.telephone,
            obj.telephone_two,
            obj.mobile,
            obj.fax,
            obj.email,
            obj.url,
            obj.additional_hours,
            obj.business_hours,
            obj.monaco_id,
            obj.meta_title,
            obj.meta_description,
            obj.facebook,
            obj.google_plus,
            obj.twitter,
            obj.youtube,
            obj.instagram,
            obj.linkedin,
            ", ".join(
                self.sites.get(masthead.site_id, smart_str(masthead.site_id))
                for masthead in obj.listing_mastheads.all()
            ),
            ", ".join(
                self.sites.get(site, smart_str(site)) for site in obj.mastheads
            ),
            obj.ad_title,
            obj.ad_copy,
            self.get_image_url(obj.ad_image),
            obj.ad_contact_me,
            obj.ad_last_accessed,
        )

    def get_queryset(self):
        """Get all businesses."""
        return (
            Business.objects.all()
            .select_related(
                "category",
            )
            .prefetch_related(
                "listing_mastheads",
                "tags",
            )
        )


class BusinessInventoryReport(BusinessCSVReport):
    """
    Generate an inventory report for the business listing's that:
        - Are of the IMPACT account_type
    """

    def generate_csv_response(self):
        # Headers
        rows = list()
        rows.append(
            [
                datetime.today().strftime("%Y-%m-%d"),
            ]
        )

        rows.append(
            [
                "Business ID",
                "Business Name",
                "Subtype",
                "Start Date",
                "End Date",
                "Count of Website Content Ad Mastheads",
                "Website content ad Mastheads",
                "Count of Listings Mastheads",
                "Listing Mashteads",
                "Salesforce Opportunity ID",
            ]
        )

        sites = get_sites()

        # For all business that are impact accounts
        B = Business.objects.filter(
            account_type=Business.ACCOUNT_IMPACT
        ).prefetch_related("listing_mastheads")
        for b in B:
            # First four columns
            row = [b.id, b.name, b.impact_subtype, b.listing_start or ""]

            # Calculate end date using the standard rule
            # - Subscription accounts do not have end dates
            # - Classified accounts end on 13 weeks from the start date
            # - Forward revenue accounts end on 12 months from the start date
            end_date = ""
            if b.impact_subtype == "Classified":
                end_date = Date.instance(b.listing_start).add(weeks=13)
            if b.impact_subtype == "Forward Rev":
                end_date = Date.instance(b.listing_start).add(months=12)
            row.append(end_date)

            # Calculate website content ad mastheads and listing mastheads
            # prefect_related had been used above to speedup the query
            row.append(len(b.mastheads))
            row.append(
                "|".join(
                    [
                        sites.get(site).get("name")
                        for site in b.mastheads
                        if sites.get(site)
                    ]
                )
            )
            row.append(len(b.listing_mastheads.all()))
            row.append(
                "|".join(
                    [
                        sites.get(site.site_id).get("name")
                        for site in b.listing_mastheads.all()
                        if sites.get(site.site_id)
                    ]
                )
            )
            row.append(b.listing_client_id)

            rows.append(row)

        response = self.get_http_response(rows)
        return response


class UnapprovedListings(BusinessCSVReport):
    def generate_csv_response(self):
        # Headers
        rows = list()
        rows.append(
            [
                datetime.today().strftime("%Y-%m-%d"),
            ]
        )

        rows.append(
            [
                "Business ID",
                "Business Name",
                "Category",
                "Email",
                "Business phone number",
                "Business address",
                "Contact Name",
                "Masthead",
                "Date submitted",
            ]
        )
        sites = get_sites()
        masthead = None

        # For all business that are impact accounts
        B = (
            Business.objects.filter(account_type=Business.ACCOUNT_UNAPPROVED)
            .select_related("category")
            .prefetch_related("listing_mastheads")
        )
        for b in B:
            mastheads = b.listing_mastheads.all()
            if mastheads:
                site_id = mastheads[0].site_id
                masthead = sites.get(site_id).get("name")
            row = [
                b.id,
                b.name,
                b.category,
                b.email,
                b.telephone,
                b.address,
                b.contact_name,
                masthead,
                b.created_on,
            ]
            rows.append(row)

        response = self.get_http_response(rows)
        return response
