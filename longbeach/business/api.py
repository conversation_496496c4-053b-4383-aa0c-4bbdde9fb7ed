import datetime
from random import sample
from urllib.parse import urlencode

from django.core.exceptions import MultipleObjectsReturned
from django.utils.timezone import get_current_timezone, now as datetime_now
from haystack.exceptions import SpatialError
from haystack.models import SearchResult
from haystack.query import SQ, AutoQuery, RelatedSearchQuerySet
from oauthsome.oauth_server.contrib.tastypie import OAuthAuthentication
from shared_orgs_client.api import orgs_for_user
from shared_orgs_client.utils import OrgsAPIMixin, _should_limit_by_orgs
from tastypie import fields
from tastypie.authorization import Authorization
from tastypie.bundle import Bundle
from tastypie.exceptions import InvalidFilterError, NotFound
from tastypie.paginator import Paginator as _Paginator
from tastypie.resources import ModelResource
from tastypie.serializers import Serializer
from tastypie.utils import format_datetime, make_naive

from longbeach.business.models import (
    Business,
    BusinessCategory,
    BusinessFeatureAd,
    BusinessMasthead,
    OpeningHours,
)
from longbeach.business.search import SCORE_FIELD

BOOLEAN_TRUE_STRINGS = ["true", "yes", "1"]
BOOLEAN_FALSE_STRINGS = ["false", "no", "0"]


def image_info(image):
    if not image:
        return None
    return {"uri": str(image.name.lstrip("/")), "cdn_url": str(image.url)}


class Paginator(_Paginator):
    """
    Subclass of `tastypie.paginator.Paginator` to fix next/previous.

    """

    def _generate_uri(self, limit, offset):
        if self.resource_uri is None:
            return None

        try:
            # QueryDict has a urlencode method that can handle multiple
            # values for the same key
            request_params = self.request_data.copy()
            request_params["limit"] = limit
            request_params["offset"] = offset
            encoded_params = request_params.urlencode()
        except AttributeError:
            request_params = {}

            for k, v in list(self.request_data.items()):
                if isinstance(v, str):
                    request_params[k] = v.encode("utf-8")
                else:
                    request_params[k] = v

            request_params.update({"limit": limit, "offset": offset})
            encoded_params = urlencode(request_params)

        return "%s?%s" % (self.resource_uri, encoded_params)


class BusinessCategoryResource(ModelResource):
    image = fields.DictField()

    class Meta:
        resource_name = "category"
        queryset = BusinessCategory.objects.all()
        authorization = Authorization()
        authentication = OAuthAuthentication(
            accept_session=True, credentials_allowed="client"
        )
        list_allowed_methods = ["get"]
        detail_allowed_methods = []
        filtering = {
            "slug": ["exact"],
        }
        paginator_class = Paginator

    def dehydrate_image(self, bundle):
        try:
            return image_info(bundle.obj.image)
        except ValueError:
            return None


class TzSerializer(Serializer):
    """
    A subclass of the built-in Serializer that adds timezone offsets to
    outputted times, making them ISO-8601 compliant.

    adapted from the patch in this pull request:
    https://github.com/toastdriven/django-tastypie/pull/445
    """

    def format_datetime(self, data):
        """
        A hook to control how datetimes are formatted.

        Can be overridden at the ``Serializer`` level (``datetime_formatting``)
        or globally (via ``settings.TASTYPIE_DATETIME_FORMATTING``).

        Default is ``iso-8601``, which looks like "2010-12-16T03:02:14+00:00".
        """
        data = make_naive(data)
        if self.datetime_formatting == "rfc-2822":
            return format_datetime(data)

        tzinfo = get_current_timezone()
        if tzinfo:
            data = datetime.datetime(
                data.year,
                data.month,
                data.day,
                data.hour,
                data.minute,
                data.second,
                data.microsecond,
                tzinfo,
            )

        return data.isoformat()


class SearchPaginator(Paginator):
    def page(self):
        """
        Return data for the current page.

        Overrides `tastypie.paginator.Paginator` to change the order of
        calls to `self.get_count()` and `self.get_slice()`. This avoids
        an additional call to the search backend.

        """
        limit = self.get_limit()
        offset = self.get_offset()
        objects = self.get_slice(limit, offset)
        count = self.get_count()
        meta = {
            "offset": offset,
            "limit": limit,
            "total_count": count,
        }

        if limit:
            meta["previous"] = self.get_previous(limit, offset)
            meta["next"] = self.get_next(limit, offset, count)

        return {
            self.collection_name: objects,
            "meta": meta,
        }


class OpeningHoursResource(ModelResource):
    class Meta:
        resource_name = "openinghours"
        queryset = OpeningHours.objects.all()
        authorization = Authorization()
        authentication = OAuthAuthentication(
            accept_session=True, credentials_allowed="client"
        )
        list_allowed_methods = ["get"]
        excludes = ["id"]
        include_resource_uri = False


class BusinessFeatureAdResource(ModelResource):
    """
    Business feature ad resource.

    This returns a business feature ad object filtered by a Silverstone tag and masthead id.
    """

    class Meta:
        resource_name = "business_feature_ad"
        queryset = BusinessFeatureAd.objects.get_active_by_last_accessed()  # type: ignore[attr-defined]
        authorization = Authorization()
        authentication = OAuthAuthentication(
            accept_session=True, credentials_allowed="client"
        )
        list_allowed_methods = ["get"]
        detail_allowed_methods = ["get", "delete"]

    def dehydrate(self, bundle):
        """
        Include additional fields from the Business profile in the response.
        """
        bundle.data["business_id"] = bundle.obj.business.id
        bundle.data["category_name"] = bundle.obj.business.category.name
        bundle.data["category_slug"] = bundle.obj.business.category.slug
        bundle.data["telephone"] = bundle.obj.business.telephone
        bundle.data["business_name"] = bundle.obj.business.name
        return bundle

    def obj_get_list(self, bundle, **kwargs):
        """
        Override this method to impose our limit and update the last_accessed timestamps.
        """
        tag = bundle.request.GET.get("tag")
        masthead_site_id = bundle.request.GET.get("masthead__site_id")
        if not masthead_site_id or not tag:
            return []
        limit = int(bundle.request.GET.get("limit", 8))
        masthead_site_id = int(masthead_site_id)
        now = datetime_now()
        base_object_list = self.get_object_list(bundle.request).filter(
            mastheads__site_id=masthead_site_id,
            tag=tag,
        )
        limit_max = len(base_object_list)
        limit = min(limit, limit_max)
        object_slice = base_object_list[:limit]
        BusinessFeatureAd.objects.filter(
            id__in=[obj.id for obj in object_slice]
        ).update(last_accessed=now)
        return object_slice


class BusinessMastheadResource(ModelResource):
    class Meta:
        resource_name = "listingmastheads"
        queryset = BusinessMasthead.objects.all()
        authorization = Authorization()
        authentication = OAuthAuthentication(
            accept_session=True, credentials_allowed="client"
        )
        detail_allowed_methods = ["get"]
        excludes = ["id"]
        include_resource_uri = False

    def dehydrate(self, bundle):
        """Flatten to just the site ID."""
        return bundle.data["site_id"]


class BusinessResource(OrgsAPIMixin, ModelResource):
    """
    Business resource.
    """

    images = fields.ListField()
    category = fields.ToOneField(
        BusinessCategoryResource, "category", null=True
    )
    opening_hours = fields.ToManyField(
        OpeningHoursResource, "opening_hours", full=True
    )
    hero = fields.DictField()
    category_image = fields.DictField()
    mastheads = fields.ListField(attribute="mastheads")
    listing_mastheads = fields.ManyToManyField(
        BusinessMastheadResource, "listing_mastheads", full=True
    )
    category_attrs = fields.DictField()

    class Meta:
        resource_name = "business"
        queryset = Business.orgs_manager.prefetch_related("images")
        authorization = Authorization()
        authentication = OAuthAuthentication(
            accept_session=True, credentials_allowed="client"
        )
        list_allowed_methods = ["get"]
        detail_allowed_methods = ["get"]
        paginator_class = SearchPaginator

        serializer = TzSerializer()
        filtering = {
            "is_active": ["exact"],
            "account_type": ["in"],
            "organization": ["exact"],
            "category": ["exact"],
            "mastheads": ["in"],
        }

    def apply_filters(self, request, filters):
        """
        Return a Haystack `RelatedSearchQuerySet` for the request.
        """
        sqs = (
            RelatedSearchQuerySet()
            .models(Business)
            .load_all()
            .load_all_queryset(
                Business,
                Business.objects.all()
                .select_related("category")
                .prefetch_related("images", "opening_hours"),
            )
        )
        qp = request.GET
        order_field_choices = ["account_type"]
        q = qp.get("q", "").strip()
        if q:
            _q = AutoQuery(q)
            sqs = sqs.filter(
                SQ(name=_q) | SQ(text=_q) | SQ(address=_q) | SQ(tags_text=_q)
            )
            order_field_choices.append(SCORE_FIELD)

        sqs = sqs.exclude(
            account_type__in=[
                Business.ACCOUNT_INACTIVE,
                Business.ACCOUNT_UNAPPROVED,
            ]
        )

        site_id = qp.get("site_id")
        if site_id:
            sqs = sqs.filter(listing_masthead_site_ids=site_id)

        if "account_type__in" in filters:
            account_type = list(filters["account_type__in"])
            for account in account_type:
                sqs = sqs.filter_or(account_type=account)

        if "mastheads__in" in filters:
            site_ids = list(filters["mastheads__in"])
            sqs = sqs.narrow(
                "mastheads:(%s)" % " OR ".join('"%s"' % i for i in site_ids)
            )

        if "organization__exact" in filters:
            try:
                org_id = int(filters["organization__exact"])
            except ValueError:
                raise InvalidFilterError("'organization' must be an integer")
            if _should_limit_by_orgs(request) and (
                org_id not in [o.id for o in orgs_for_user(request.user)]
            ):
                # Could be either user isn't a member or org doesn't exist.
                raise InvalidFilterError("'organization' not valid")
            sqs = sqs.narrow("organization:(%s)" % org_id)
        elif _should_limit_by_orgs(request):
            user_org_ids = [o.id for o in orgs_for_user(request.user)]
            if not user_org_ids:
                return sqs.none()
            sqs = sqs.narrow(
                "organization:(%s)" % " OR ".join(str(o) for o in user_org_ids)
            )

        category_slug = qp.get("category_slug", "").strip()
        if category_slug:
            try:
                category = BusinessCategory.objects.get(slug=category_slug)
            except BusinessCategory.DoesNotExist:
                return sqs.none()
            sqs = sqs.narrow("category:(%s)" % category.id)
        if "category__exact" in filters:
            try:
                category_id = int(filters["category__exact"])
            except ValueError:
                raise InvalidFilterError("'category' must be an integer")
            sqs = sqs.narrow("category:(%s)" % category_id)

        order_field_choices.append("name")
        order_field_choices.append("updated_on")
        order_by = qp.get("order_by", "")
        order_reverse = order_by.startswith("-")

        order_field = order_by[1:] if order_reverse else order_by
        if order_field not in order_field_choices:
            order_field = order_field_choices[0]
            order_reverse = False

        if order_field == "score":
            order_reverse = not order_reverse
        elif order_field == "name":
            order_field = "name_sort"

        if order_field:
            sqs = sqs.order_by(
                "%s%s" % ("-" if order_reverse else "", order_field)
            )
        random_sample = qp.get("sample", "").strip()
        limit = qp.get("limit", "").strip()
        try:
            limit = int(limit)
        except ValueError:
            return sqs
        else:
            limit = limit if limit and len(sqs) > limit else len(sqs)
            # TODO: This will be replaced with new ESOV rules at some point
            if random_sample:
                return sample(list(sqs), limit)
            return sqs

    def apply_sorting(self, obj_list, options=None):
        # Sorting is handled by `apply_filters()`.
        return obj_list

    def build_bundle(self, obj=None, data=None, request=None):
        if obj is None:
            obj = self._meta.object_class()
        else:
            data = data or {}
            if isinstance(obj, SearchResult):
                try:
                    if hasattr(obj, "distance"):
                        data["distance"] = round(obj.distance.km, 3)
                except SpatialError:
                    pass
                data["tags"] = sorted([t for t in obj.tags or []])
                obj = obj.object
            else:
                data["tags"] = sorted(obj.tags.values_list("name", flat=True))
        return Bundle(obj=obj, data=data, request=request)

    def obj_get(self, bundle, **kwargs):
        # Return only active businesses.
        try:
            base_object_list = (
                self.get_object_list(bundle.request)
                .filter(**kwargs)
                .exclude(
                    account_type__in=[
                        Business.ACCOUNT_INACTIVE,
                        Business.ACCOUNT_UNAPPROVED,
                    ]
                )
            )
            object_list = self.authorized_read_list(base_object_list, bundle)
            stringified_kwargs = ", ".join(
                ["%s=%s" % (k, v) for k, v in list(kwargs.items())]
            )
            if len(object_list) <= 0:
                raise self._meta.object_class.DoesNotExist(
                    "Couldn't find an instance of '%s' which matched '%s'."
                    % (self._meta.object_class.__name__, stringified_kwargs)
                )
            if len(object_list) > 1:
                raise MultipleObjectsReturned(
                    "More than '%s' matched '%s'."
                    % (self._meta.object_class.__name__, stringified_kwargs)
                )
        except ValueError:
            raise NotFound(
                "Invalid resource lookup data provided (mismatched type)."
            )
        obj = object_list[0]
        obj._is_single_obj = True
        bundle.request.GET
        return obj

    def dehydrate(self, bundle):
        if getattr(bundle.obj, "_is_single_obj", False):
            for field in ("category", "logo"):
                bundle.data[field] = getattr(bundle.obj, field)
            try:
                bundle.data["category_image"] = image_info(
                    bundle.obj.category.image
                )
            except ValueError:
                bundle.data["category_image"] = None

        cached_category = bundle.obj._meta.get_field(
            "category"
        ).get_cached_value(bundle.obj, None)
        bundle.data["category_attrs"] = (
            {
                "id": cached_category.id,
                "name": cached_category.name,
                "slug": cached_category.slug,
            }
            if cached_category
            else {}
        )
        return bundle

    def dehydrate_logo(self, bundle):
        return str(bundle.obj.logo)

    def dehydrate_hero(self, bundle):
        try:
            return image_info(bundle.obj.hero)
        except ValueError:
            return None

    def dehydrate_images(self, bundle):
        return [i.image for i in bundle.obj.images.all()]
