"""
This file demonstrates writing tests using the unittest module. These will pass
when you run "manage.py test".

Replace this with more appropriate tests for your application.
"""

import datetime
from json import loads
from unittest import mock
from urllib.parse import urlencode

from django.contrib.auth.models import User
from django.test import TestCase
from django.test.utils import override_settings
from haystack import connections

from longbeach.business.models import Business, BusinessCategory

TEST_ORG_ID = 1


class BusinessTest(TestCase):
    @mock.patch("geocode.models.GeocodeBase._geocode_lookup")
    def setUp(self, patch):
        self.username, self.password = "test", "test"
        user = User.objects.create_user(self.username, "", self.password)
        self.biz_args = {
            "created_by": user,
            "updated_by": user,
            "organization": TEST_ORG_ID,
        }
        self.cat1 = BusinessCategory.objects.create(name="cat 1")
        self.cat2 = BusinessCategory.objects.create(name="cat 2")
        self.cat3 = BusinessCategory.objects.create(name="cat 3")
        self.cat4 = BusinessCategory.objects.create(name="cat 4")
        self.biz1 = Business.objects.create(
            name="biz 1", address="20 King Street, Sydney", **self.biz_args
        )
        self.biz2 = Business.objects.create(name="biz 2", **self.biz_args)
        self.biz3 = Business.objects.create(name="biz 3", **self.biz_args)
        self.biz_args["organization"] = 2
        self.biz4 = Business.objects.create(name="biz 4", **self.biz_args)
        self.biz1.category = self.cat1
        self.biz1.save()
        self.biz2.category = self.cat2
        self.biz2.save()
        self.biz3.category = self.cat3
        self.biz3.save()
        self.biz4.category = self.cat4
        self.biz4.save()
        self.update_haystack_index()

    def tearDown(self):
        super().tearDown()

        BusinessCategory.objects.all().delete()
        User.objects.all().delete()
        Business.objects.all().delete()

    def update_haystack_index(self):
        engine = connections["default"]
        backend = engine.get_backend()
        unified_index = engine.get_unified_index()
        business_index = unified_index.get_index(Business)
        backend.update(business_index, Business.objects.all())

    @override_settings(TEST_ORG_IDS=[TEST_ORG_ID])
    def test_api(self):
        """
        Basic API tests.
        """
        api_url = lambda **kw: "/api/business/?%s" % urlencode(kw)
        # HACK: q=biz so that Whoosh has a keyword to search with.
        api_response = lambda **kw: self.client.get(api_url(q="biz", **kw))
        api_json = lambda **kw: loads(api_response(**kw).content)["objects"]
        # Unauthenticated
        self.assertEqual(api_response().status_code, 401)
        # Authenticated
        self.client.login(username=self.username, password=self.password)
        self.assertEqual(api_response().status_code, 200)
        # All businesses returned for the test org.
        self.assertEqual(len(api_json()), 3)
        # Businesses for cat.
        self.assertEqual(len(api_json(category=self.cat1.id)), 1)


class BusinessManagerTest(TestCase):
    """
    Tests for `BusinessManager` custom model manager.

    """

    def setUp(self):
        self.category = BusinessCategory.objects.create(name="Cloud Providers")
        self.user = User.objects.create_user("businesslistings")
        kw = {
            "category": self.category,
            "organization": TEST_ORG_ID,
            "created_by": self.user,
            "updated_by": self.user,
        }
        today = datetime.date.today()
        past = today - datetime.timedelta(days=7)
        future = today + datetime.timedelta(days=7)
        Business.objects.create(
            listing_start=None,
            listing_end=None,
            name="Amazon Web Services",
            **kw,
        )
        Business.objects.create(
            listing_start=None, listing_end=past, name="Rackspace", **kw
        )
        Business.objects.create(
            listing_start=None,
            listing_end=today,
            name="Google Compute Engine",
            **kw,
        )
        Business.objects.create(
            listing_start=None, listing_end=future, name="Windows Azure", **kw
        )
        Business.objects.create(
            listing_start=past, listing_end=None, name="SoftLayer", **kw
        )
        Business.objects.create(
            listing_start=past, listing_end=past, name="HP Public Cloud", **kw
        )
        Business.objects.create(
            listing_start=past,
            listing_end=today,
            name="Telstra Cloud Services",
            **kw,
        )
        Business.objects.create(
            listing_start=past,
            listing_end=future,
            name="Optus PowerON Cloud Solutions",
            **kw,
        )
        Business.objects.create(
            listing_start=today, listing_end=None, name="Linode", **kw
        )
        Business.objects.create(
            listing_start=today,
            listing_end=past,
            name="Google App Engine",
            **kw,
        )
        Business.objects.create(
            listing_start=today, listing_end=today, name="Heroku", **kw
        )
        Business.objects.create(
            listing_start=today, listing_end=future, name="Joyent", **kw
        )
        Business.objects.create(
            listing_start=future, listing_end=None, name="Engine Yard", **kw
        )
        Business.objects.create(
            listing_start=future, listing_end=past, name="Gondor", **kw
        )
        Business.objects.create(
            listing_start=future, listing_end=today, name="Ninefold", **kw
        )
        Business.objects.create(
            listing_start=future,
            listing_end=future,
            name="Elastic COBOL",
            **kw,
        )

    def tearDown(self):
        super().tearDown()

        BusinessCategory.objects.all().delete()
        User.objects.all().delete()
        Business.objects.all().delete()

    def test_current_queryset(self):
        """
        `BusinessManager.current()` returns only current listings.

        """
        business_names = set(b.name for b in Business.objects.current())
        expected_names = set(
            [
                "Amazon Web Services",
                "Google Compute Engine",
                "Windows Azure",
                "SoftLayer",
                "Telstra Cloud Services",
                "Optus PowerON Cloud Solutions",
                "Linode",
                "Heroku",
                "Joyent",
            ]
        )
        assert business_names == expected_names

    def test_none_queryset(self):
        """
        `.current()` is still valid on an empty queryset.

        """
        businesses = Business.objects.none()
        businesses = businesses.current()
        assert len(businesses) == 0
