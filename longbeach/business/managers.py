import datetime

from django.db.models import Manager, Q, QuerySet
from shared_orgs_client.utils import OrgsManager, OrgsQuerySet


class BusinessManager(Manager):
    """
    Subclass of `models.Manager` that uses `BusinessQuerySet`.

    """

    def get_query_set(self):
        return BusinessQuerySet(self.model, using=self._db)

    def current(self, *args, **kwargs):
        return self.get_query_set().current(*args, **kwargs)


class BusinessOrgsManager(OrgsManager, BusinessManager):
    """
    Subclass of `OrgsManager` that uses `BusinessOrgsQuerySet`.

    """

    def get_query_set(self):
        return BusinessOrgsQuerySet(self.model, using=self._db)


class BusinessQuerySet(QuerySet):
    """
    Subclass of `QuerySet` implementing listings methods.

    """

    def current(self, time=None, exclude=False):
        """Return businesses within `listing_start` and `listing_end`."""
        if time is None:
            time = datetime.date.today()
        query = self.exclude if exclude else self.filter
        return query(
            Q(listing_start__isnull=True, listing_end__isnull=True)
            | Q(listing_start__isnull=True, listing_end__gte=time)
            | Q(listing_end__isnull=True, listing_start__lte=time)
            | Q(listing_start__lte=time, listing_end__gte=time)
        )


class BusinessOrgsQuerySet(OrgsQuerySet, BusinessQuerySet):
    """
    Subclass of `OrgsQuerySet` implementing listings for shared orgs.

    """


class BusinessFeatureAdManager(Manager):
    """
    Manager for returning business feature ads according to ESOV rules.
    """

    def get_active_by_last_accessed(self):
        qs = (
            super()
            .get_queryset()
            .filter(expiration_date__gte=datetime.date.today())
            .order_by("last_accessed")
        )
        return qs
