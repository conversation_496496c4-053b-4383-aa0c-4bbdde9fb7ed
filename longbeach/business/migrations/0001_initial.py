# -*- coding: utf-8 -*-


from django.db import migrations, models
import autoslug.fields
from django.conf import settings
import valencia_storage.storage
import taggit.managers
import shared_orgs_client.utils


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('taggit', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Business',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('lookup_address', models.TextField(null=True, editable=False)),
                ('resolved_address', models.TextField(null=True, editable=False)),
                ('latitude', models.FloatField(null=True, editable=False)),
                ('longitude', models.FloatField(null=True, editable=False)),
                ('organization', shared_orgs_client.utils.OrgField(max_length=255, db_index=True)),
                ('name', models.CharField(max_length=1000)),
                ('description', models.TextField(blank=True)),
                ('logo', models.ImageField(storage=valencia_storage.storage.ValenciaStorage(), upload_to='images', blank=True)),
                ('hero', models.ImageField(storage=valencia_storage.storage.ValenciaStorage(), null=True, upload_to='images', blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('listing_start', models.DateField(null=True, blank=True)),
                ('listing_end', models.DateField(null=True, blank=True)),
                ('listing_client_id', models.CharField(max_length=100, blank=True)),
                ('address', models.CharField(max_length=1000)),
                ('telephone', models.CharField(max_length=500, blank=True)),
                ('mobile', models.CharField(max_length=500, blank=True)),
                ('fax', models.CharField(max_length=500, blank=True)),
                ('email', models.CharField(max_length=2000, blank=True)),
                ('url', models.URLField(blank=True)),
                ('business_hours', models.TextField(max_length=1000, blank=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('monaco_id', models.IntegerField(null=True, editable=False)),
                ('meta_title', models.CharField(max_length=200, blank=True)),
                ('meta_description', models.TextField(blank=True)),
            ],
            options={
                'ordering': ['name'],
                'verbose_name': 'Business',
                'verbose_name_plural': 'Businesses',
            },
        ),
        migrations.CreateModel(
            name='BusinessCategory',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('name', models.CharField(max_length=1000)),
                ('slug', autoslug.fields.AutoSlugField(max_length=250, null=True, editable=False)),
                ('image', models.ImageField(storage=valencia_storage.storage.ValenciaStorage(), null=True, upload_to='images')),
            ],
            options={
                'ordering': ['name'],
                'verbose_name': 'Category',
                'verbose_name_plural': 'Categories',
            },
        ),
        migrations.CreateModel(
            name='Image',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('image', models.ImageField(storage=valencia_storage.storage.ValenciaStorage(), upload_to='images')),
                ('order', models.IntegerField()),
                ('caption', models.CharField(max_length=500)),
                ('business', models.ForeignKey(on_delete=models.deletion.CASCADE, related_name='images', to='business.Business')),
            ],
        ),
        migrations.AddField(
            model_name='business',
            name='category',
            field=models.ForeignKey(on_delete=models.deletion.CASCADE, related_name='businesses', to='business.BusinessCategory', null=True),
        ),
        migrations.AddField(
            model_name='business',
            name='created_by',
            field=models.ForeignKey(on_delete=models.deletion.CASCADE, related_name='business', editable=False, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='business',
            name='tags',
            field=taggit.managers.TaggableManager(to='taggit.Tag', through='taggit.TaggedItem', help_text='A comma-separated list of tags.', verbose_name='Tags'),
        ),
        migrations.AddField(
            model_name='business',
            name='updated_by',
            field=models.ForeignKey(on_delete=models.deletion.CASCADE, related_name='business_updated', editable=False, to=settings.AUTH_USER_MODEL),
        ),
    ]
