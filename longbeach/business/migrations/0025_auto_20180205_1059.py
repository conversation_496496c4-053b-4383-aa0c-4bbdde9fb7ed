# -*- coding: utf-8 -*-


from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0024_auto_20180108_1637'),
    ]

    operations = [
        migrations.CreateModel(
            name='ESOVTracker',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('site_id', models.IntegerField()),
                ('last_displayed', models.DateTimeField(auto_now_add=True)),
                ('business', models.ForeignKey(on_delete=models.deletion.CASCADE, to='business.Business')),
            ],
        ),
        migrations.AlterUniqueTogether(
            name='esovtracker',
            unique_together=set([('business', 'site_id')]),
        ),
    ]
