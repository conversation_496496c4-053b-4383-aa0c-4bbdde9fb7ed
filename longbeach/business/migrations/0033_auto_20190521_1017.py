# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2019-05-21 00:17


import datetime

from django.contrib.auth import get_user_model
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
from django.utils.timezone import make_aware

User = get_user_model()

create_date = make_aware(datetime.datetime(2019, 5, 17, 12, 30))

def get_system_user_id():
    return User.objects.get(email='<EMAIL>').id


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('business', '0032_business_video_montage'),
    ]

    operations = [
        migrations.AddField(
            model_name='businessfeaturead',
            name='created_by',
            field=models.ForeignKey(editable=False, on_delete=django.db.models.deletion.CASCADE, related_name='business_feature_ad', to=settings.AUTH_USER_MODEL),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='businessfeaturead',
            name='created_on',
            field=models.DateTimeField(auto_now_add=True, default=create_date),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='businessfeaturead',
            name='updated_by',
            field=models.ForeignKey(editable=False, on_delete=django.db.models.deletion.CASCADE, related_name='business_feature_ad_updated', to=settings.AUTH_USER_MODEL),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='businessfeaturead',
            name='updated_on',
            field=models.DateTimeField(auto_now=True),
        ),
    ]
