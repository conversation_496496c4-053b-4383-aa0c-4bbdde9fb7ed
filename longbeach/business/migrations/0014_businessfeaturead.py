# -*- coding: utf-8 -*-


from django.db import migrations, models
import valencia_storage.storage


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0013_auto_20170810_1525'),
    ]

    operations = [
        migrations.CreateModel(
            name='BusinessFeatureAd',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('tag', models.CharField(max_length=255)),
                ('ad_title', models.CharField(help_text='Max Characters allowed is 24.', max_length=24, verbose_name='Title', blank=True)),
                ('ad_copy', models.TextField(help_text='Max Characters allowed is 62.', max_length=62, verbose_name='Body Copy', blank=True)),
                ('ad_image', models.ImageField(upload_to='ad_images', storage=valencia_storage.storage.ValenciaStorage(), verbose_name='Image', blank=True)),
                ('ad_contact_me', models.<PERSON><PERSON>an<PERSON>ield(default=False, help_text='On mobile layouts a call button is made visible.', verbose_name='Contact Me')),
                ('publication_date', models.DateField(null=True, blank=True)),
                ('business', models.ForeignKey(on_delete=models.deletion.CASCADE, related_name='business_features', to='business.Business')),
                ('mastheads', models.ManyToManyField(to='business.BusinessMasthead')),
            ],
        ),
    ]
