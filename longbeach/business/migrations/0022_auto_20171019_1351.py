# -*- coding: utf-8 -*-


from django.db import migrations, models
import datetime
from django.utils.timezone import now as datetime_now


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0021_auto_20170915_1419'),
    ]

    operations = [
        migrations.AddField(
            model_name='business',
            name='listing_mastheads',
            field=models.ManyToManyField(related_name='business_listings', to='business.BusinessMasthead'),
        ),
        migrations.AlterField(
            model_name='business',
            name='account_type',
            field=models.IntegerField(default=1, choices=[(0, 'Inactive'), (1, 'Listing'), (2, 'Impact'), (3, 'Premium')]),
        ),
        migrations.AlterField(
            model_name='business',
            name='ad_last_accessed',
            field=models.DateTimeField(default=datetime_now, editable=False),
        ),
    ]
