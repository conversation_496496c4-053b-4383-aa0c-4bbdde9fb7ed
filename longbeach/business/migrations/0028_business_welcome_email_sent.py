# -*- coding: utf-8 -*-


from django.contrib.auth.models import User
from django.db import migrations, models, IntegrityError

def forwards_func(apps, schema_editor):
    Business = apps.get_model("business", "Business")
    Business.objects.all().update(welcome_email_sent=True)

    try:
        l = User.objects.create_user(username='longbot')  # Create a bot user called 'longbot' to handle automated stuffs.
        l.is_active = False
        l.save()
    except IntegrityError:
        pass

def reverse_func(apps, schema_editor):
    pass

class Migration(migrations.Migration):
    dependencies = [
        ('business', '0027_populate_esovtracker'),
    ]

    operations = [
        migrations.AddField(
            model_name='business',
            name='welcome_email_sent',
            field=models.BooleanField(default=False),
        ),
        migrations.RunPython(forwards_func, reverse_func),

    ]
