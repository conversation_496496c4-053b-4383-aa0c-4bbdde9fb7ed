# -*- coding: utf-8 -*-


from django.db import migrations, models
import django.contrib.postgres.fields
import valencia_storage.storage
import taggit.managers


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='OpeningHours',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('weekday', models.IntegerField(default=1, choices=[(1, 'Monday'), (2, 'Tuesday'), (3, 'Wednesday'), (4, 'Thursday'), (5, 'Friday'), (6, 'Saturday'), (7, 'Sunday')])),
                ('from_hour', models.CharField(max_length=20)),
                ('to_hour', models.CharField(max_length=20)),
            ],
        ),
        migrations.AddField(
            model_name='business',
            name='account_type',
            field=models.IntegerField(default=1, choices=[(1, 'Listing'), (2, 'Impact'), (3, 'Premium'), (0, 'Inactive')]),
        ),
        migrations.AddField(
            model_name='business',
            name='ad_contact_me',
            field=models.BooleanField(default=False, help_text='On mobile layouts a call button is made visible.', verbose_name='Contact Me'),
        ),
        migrations.AddField(
            model_name='business',
            name='ad_copy',
            field=models.TextField(help_text='Max Characters allowed is 62.', max_length=62, verbose_name='Body Copy', blank=True),
        ),
        migrations.AddField(
            model_name='business',
            name='ad_image',
            field=models.ImageField(upload_to='ad_images', storage=valencia_storage.storage.ValenciaStorage(), verbose_name='Image', blank=True),
        ),
        migrations.AddField(
            model_name='business',
            name='ad_title',
            field=models.CharField(help_text='Max Characters allowed is 24.', max_length=24, verbose_name='Title', blank=True),
        ),
        migrations.AddField(
            model_name='business',
            name='additional_hours',
            field=models.CharField(help_text='Tell your customers if you are available outside normal business hours', max_length=500, blank=True),
        ),
        migrations.AddField(
            model_name='business',
            name='facebook',
            field=models.CharField(max_length=500, blank=True),
        ),
        migrations.AddField(
            model_name='business',
            name='google_plus',
            field=models.CharField(max_length=500, blank=True),
        ),
        migrations.AddField(
            model_name='business',
            name='mastheads',
            field=django.contrib.postgres.fields.ArrayField(default=list, null=True, base_field=models.IntegerField(), size=None, blank=True),
        ),
        migrations.AddField(
            model_name='business',
            name='telephone_two',
            field=models.CharField(max_length=500, verbose_name='Telephone no. 2', blank=True),
        ),
        migrations.AddField(
            model_name='business',
            name='twitter',
            field=models.CharField(max_length=500, blank=True),
        ),
        migrations.AddField(
            model_name='business',
            name='youtube',
            field=models.CharField(max_length=500, blank=True),
        ),
        migrations.AlterField(
            model_name='business',
            name='address',
            field=models.CharField(help_text='Help users locate your ad by entering your full address', max_length=1000, verbose_name='Location Details'),
        ),
        migrations.AlterField(
            model_name='business',
            name='listing_client_id',
            field=models.CharField(max_length=100, verbose_name='Salesforce Opportunity ID', blank=True),
        ),
        migrations.AlterField(
            model_name='business',
            name='logo',
            field=models.ImageField(storage=valencia_storage.storage.ValenciaStorage(), null=True, upload_to='images', blank=True),
        ),
        migrations.AlterField(
            model_name='business',
            name='tags',
            field=taggit.managers.TaggableManager(to='taggit.Tag', through='taggit.TaggedItem', blank=True, help_text='A comma-separated list of tags.', verbose_name='Tags'),
        ),
        migrations.AlterField(
            model_name='business',
            name='telephone',
            field=models.CharField(max_length=500, verbose_name='Telephone no', blank=True),
        ),
        migrations.AlterField(
            model_name='image',
            name='caption',
            field=models.CharField(default='', max_length=200),
        ),
        migrations.AlterField(
            model_name='image',
            name='order',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='openinghours',
            name='business',
            field=models.ForeignKey(on_delete=models.deletion.CASCADE, related_name='opening_hours', to='business.Business'),
        ),
        migrations.AlterUniqueTogether(
            name='openinghours',
            unique_together=set([('business', 'weekday')]),
        ),
    ]
