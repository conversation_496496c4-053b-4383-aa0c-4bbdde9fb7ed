# -*- coding: utf-8 -*-


from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0003_businesslistingevent'),
    ]

    operations = [
        migrations.AddField(
            model_name='businesslistingevent',
            name='device_type',
            field=models.IntegerField(default=0, choices=[(0, 'Mobile'), (1, 'Tablet'), (2, 'Desktop')]),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='businesslistingevent',
            name='event_type',
            field=models.IntegerField(choices=[(0, 'View'), (1, 'Click')]),
        ),
        migrations.AlterField(
            model_name='businesslistingevent',
            name='page_type',
            field=models.IntegerField(choices=[(0, 'Story'), (1, 'Homepage'), (2, 'Landing'), (3, 'Directory Listing'), (4, 'Profile')]),
        ),
    ]
