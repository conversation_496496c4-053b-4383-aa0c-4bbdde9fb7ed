"""
https://docs.djangoproject.com/en/1.8/ref/migration-operations/#runpython
Standard way to supply the forwards function since ESOV Tracker needs the initial data

    - For Each business that are IMPACT account
        - For each masthead of the above business (website content ad)
        - Updates the last_displayed as now() by using Django's auto_created=True
"""
from django.db import migrations

def forwards_func(apps, schema_editor):
    ESOVTracker = apps.get_model("business", "ESOVTracker")
    Business = apps.get_model("business", "Business")
    db_alias = schema_editor.connection.alias
    B = Business.objects.filter(account_type=2)
    ET = []
    for b in B:
        for m in b.mastheads:
            ET.append(ESOVTracker(business=b, site_id=m))

    ESOVTracker.objects.using(db_alias).bulk_create(ET)

def reverse_func(apps, schema_editor):
    pass

class Migration(migrations.Migration):
    dependencies = [
        ('business', '0026_auto_20180219_1110'),
    ]

    operations = [
        migrations.RunPython(forwards_func, reverse_func),
    ]
