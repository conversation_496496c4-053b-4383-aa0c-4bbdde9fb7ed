# -*- coding: utf-8 -*-
# Generated by Django 1.11.26 on 2019-11-26 00:08
import autoslug.fields
from django.db import migrations, models
import valencia_storage.storage


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0033_auto_20190521_1017'),
    ]

    operations = [
        migrations.AlterField(
            model_name='business',
            name='account_type',
            field=models.IntegerField(choices=[(0, 'Inactive'), (1, 'Listing'), (2, 'Impact'), (3, 'Premium'), (4, 'Unapproved')], db_index=True, default=1),
        ),
        migrations.AlterField(
            model_name='business',
            name='ad_contact_me',
            field=models.BooleanField(default=False, help_text='On mobile layouts a call button is made visible.', verbose_name='Contact Me'),
        ),
        migrations.AlterField(
            model_name='business',
            name='ad_copy',
            field=models.TextField(blank=True, help_text='Max Characters allowed is 62.', max_length=62, verbose_name='Body Copy'),
        ),
        migrations.AlterField(
            model_name='business',
            name='ad_image',
            field=models.ImageField(blank=True, storage=valencia_storage.storage.ValenciaStorage(), upload_to='ad_images', verbose_name='Image'),
        ),
        migrations.AlterField(
            model_name='business',
            name='ad_title',
            field=models.CharField(blank=True, help_text='Max Characters allowed is 24.', max_length=24, verbose_name='Title'),
        ),
        migrations.AlterField(
            model_name='business',
            name='additional_hours',
            field=models.CharField(blank=True, help_text='Tell your customers if you are available outside normal business hours', max_length=500),
        ),
        migrations.AlterField(
            model_name='business',
            name='address',
            field=models.CharField(help_text='Help users locate your ad by entering your full address', max_length=1000, verbose_name='Location Details'),
        ),
        migrations.AlterField(
            model_name='business',
            name='contact_name',
            field=models.CharField(blank=True, default='', max_length=100),
        ),
        migrations.AlterField(
            model_name='business',
            name='hero',
            field=models.ImageField(blank=True, null=True, storage=valencia_storage.storage.ValenciaStorage(), upload_to='images'),
        ),
        migrations.AlterField(
            model_name='business',
            name='impact_subtype',
            field=models.CharField(choices=[('Subscription', 'Subscription'), ('Classified', 'Classified'), ('Forward Rev', 'Forward Rev')], default='Subscription', max_length=20),
        ),
        migrations.AlterField(
            model_name='business',
            name='listing_client_id',
            field=models.CharField(blank=True, max_length=100, verbose_name='Salesforce Opportunity ID'),
        ),
        migrations.AlterField(
            model_name='business',
            name='logo',
            field=models.ImageField(blank=True, null=True, storage=valencia_storage.storage.ValenciaStorage(), upload_to='images'),
        ),
        migrations.AlterField(
            model_name='business',
            name='telephone',
            field=models.CharField(blank=True, max_length=500, verbose_name='Telephone no'),
        ),
        migrations.AlterField(
            model_name='business',
            name='telephone_two',
            field=models.CharField(blank=True, max_length=500, verbose_name='Telephone no. 2'),
        ),
        migrations.AlterField(
            model_name='business',
            name='video_montage',
            field=models.CharField(blank=True, max_length=100, verbose_name='Youtube shared link'),
        ),
        migrations.AlterField(
            model_name='businesscategory',
            name='image',
            field=models.ImageField(null=True, storage=valencia_storage.storage.ValenciaStorage(), upload_to='images'),
        ),
        migrations.AlterField(
            model_name='businesscategory',
            name='slug',
            field=autoslug.fields.AutoSlugField(editable=False, max_length=250, null=True, populate_from='name'),
        ),
        migrations.AlterField(
            model_name='businessfeaturead',
            name='ad_contact_me',
            field=models.BooleanField(default=False, help_text='On mobile layouts a call button is made visible.', verbose_name='Contact Me'),
        ),
        migrations.AlterField(
            model_name='businessfeaturead',
            name='ad_copy',
            field=models.TextField(blank=True, help_text='Max Characters allowed is 62.', max_length=62, verbose_name='Body Copy'),
        ),
        migrations.AlterField(
            model_name='businessfeaturead',
            name='ad_image',
            field=models.ImageField(blank=True, storage=valencia_storage.storage.ValenciaStorage(), upload_to='ad_images', verbose_name='Image'),
        ),
        migrations.AlterField(
            model_name='businessfeaturead',
            name='ad_title',
            field=models.CharField(blank=True, help_text='Max Characters allowed is 24.', max_length=24, verbose_name='Title'),
        ),
        migrations.AlterField(
            model_name='image',
            name='caption',
            field=models.CharField(default='', max_length=200),
        ),
        migrations.AlterField(
            model_name='image',
            name='image',
            field=models.ImageField(storage=valencia_storage.storage.ValenciaStorage(), upload_to='images'),
        ),
    ]
