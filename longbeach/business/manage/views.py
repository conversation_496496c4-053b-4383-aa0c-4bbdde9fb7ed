import datetime
import re
from collections import OrderedDict

from dateutil.relativedelta import relativedelta
from django import forms
from django.conf import settings
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib.gis.geos import Point
from django.contrib.gis.measure import D
from django.contrib.messages import error, info
from django.core.cache import cache
from django.core.mail import send_mail
from django.forms.models import inlineformset_factory
from django.shortcuts import get_object_or_404, redirect, render
from django.template.loader import get_template
from django.utils import timezone
from django.utils.decorators import method_decorator
from django.utils.text import slugify
from django.views.decorators.cache import cache_page
from django.views.generic import View
from django.views.generic.edit import CreateView, UpdateView
from django.views.generic.list import ListView
from haystack.query import SQ, AutoQuery, RelatedSearchQuerySet
from shared_orgs_client.api import orgs_for_user
from shared_orgs_client.utils import org_for_context, org_required

from longbeach.business.manage.forms import (
    BusinessCategoryForm,
    BusinessFeatureAdForm,
    BusinessForm,
)
from longbeach.business.manage.utils import get_sites
from longbeach.business.models import (
    Business,
    BusinessCategory,
    BusinessFeatureAd,
    Image,
    OpeningHours,
)
from longbeach.business.reports import (
    BusinessInventoryReport,
    BusinessReport,
    UnapprovedListings,
)
from longbeach.business.search import SCORE_FIELD
from longbeach.utils.search import term_to_query

from .paginator import HaystackPaginator


def any_permission_required(*perms):
    return user_passes_test(lambda u: any(u.has_perm(perm) for perm in perms))


class BusinessListView(ListView):
    """
    The main view for listing and managing business listings.

    Filterable and allows export.

    TODO: Access control for the reporting process.
    """

    paginate_by = 20
    paginator_class = HaystackPaginator
    template_name = "business/manage/business_list.html"

    @method_decorator(login_required)
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    def get_queryset(self):
        """
        Return the list of items for this view as a RelatedSearchQuerySet.

        QuerySet is constructed using the `filter()` method for search
        keywords and the `narrow()` method for facets. The difference is
        that `narrow()` queries do not contribute to the score and are
        faster to execute (depending on the search backend).

        """
        qp = self.request.GET
        qs = (
            RelatedSearchQuerySet()
            .load_all()
            .load_all_queryset(
                Business, Business.objects.all().select_related("category")
            )
        )
        self.filters = filters = {}
        self.order_field_choices = order_field_choices = ["account_type"]

        q = qp.get("q", "").strip()
        if q:
            _q = AutoQuery(q)
            qs = qs.filter(
                SQ(name=_q) | SQ(text=_q) | SQ(address=_q) | SQ(tags_text=_q)
            )
            filters["q"] = q
            order_field_choices.append(SCORE_FIELD)

        account_type = qp.get("account_type")
        if account_type:
            qs = qs.filter(account_type=account_type)

        product_type = qp.get("product_type")
        if product_type:
            qs = qs.narrow("has_%s:%d" % (product_type, 1))

        # Dont narrow on org if the request user is a member of certain groups
        org_id = org_for_context()
        if org_id:
            qs = qs.narrow("organization:(%s)" % org_id)
        elif (
            not self.request.user.is_superuser
            and not self.request.user.groups.filter(
                name__in=["Fulfilment Managers"]
            ).exists()
        ):
            org_ids = [org.id for org in orgs_for_user(self.request.user)]
            if not org_ids:
                return qs.none()
            qs = qs.narrow(
                "organization:(%s)" % " OR ".join('"%s"' % o for o in org_ids)
            )

        tags = self._get_param_list("tags", lambda v: v.strip(), ",")
        if tags:
            qs = qs.narrow(
                "tags:(%s)" % " AND ".join('"%s"' % t for t in tags)
            )
            filters["tags"] = tags
        qs = qs.facet("tags")

        categories = self._get_param_list("category", lambda v: v.strip(), ",")
        if categories:
            qs = qs.narrow(
                "category:(%s)" % " OR ".join('"%s"' % c for c in categories)
            )
            filters["category"] = categories
        qs = qs.facet("category")

        sites = self._get_param_list("site", lambda v: int(v.strip()), ",")
        site = self.sites.get(sites[0]) if sites else None
        if site:
            point = Point(site["longitude"], site["latitude"])
            dist = D(km=site["radius"])
            qs = qs.dwithin("point", point, dist).distance("point", point)
            order_field_choices.append("distance")
            filters["site"] = sites
        try:
            la, lo, rad = self.request.GET["location"].split()
            la = float(la)
            lo = float(lo)
            rad = float(rad)
        except (KeyError, ValueError):
            pass
        else:
            if not (la < -90 or la > 90 or lo < -180 or lo > 180):
                point = Point(lo, la)  # `Point` accepts long, lat.
                dist = D(km=float(rad))
                qs = qs.dwithin("point", point, dist)
                if not site:
                    qs = qs.distance("point", point)
                    order_field_choices.append("distance")
                filters["location"] = (la, lo, rad)

        order_field_choices.append("name")
        order_by = qp.get("order_by", "")
        order_reverse = order_by.startswith("-")
        order_field = order_by[1:] if order_reverse else order_by
        if order_field not in order_field_choices:
            order_field = order_field_choices[0]
            order_reverse = False

        self.order_field = order_field
        self.order_reverse = order_reverse

        if order_field == SCORE_FIELD:
            order_reverse = not order_reverse
        elif order_field == "name":
            order_field = "name_sort"
        elif order_field == "account_type":
            order_field = "-account_type"

        qs = qs.order_by("%s%s" % ("-" if order_reverse else "", order_field))

        return qs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        facet_counts = context["paginator"].object_list.facet_counts()
        category_facet = facet_counts.get("fields", {}).get("category", [])
        category_map = {str(c.pk): c for c in BusinessCategory.objects.all()}
        categories = [
            (category_map[str(pk)], count)
            for pk, count in category_facet
            if count > 0
        ]
        if "category" in self.filters:
            self.filters["category"] = [
                category_map[c] for c in self.filters["category"]
            ]
        if "site" in self.filters:
            self.filters["site"] = [
                self.sites[s] for s in self.filters["site"]
            ]

        def make_filter(name, label, options):
            selected_value = self.request.GET.get(name)
            selected_label = (
                options.get(selected_value, label) if selected_value else label
            )
            return (
                name,
                {
                    "selected": {
                        "value": selected_value,
                        "text": selected_label,
                    },
                    "options": options,
                },
            )

        filters = OrderedDict(
            [
                make_filter(
                    "account_type",
                    "Account Type",
                    OrderedDict(
                        [
                            (None, "Any Account Type"),
                            (Business.ACCOUNT_LISTING, "Listing"),
                            (Business.ACCOUNT_IMPACT, "Impact"),
                            (Business.ACCOUNT_PREMIUM, "Premium"),
                            (Business.ACCOUNT_INACTIVE, "Inactive"),
                            (Business.ACCOUNT_UNAPPROVED, "Unapproved"),
                        ]
                    ),
                ),
                make_filter(
                    "product_type",
                    "Product Type",
                    OrderedDict(
                        [
                            (None, "All Products"),
                            ("website_content_ad", "Website Content Ad"),
                            ("business_feature_ad", "Business Feature Ad"),
                        ]
                    ),
                ),
            ]
        )
        order_field_choices = OrderedDict(
            (field, label)
            for field, label in [
                (SCORE_FIELD, "Relevance"),
                ("distance", "Distance"),
                ("name", "Business Name"),
                ("account_type", "Account Type"),
            ]
            if field in self.order_field_choices
        )
        context.update(
            {
                "parsed_filters": self.filters,
                "tags": facet_counts.get("fields", {}).get("tags", []),
                "categories": categories,
                "sites": self.sites,
                "order_field": self.order_field,
                "order_reverse": self.order_reverse,
                "order_field_label": order_field_choices[self.order_field],
                "order_field_choices": order_field_choices,
                "is_filtered": bool(self.filters)
                or any(f["selected"]["value"] for f in filters.values()),
                "q": self.filters.get("q"),
                "filters": filters,
            }
        )

        return context

    @property
    def now(self):
        """
        Return the 'now' datetime for the current request.

        """
        if not hasattr(self, "_now"):
            self._now = timezone.now()

        return self._now

    @property
    def sites(self):
        """
        Return a list of sites from Suzuka.

        """
        if not hasattr(self, "_sites"):
            self._sites = get_sites()

        return self._sites

    def _filter_value(self, value):
        """
        Return a value for use in a RelatedSearchQuerySet filter.

        """
        if isinstance(value, datetime.datetime):
            return str(value.isoformat()) + "Z"

        return str(value)

    def _range_filter(self, gt=None, lt=None):
        """
        Return a RelatedSearchQuerySet range filter.

        """
        if not gt and not lt:
            return None

        return "{%s TO %s}" % tuple(
            ('"%s"' % self._filter_value(d) if d else "*") for d in (gt, lt)
        )

    def _get_param_date_range(self, param_name):
        """
        Return a datetime parameter as a SearchQuerySet range filter.

        """
        param_value = self.request.GET.get(param_name, "")
        gt = None
        lt = None

        if re.match(r"^([-\+]\d+)([ymwdhM])$", param_value):
            _kwarg = {
                "y": "years",
                "m": "months",
                "w": "weeks",
                "d": "days",
                "h": "hours",
                "M": "minutes",
            }[param_value[-1]]
            value = self.now + relativedelta(**{_kwarg: int(param_value[:-1])})
            if param_value.startswith("-"):
                gt = value
                lt = self.now
            else:
                gt = self.now
                lt = value

        return self._range_filter(gt, lt)

    def _get_param_list(self, param_name, coerce=lambda val: val, sep=None):
        """
        Return a list of values by combining values in a QueryDict list.

        """
        values = set()
        for param_value in self.request.GET.getlist(param_name):
            for value in param_value.split(sep):
                try:
                    values.add(coerce(value))
                except ValueError:
                    pass
        return list(values)


business_list = BusinessListView.as_view()


class BusinessInventoryReportView(View):
    def dispatch(self, request, *args, **kwargs):
        report = BusinessInventoryReport()
        response = report.generate_csv_response()
        return response


class BusinessReportView(View):
    def dispatch(self, request, *args, **kwargs):
        report = BusinessReport()
        response = report.generate_csv_response()
        return response


class UnapprovedListingsView(View):
    def dispatch(self, request, *args, **kwargs):
        report = UnapprovedListings()
        response = report.generate_csv_response()
        return response


business_inventory_report_view = cache_page(60 * 60 * 2)(
    BusinessInventoryReportView.as_view()
)
business_report_view = BusinessReportView.as_view()
unapproved_listings_view = UnapprovedListingsView.as_view()


class BusinessPermissionRequiredMixin:
    @method_decorator(org_required)
    @method_decorator(login_required)
    def dispatch(self, request, *args, **kwargs):
        business_id = kwargs.get("business_id")
        if business_id:
            if (
                not request.user.is_superuser
                and not request.user.groups.filter(
                    name__in=["Fulfilment Managers"]
                ).exists()
            ):
                business_for_orgs = Business.orgs_manager.for_user()
            else:
                business_for_orgs = Business.objects.all()
            self.business = get_object_or_404(
                business_for_orgs, id=business_id
            )
        else:
            return redirect("business_list")
        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, post_data=None, **kwargs):
        context = super().get_context_data(**kwargs)
        context["business"] = self.business
        return context


class BusinessFeatureAdFormViewMixin:
    template_name = "business/manage/business_feature_ad_form.html"
    form_class = BusinessFeatureAdForm

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["request"] = self.request
        kwargs["business"] = self.business
        return kwargs

    def form_valid(self, form):
        if form.is_valid():
            form.save()
            info(self.request, "Business feature ad saved")
            return redirect("business_change", self.business.id)


def send_welcome_email(business):
    try:
        if not business.listing_mastheads.all():
            return  # The given business must contain at least masthead
    except ValueError:
        # TODO: fix this. Catch ValueError on M2M field needs to have a value
        # for field 'id' before this many-to-many relationship can be used.
        return
    sites = get_sites()
    domain = sites.get(business.listing_mastheads.all()[0].site_id).get(
        "domain"
    )
    listing_url = f"{domain}/business/{business.category.slug}/{slugify(business.name)}/{business.id}/"
    ctxt = {
        "contact_name": business.contact_name,
        "listing_url": listing_url,
        "terms_conditions_url": settings.TERMS_CONDITIONS_URL,
        "privacy_policy_url": settings.PRIVACY_POLICY_URL,
    }
    if not domain:
        return  # No point in emailing them without their ad is live on a given site.

    html_message = get_template(
        "emails/selfservice_free_activation.html"
    ).render({"ctxt": ctxt})
    text_message = get_template(
        "emails/selfservice_free_activation_text.html"
    ).render({"ctxt": ctxt})

    mail_from = settings.NOREPLY_NEWSNOW_FROM_EMAIL
    mail_to = (business.email,)

    send_mail(
        "Welcome to Business Listings",
        text_message,
        mail_from,
        mail_to,
        fail_silently=True,
        html_message=html_message,
    )

    business.welcome_email_sent = True
    business.save()


@org_required
@login_required
def business_form(request, business_id=None):
    business = None
    if business_id:
        if (
            request.user.is_superuser
            or request.user.groups.filter(
                name__in=["Fulfilment Managers"]
            ).exists()
        ):
            business_for_orgs = Business.objects.all()
        else:
            business_for_orgs = Business.orgs_manager.for_user()
        business = get_object_or_404(business_for_orgs, id=business_id)
    if request.POST.get("_d"):
        if business.account_type == 2:
            # Clear ESOV memcache
            cache.delete_many(
                [
                    "site_%d_biz_list" % site_id
                    for site_id in business.mastheads
                ]
            )
        business.delete()
        info(request, "Business deleted")
        return redirect("business_list")
    BusinessFormset = inlineformset_factory(
        Business,
        Image,
        fields=("image",),
        extra=3,
    )
    OpeningHoursFormset = inlineformset_factory(
        Business,
        OpeningHours,
        fields=("weekday", "from_hour", "to_hour"),
        max_num=7,
        extra=1,
        widgets={
            "from_hour": forms.TimeInput(
                format="%I:%M %p", attrs={"class": "opening_time_select"}
            ),
            "to_hour": forms.TimeInput(
                format="%I:%M %p", attrs={"class": "opening_time_select"}
            ),
        },
    )
    post = request.POST or None
    files = request.FILES or None
    form = BusinessForm(post, files, instance=business)
    formset = BusinessFormset(post, files, instance=business)
    openinghours_formset = OpeningHoursFormset(post, instance=business)
    if request.method == "POST":
        if (
            form.is_valid()
            and formset.is_valid()
            and openinghours_formset.is_valid()
        ):
            business = form.save(commit=False)
            if not business.id:
                business.created_by = request.user

            business.updated_by = request.user
            business.save()
            formset.instance = business
            formset.save()
            openinghours_formset.instance = business
            openinghours_formset.save()
            form.save_m2m()
            if business.account_type == Business.ACCOUNT_IMPACT:
                # Clear ESOV memcache
                cache.delete_many(
                    [
                        "site_%d_biz_list" % site_id
                        for site_id in business.mastheads
                    ]
                )
                # HACK: Need extra save() for haystack to update index
                business.save()

            if (
                business.account_type == Business.ACCOUNT_LISTING
                and not business.welcome_email_sent
            ):
                send_welcome_email(business)

            info(request, "Business saved")
            return redirect("business_change", business.id)
        error(request, "Please correct the errors below")
    context = {
        "business_form": form,
        "business_formset": formset,
        "business_hours_formset": openinghours_formset,
        "editing": business is not None,
        "business": business,
    }
    return render(request, "business/manage/business_form.html", context)


class BusinessFeatureAdCreateView(  # type: ignore[misc]
    BusinessPermissionRequiredMixin, BusinessFeatureAdFormViewMixin, CreateView
):
    pass


business_feature_ad_create_view = BusinessFeatureAdCreateView.as_view()


class BusinessFeatureAdUpdateView(  # type: ignore[misc]
    BusinessPermissionRequiredMixin, BusinessFeatureAdFormViewMixin, UpdateView
):
    def get_object(self, queryset=None):
        return get_object_or_404(BusinessFeatureAd, id=self.kwargs["pk"])


business_feature_ad_update_view = BusinessFeatureAdUpdateView.as_view()


class BusinessFeatureAdListView(BusinessPermissionRequiredMixin, ListView):
    paginate_by = 20
    template_name = "business/manage/business_feature_ad_list.html"

    def get_queryset(self):
        qs = BusinessFeatureAd.objects.filter(
            business__id=self.kwargs["business_id"]
        )

        q = self.request.GET.get("q")

        if q:
            qs = qs.filter(term_to_query(q, ("ad_title", "ad_copy")))

        return qs

    def get_context_data(self, post_data=None, **kwargs):
        context_data = super().get_context_data(post_data, **kwargs)

        context_data["q"] = self.request.GET.get("q", "")

        return context_data


business_feature_ad_list_view = BusinessFeatureAdListView.as_view()


@any_permission_required(
    "business.add_businesscategory",
    "business.change_businesscategory",
    "business.delete_businesscategory",
)
@login_required
def category_list(request):
    categories = BusinessCategory.objects.all()
    query = request.GET.get("q")
    if query:
        categories = categories.filter(name__icontains=query)
    context = {"request": request, "categories": categories}
    return render(request, "business/manage/category_list.html", context)


@any_permission_required(
    "business.add_businesscategory",
    "business.change_businesscategory",
    "business.delete_businesscategory",
)
@login_required
def category_form(request, category_id=None):
    category = None
    if category_id:
        category = get_object_or_404(BusinessCategory, id=category_id)

    if request.POST.get("_d"):
        category.delete()
        info(request, "Business Category deleted")
        return redirect("category_list")

    post = request.POST or None
    files = request.FILES or None
    form = BusinessCategoryForm(post, files, instance=category)

    if request.method == "POST":
        if form.is_valid():
            category = form.save(commit=False)
            if not category.id:
                category.created_by = request.user
            category.updated_by = request.user
            category.save()

            info(request, "Business Category saved")
            return redirect("category_list")
        error(request, "Please correct the errors below")

    context = {
        "request": request,
        "category_form": form,
        "editing": category is not None,
        "category": category,
    }
    return render(request, "business/manage/category_form.html", context)
