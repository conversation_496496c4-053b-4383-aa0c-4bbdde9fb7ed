from django import forms
from django.core.files.images import get_image_dimensions
from django.forms import SelectDateWidget
from django.utils.translation import gettext, gettext_lazy as _

from longbeach.business.forms import BusinessForm as BaseBusinessForm
from longbeach.business.manage.utils import get_sites, get_tags
from longbeach.business.models import (
    Business,
    BusinessCategory,
    BusinessFeatureAd,
    BusinessMasthead,
)


class BPImageFileInput(forms.ClearableFileInput):
    clear_checkbox_label = _("Delete")  # type: ignore[assignment]
    template_with_clear = "<br />%(clear_checkbox_label)s:  %(clear)s<br>"
    template_with_initial = "<br />%(initial_text)s: %(initial)s<br /> %(clear_template)s<br />%(input_text)s: %(input)s"


class BusinessForm(BaseBusinessForm):
    logo = forms.ImageField(
        widget=BPImageFileInput,
        help_text="Only supports square images",
        required=False,
    )
    hero = forms.ImageField(widget=BPImageFileInput, required=False)
    meta_title = forms.CharField(label="SEO title", required=False)
    meta_description = forms.CharField(
        label="SEO description", required=False, widget=forms.Textarea
    )
    listing_start = Business._meta.get_field("listing_start").formfield(
        input_formats=["%d/%m/%Y"],
        help_text=_("Start date of listing in dd/mm/yyyy format."),
        widget=forms.DateInput(format="%d/%m/%Y"),
    )
    listing_end = Business._meta.get_field("listing_end").formfield(
        input_formats=["%d/%m/%Y"],
        help_text=_("End date of listing in dd/mm/yyyy format."),
        widget=forms.DateInput(format="%d/%m/%Y"),
    )

    def clean_logo(self):
        # Validate image is square.
        logo = self.cleaned_data["logo"]
        if "logo" in self.changed_data:
            width, height = get_image_dimensions(logo)
            if width != height:
                self._errors["logo"] = self.error_class(
                    [
                        _(
                            "Image %s uploaded is not a square aspect ratio."
                            % logo
                        )
                    ]
                )
        return logo


class BusinessCategoryForm(forms.ModelForm):
    class Meta:
        model = BusinessCategory
        fields = "__all__"


class BusinessFeatureAdForm(forms.ModelForm):
    publication_date = forms.DateField(
        widget=SelectDateWidget(), input_formats=["%d/%m/%Y"]
    )
    mastheads = forms.MultipleChoiceField(
        widget=forms.SelectMultiple(
            attrs={"data-placeholder": "Select Masthead(s)"}
        ),
        choices=[],
    )
    tag = forms.ChoiceField(choices=[])

    class Meta:
        model = BusinessFeatureAd
        fields = (
            "tag",
            "ad_title",
            "ad_copy",
            "ad_image",
            "ad_contact_me",
            "publication_date",
        )

    def __init__(self, *args, **kwargs):
        self.request = kwargs.pop("request", None)
        self.business = kwargs.pop("business", None)
        super().__init__(*args, **kwargs)
        try:
            self.fields["ad_contact_me"].initial = True
        except KeyError:
            pass
        if self.instance.pk:
            # NOTE: Strange behaviour, passing the queryset as is doesnt behave properly
            # with the form field. Instead, we'll map the values outside to ensure we
            # return a list.
            mastheads = list(
                map(
                    int,
                    self.instance.mastheads.values_list("site_id", flat=True),
                )
            )
            self.fields["mastheads"].initial = mastheads

        TAG_CHOICES = [(tag_name, tag_name) for tag_name in get_tags()]
        TAG_CHOICES.insert(0, ("", "----"))
        self.fields["tag"].choices = TAG_CHOICES

        MASTHEAD_CHOICES = [(k, v["name"]) for k, v in get_sites().items()]
        self.fields["mastheads"].choices = MASTHEAD_CHOICES

    def clean(self):
        cleaned_data = super().clean()
        tag = cleaned_data.get("tag")
        ad_title = cleaned_data.get("ad_title")
        ad_copy = cleaned_data.get("ad_copy")
        if not tag:
            self._errors["tag"] = self.error_class(
                [
                    gettext(
                        "Tag is required in order for an ad to be published for a feature."
                    )
                ]
            )
        if not ad_copy:
            self._errors["ad_copy"] = self.error_class(
                [
                    gettext(
                        "Body Copy is required in order for an ad to be published for a feature."
                    )
                ]
            )
        if not ad_title:
            self._errors["ad_title"] = self.error_class(
                [
                    gettext(
                        "Title is required in order for an ad to be published for a feature."
                    )
                ]
            )
        return cleaned_data

    def save(self):
        instance = super().save(commit=False)
        self.business.updated_by = self.request.user
        self.business.save()
        instance.business = self.business
        if instance.pk is None:
            instance.created_by = self.request.user
        if not instance.ad_image and instance.business.logo:
            instance.ad_image = instance.business.logo
        instance.updated_by = self.request.user
        instance.save()
        instance.mastheads.clear()
        mastheads = self.cleaned_data.get("mastheads")
        for site_id in mastheads:
            masthead, created = BusinessMasthead.objects.get_or_create(
                site_id=site_id
            )
            instance.mastheads.add(masthead)
        return instance
