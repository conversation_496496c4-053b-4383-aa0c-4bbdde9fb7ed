from django.urls import re_path

from longbeach.business.manage.views import (
    business_feature_ad_create_view,
    business_feature_ad_list_view,
    business_feature_ad_update_view,
    business_form,
    business_inventory_report_view,
    business_list,
    business_report_view,
    category_form,
    category_list,
    unapproved_listings_view,
)

urlpatterns = [
    re_path(r"^$", business_list, name="business_list"),
    re_path(r"^business/$", business_form, name="business_add"),
    re_path(
        r"^business/(?P<business_id>[0-9]+)/$",
        business_form,
        name="business_change",
    ),
    re_path(
        r"^business/(?P<business_id>[0-9]+)/feature-ad/list/$",
        business_feature_ad_list_view,
        name="business_feature_ad_list",
    ),
    re_path(
        r"^business/(?P<business_id>[0-9]+)/feature-ad/create/$",
        business_feature_ad_create_view,
        name="business_feature_ad_create",
    ),
    re_path(
        r"^business/(?P<business_id>[0-9]+)/feature-ad/update/(?P<pk>[0-9]+)/$",
        business_feature_ad_update_view,
        name="business_feature_ad_update",
    ),
    re_path(r"^category/$", category_list, name="category_list"),
    re_path(r"^category/add/$", category_form, name="category_add"),
    re_path(
        r"^category/(?P<category_id>[0-9]+)/$",
        category_form,
        name="category_change",
    ),
    re_path(
        r"^report/inventory/$",
        business_inventory_report_view,
        name="business_inventory_report",
    ),
    re_path(
        r"^report/business/$", business_report_view, name="business_report"
    ),
    re_path(
        r"^report/unapproved_listings/$",
        unapproved_listings_view,
        name="unapproved_listings",
    ),
]
