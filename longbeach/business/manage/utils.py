from django.conf import settings
from django.core.cache import cache
from django.core.exceptions import ObjectDoesNotExist
from oauthsome.oauth_client.requests import oauth_session
from requests import RequestException

site_fields = {
    "id",
    "businessfeature_enabled",
    "domain",
    "location_point",
    "location_radius",
    "name",
    "visible",
}


def get_sites():
    """Retrieve a list of sites from Suzuka."""
    sites = cache.get("sites")

    if not sites:
        sites = {}
        try:
            resp = oauth_session("suzuka").get(
                "%s/site/?limit=0&fields=%s"
                % (
                    settings.SUZUKA_API_HOST,
                    ",".join(site_fields),
                )
            )
            resp.raise_for_status()
            json = resp.json
            if callable(json):
                json = json()
            site_objects = json["objects"]
        except (
            RequestException,
            ObjectDoesNotExist,
            TypeError,
            KeyError,
        ) as e:
            print(e)
            # Silently fail with an empty list of sites.
            return sites

        for site in site_objects:
            if (
                site["visible"]
                and site["businessfeature_enabled"]
                and (
                    site["location_point"] is not None
                    and site["location_radius"] is not None
                    and
                    # TODO: unset businessfeature_enabled for archived/test sites
                    "ZArchive" not in site["name"]
                )
            ):
                sites[site["id"]] = {
                    "id": site["id"],
                    "name": site["name"],
                    "domain": site["domain"],
                    "latitude": site["location_point"][0],
                    "longitude": site["location_point"][1],
                    "radius": site["location_radius"],
                }
        cache.set("sites", sites, 3600)
    return sites


def get_tags():
    tags = {}
    try:
        resp = oauth_session("silverstone").get(
            "%s/tag/?limit=0&name__startswith=bf-title"
            % settings.SILVERSTONE_API_HOST
        )
        resp.raise_for_status()
        json = resp.json
        if callable(json):
            json = json()
        tag_objects = json["objects"]
    except (RequestException, ObjectDoesNotExist, TypeError, KeyError) as e:
        print(e)
        return tags
    tags = [tag["name"] for tag in tag_objects]
    return tags
