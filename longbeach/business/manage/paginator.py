from django.core.paginator import EmptyPage, Page, PageNotAnInteger, Paginator


class HaystackPaginator(Paginator):
    """
    Paginator for `haystack.query.SearchQuerySet` results.

    """

    def page(self, number):
        """
        Return a Page object for the given 1-based page number.

        Eliminates an extra request to the backend by obtaining total
        result count from the page request.

        """
        try:
            number = int(number)
        except (TypeError, ValueError):
            raise PageNotAnInteger("That page number is not an integer")
        if number < 1:
            raise EmptyPage("That page number is less than 1")
        bottom = (number - 1) * self.per_page
        top = bottom + self.per_page + self.orphans

        page_objects = self.object_list[bottom:top]
        if top < self.count:
            page_objects = page_objects[: self.per_page]
        elif number > self.num_pages:
            if number == 1 and self.allow_empty_first_page:
                pass
            else:
                raise EmptyPage("That page contains no results")

        return Page(page_objects, number, self)
