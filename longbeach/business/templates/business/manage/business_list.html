{% extends "base_no_sidebar.html" %}
{% load orgs_tags %}
{% load search_tags util_tags %}
{% load service_tags %}
{% load humanize %}
{% load static %}

{% block head_styles %}
{{ block.super }}
<link href="{% get_static_prefix %}business/style.css" media="all" rel="stylesheet" type="text/css" />
{% endblock %}
{% block messages %}{% endblock %}

{% block body_extra %}
{{ block.super }}
<script src="{% get_static_prefix %}business/search.js"></script>

{% endblock %}

{% block base_content %}

<div class="container">
    <div id="business-list-menu-container" data-spy="affix">
        <div id="business-list-menu" class="container {% if is_filtered %}business-list-filtered{% endif %}">
            <form id="business-search-form" class="form-search business-list-menu-item">
                <input type="text" name="q"{% if q %} value="{{ q }}"{% endif %} id="business-search-query" class="search-query" placeholder="Search" autocomplete="off" spellcheck="false" />
                {% for param in request.GET.items %}{% if param.0 != "q" and param.0 != "page" %}
                    <input type="hidden" name="{{ param.0 }}" value="{{ param.1 }}" />
                {% endif %}{% endfor %}
                <span class="search-icon">
                    {% if q %}<a href="{% page_url delete q delete page delete order_by %}" class="close">x—</a>{% else %}<i class="icon-search"></i>{% endif %}
                </span>
            </form>
            {% if perms.business.add_business %}
            <div id="business-list-buttons" class="business-list-menu-item">
                <a href="{% url 'business_add' %}" class="btn btn-primary">Create Business Profile</a>
            </div>
                <div class="right novmargin">
                    <a href="{% url 'business_inventory_report' %}" class="btn btn-primary">Inventory Report</a>
                </div>&nbsp;
                <div class="right novmargin">
                    <a href="{% url 'business_report' %}" class="btn btn-primary">Business Report</a>
                </div>&nbsp;
                <div class="right novmargin">
                    <a href="{% url 'unapproved_listings' %}" class="btn btn-primary">Unapproved Listings</a>
            </div>&nbsp;
            {% endif %}
            <div>
                <a id="exportLink"></a>
            </div>
        </div>
    </div>
    <div id="business-list-submenu">
        <ul class="nav nav-pills" id="sub-filters">
            <li class="disabled"><a>Filters:</a></li>
            {% for filterName, filter in filters.items %}
              <li class="dropdown{% if filter.selected.value %} active{% endif %}">
                <a class="dropdown-toggle" data-toggle="dropdown" href="#">
                    {{ filter.selected.text }} <b class="caret"></b></a>
                <ul class="dropdown-menu">
                    {% for value, text in filter.options.items %}
                    <li{% if value == filter.selected.value %} class="option-active"{% endif %}><a href="?{{ filterName }}={{ value|default_if_none:'' }}">{{ text }}</a></li>
                    {% endfor %}
                </ul>
              </li>
            {% endfor %}
            {% if parsed_filters.category %}
            <li class="dropdown dropdown-filtered active" id="category-control">
                <a class="dropdown-toggle" data-toggle="dropdown" href="#">{% if parsed_filters.category|length == 1 %}{{ parsed_filters.category.0 }}{% else %}Multiple categories{% endif %} <b class="caret"></b></a>
            {% else %}
            <li class="dropdown dropdown-filtered" id="category-control">
                <a class="dropdown-toggle" data-toggle="dropdown" href="#">Category <b class="caret"></b></a>
            {% endif %}
                <ul class="dropdown-menu">
                    <li><input type="text" placeholder="Find categories" /></li>
                    <li>
                        <ul class="dropdown-scroll">
                    <li data-option="All Categories"><a href="?category=">All Categories</a></li>
                {% for category, count in categories %}
                    <li data-option="{{ category.name }}"{% if category in parsed_filters.category %} data-selected="selected"{% endif %}>
                        <a href="?category={{ category.pk }}">{{ category.name }} <span class="option-count">({{ count|precision:3|intcomma }})</span></a>
                    </li>
                {% endfor %}
                        </ul>
                    </li>
                    {% if perms.business.add_businesscategory or perms.business.change_businesscategory or perms.business.delete_businesscategory %}
                    <li class="divider"></li>
                    <li><a href="{% url 'category_list' %}">Edit categories</a></li>
                    {% endif %}
                </ul>
            </li>
            {% if parsed_filters.tags %}
            <li class="dropdown dropdown-filtered active" id="tags-control">
                <a class="dropdown-toggle" data-toggle="dropdown" href="#">{% if parsed_filters.tags|length == 1 %}{{ parsed_filters.tags.0 }}{% else %}Multiple tags{% endif %} <b class="caret"></b></a>
            {% else %}
            <li class="dropdown dropdown-filtered" id="tags-control">
                <a class="dropdown-toggle" data-toggle="dropdown" href="#">Tags <b class="caret"></b></a>
            {% endif %}
                <ul class="dropdown-menu">
                    <li><input type="text" placeholder="Find tags" /></li>
                    <li>
                        <ul class="dropdown-scroll">
                    <li data-option="All Tags"><a href="?tags=ALL">All Tags</a></li>
                {% for tag, count in tags %}
                    <li data-option="{{ tag }}"{% if tag in parsed_filters.tags %} data-selected="selected"{% endif %}>
                        <a href="?tags={{ tag|urlencode }}">{{ tag }} <span class="option-count">({{ count|precision:3|intcomma }})</span></a>
                    </li>
                {% endfor %}
                        </ul>
                    </li>
                </ul>
            </li>
            {% if sites %}
            {% if parsed_filters.site %}
            <li class="dropdown dropdown-filtered active" id="site-control">
                <a class="dropdown-toggle" data-toggle="dropdown" href="#">{% if parsed_filters.site|length == 1 %}{{ parsed_filters.site.0.name }}{% else %}Multiple sites{% endif %} <b class="caret"></b></a>
            {% else %}
            <li class="dropdown dropdown-filtered" id="site-control">
                <a class="dropdown-toggle" data-toggle="dropdown" href="#">Site (by location) <b class="caret"></b></a>
            {% endif %}
                <ul class="dropdown-menu">
                    <li><input type="text" placeholder="Find sites" /></li>
                    <li>
                        <ul class="dropdown-scroll">
                    <li data-option="All Sites"><a href="?site=ALL">All Sites</a></li>
                {% for id, site in sites.items %}
                    <li data-option="{{ site.name }}"{% if site in parsed_filters.site %} data-selected="selected"{% endif %}>
                        <a href="?site={{ id }}">{{ site.name }}</a>
                    </li>
                {% endfor %}
                        </ul>
                    </li>
                </ul>
            </li>
            {% endif %}
            {% if parsed_filters.location %}
            <li class="dropdown active">
                <a class="dropdown-toggle" data-toggle="dropdown" href="#">Within {{ parsed_filters.location.2 }} km of ({{ parsed_filters.location.0 }}, {{ parsed_filters.location.1 }}) <b class="caret"></b></a>
                <ul class="dropdown-menu">
                    <li><a href="?location=ALL">Anywhere</a></li>
                </ul>
            </li>
            {% endif %}
            {% if order_field_choices|length > 1 %}
            <li class="separator"><a></a></li>
            <li class="disabled"><a>Sort:</a></li>
            <li class="dropdown">
                <a class="dropdown-toggle" data-toggle="dropdown" href="#">
                    {{ order_field_label }} <b class="caret"></b></a>
                <ul class="dropdown-menu">
                    {% for field, label in order_field_choices.items %}
                    <li{% if field == order_field %} class="option-active"{% endif %}><a href="?order_by={{ field }}">{{ label }}</a></li>
                    {% endfor %}
                </ul>
            </li>
            {% endif %}
        </ul>

        {% if paginator.count %}
        <div id="business-list-submenu-count">
            {% if is_paginated %}Page {{ page_obj.number|intcomma }} of {% if paginator.count >= 1000 %}about {% endif %}{% endif %}{{ paginator.count|precision:3|intcomma }} business{{ paginator.count|pluralize:"es" }}
            <div class="btn-group business-list-pagination-arrows">
                <a class="btn{% if not page_obj.has_previous %} disabled{% endif %}" href="{% if page_obj.previous_page_number > 1 %}{% page_url page=page_obj.previous_page_number %}{% else %}{% page_url delete page %}{% endif %}">◀</a>
                <a class="btn{% if not page_obj.has_next %} disabled{% endif %}" href="{% if page_obj.has_next %}{% page_url page=page_obj.next_page_number %}{% else %}{% page_url %}{% endif %}">►</a>
            </div>
        </div>
        {% endif %}
    </div>

{% if messages %}
<div class="container">
    {% include "pitcrews/includes/messages.html" %}
</div>
{% endif %}

{% if object_list %}
<table class="table" id="business-list">
    <thead>
        <tr>
            <th>Business</th>
            <th>Address</th>
            {% if 'location' in parsed_filters or 'site' in parsed_filters %}
            <th class="col-numeric">Distance</th>
            {% endif %}
            <th class="col-centered">Map</th>
            <th>Category</th>
            <th class="col-centered">Account Type</th>
            {% if perms.business.can_generate_report %}<th class="col-centered">Report</th>{% endif %}
        </tr>
    </thead>
    <tbody>
    {% for result in object_list %}
        {% with business=result.object %}
            {% if business.pk %}
                <tr>
                    <td><a href="{% url 'business_change' business.id %}">{{ business }}</a></td>
                    <td>{{ business.address }}</td>
                    {% if 'location' in parsed_filters or 'site' in parsed_filters %}
                    <td class="col-numeric">{{ result.distance|distanceformat }}</td>
                    {% endif %}
                    <td class="col-centered">{% if business.is_resolved %}<i class="icon-ok"></i>{% else %}<i class="icon-remove"></i>{% endif %}</td>
                    <td>{{ business.category }}</td>
                    <td class="col-centered"><span class="label">{{ business.account_type|account_type_name }}</span></td>
                    {% if perms.business.can_generate_report %}
                        <td class="col-centered"><i class="icon-download-alt" businessID={{business.id}} businessName="{{business|escape|truncatechars:30}}"></i></td>
                    {% endif %}
                </tr>
            {% else %}
            <tr>
                <td colspan="5">Business not available.</td>
            </tr>
        {% endif %}
        {% endwith %}
    {% endfor %}
    </tbody>
</table>
{% else %}
        {% if is_filtered %}
        <div class="alert alert-warning">
            <strong>No results found.</strong> Try adjusting your search query.
        </div>
        {% else %}
        <div class="alert alert-info">
            <strong>Welcome to Longbeach.</strong> {% if organizations|length == 0 %}You aren't a member of any organizations.{% else %}There aren't any business profiles from your organizations.{% endif %} Open your <a href="//{% service_link 'monza' %}/manage/"><strong class="alert-info">account profile</strong></a> to join an{% if organizations|length > 0 %}other{% endif %} organisation{% if organizations|length > 0 %} or <a href="{% url 'business_add' %}"><strong class="alert-info">add a business profile</strong></a> now{% endif %}.
        </div>
        {% endif %}
{% endif %}

    {% if is_paginated %}
    <div class="pagination pagination-centered">
        <ul>
            {% if page_obj.has_previous %}
            <li>
                <a href="{% if page_obj.previous_page_number > 1 %}{% page_url page=page_obj.previous_page_number %}{% else %}{% page_url delete page %}{% endif %}" class="prev">◄</a>
            </li>
            {% endif %}
            {% paginator_page_list as pages %}
            {% for page in pages %}
            <li{% if page.is_current %} class="active"{% endif %}>
                <a href="{{ page.url }}" class="page">{{ page.number }}</a>
            </li>
            {% endfor %}
            {% if page_obj.has_next %}
            <li>
                <a href="{% page_url page=page_obj.next_page_number %}" class="next">►</a>
            </li>
            {% endif %}
        </ul>
    </div>
    {% endif %}
</div>

{% endblock %}
