{% extends "base.html" %}
{% load orgs_tags %}

{% block content_title %}
<div style="float:right; margin-top:5px;">
    <form style="display:inline;">
        <input type="text" name="q" value="{{ request.GET.q }}" style="margin-top:8px;">
        <input type="submit" value="Search" class="btn" style="margin-right:8px;">
    </form>
    {% if perms.business.add_businesscategory %}
    <a class="btn btn-primary" href="{% url 'category_add' %}">
        Add Business Category
    </a>
    {% endif %}
</div>
<h2>Business Categories</h2>
{% endblock %}

{# side bar global navigation #}
{% block navigation %}
<ul class="nav nav-list">
    <li class="nav-header">Business</li>
    <li><a href="{% url 'business_list' %}"><i class="icon-briefcase"></i> Profiles</a></li>
    {% if perms.business.add_businesscategory or perms.business.change_businesscategory or perms.business.delete_businesscategory %}
    <li class="active"><a href="{% url 'category_list' %}"><i class="icon-folder-open"></i> Categories</a></li>
    {% endif %}
</ul>
{% endblock %}


{% block content %}

{% if categories %}
<table class="table table-striped" style="margin-top:40px;">
    <tbody>
    {% for category in categories %}
    <tr>
        <td>{{ category }}</td>
        {% if perms.business.change_businesscategory %}
        <td class="actions">
            <a class="btn btn-primary" href="{% url 'category_change' category.id %}">Edit</a>
        </td>
        {% endif %}
    </tr>
    {% endfor %}
    </tbody>
</table>
{% else %}
<p>No Business Categories</p>
{% endif %}

{% endblock %}

