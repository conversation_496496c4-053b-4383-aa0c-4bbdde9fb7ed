{% extends 'base.html' %}
{% load search_tags %}
{% load static %}
{% load image_tags %}
{% load util_tags %}
{% block content_title %}{% endblock %}
{% block body_extra %}
  {{ block.super }}
  <style type="text/css">{% include 'business/manage/includes/css/business_style.css' %}</style>
{% endblock %}
{% block content %}
<a class="btn btn-primary" href="{% url 'business_feature_ad_create' business.id  %}">
  <i class="icon-plus icon-white"></i>
  Create Business Feature Ad
</a>
<form action="{% url 'business_feature_ad_list' business.id %}" class="form-inline" style="margin-top: 1em">
  <input class="input-small" id="q" name="q" placeholder="Search business features" required style="margin-bottom:0 !important;" type="search" value="{{ q }}" />
  <button class="btn" type="submit">
    <i class="icon-search"></i>
    Search
  </button>

  {% if q %}
    <a href="{% url 'business_feature_ad_list' business.id %}">
      <i class="icon-chevron-left"></i>
      Show all
    </a>
  {% endif %}
</form>
<table class="table" id="business-list">
  <thead>
    <tr>
      <th>Business Feature Ad Title</th>
      <th>Tag</th>
      <th>Masthead(s)</th>
      <th>Publication Date</th>
      <th>Expiration Date</th>
    </tr>
  </thead>
  <tbody>
    {% for object in object_list %}
      <tr>
        <td><a href="{% url 'business_feature_ad_update' business.id object.id %}">{{ object.ad_title }}</a></td>
        <td>{{ object.tag }}</td>
        <td>{% get_masthead_names_for_feature_ad object %}</td>
        <td>{{ object.publication_date }}</td>
        <td>{{ object.expiration_date }}</td>
      </tr>
    {% empty %}
      There are no {% if q %}matching {% endif %}feature ads for this business.
    {% endfor %}
  </tbody>
</table>
{% if is_paginated %}
    <div class="pagination pagination-centered">
        <ul>
            {% if page_obj.has_previous %}
                <li>
                    <a href="{% if page_obj.previous_page_number > 1 %}{% page_url page=page_obj.previous_page_number %}{% else %}{% page_url delete page %}{% endif %}" class="prev">◄</a>
                </li>
            {% endif %}
            {% paginator_page_list as pages %}
            {% for page in pages %}
                <li{% if page.is_current %} class="active"{% endif %}>
                    <a href="{{ page.url }}" class="page">{{ page.number }}</a>
                </li>
            {% endfor %}
            {% if page_obj.has_next %}
                <li>
                    <a href="{% page_url page=page_obj.next_page_number %}" class="next">►</a>
                </li>
            {% endif %}
        </ul>
    </div>
{% endif %}
{% endblock %}
