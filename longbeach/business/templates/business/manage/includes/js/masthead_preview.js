$(function () {
    //Masthead Display
    function initMastheadDisplay(){
        function formatPhoneNumber(phoneNumber){
            function mobileAndLandlineFormat(phoneNumber){
                //add 0 in the front
                if (phoneNumber.indexOf("0") !== 0) {phoneNumber = "0" + phoneNumber;}
                  
                //mobile 0432221231
                if (phoneNumber.indexOf("04") === 0) {
                    phoneNumberFull = phoneNumber.replace(/(\d{3})(\d{3})(\d{4})/, '$1 $2 $3');
                } else {
                    //landline 0294532345
                    phoneNumberFull = phoneNumber.replace(/(\d{2})(\d{4})(\d{4})/, '$1 $2 $3');
                }
                
                return {
                    calling: phoneNumber,
                    short : phoneNumber.substring(0,4) + "...",
                    full : phoneNumberFull
                };     
            }
        
            function specialPhoneFormat(phoneNumber){
                // number like 1300123123 or 1800123123 or 1900123123
                if (phoneNumber.length === 10){
                    phoneNumberFull = phoneNumber.replace(/(\d{4})(\d{3})(\d{3})/, '$1 $2 $3');
                } else if (phoneNumber.length === 6){
                    // number like 132323 or 191212
                    phoneNumberFull = phoneNumber.replace(/(\d{2})(\d{2})(\d{2})/, '$1 $2 $3');
                } else {
                    phoneNumberFull = phoneNumber;
                }
        
                return {
                    calling: phoneNumber,
                    short : phoneNumber.substring(0,4) + "...",
                    full : phoneNumberFull
                };   
            }
        
            phoneNumber = String(phoneNumber);
            //remove +61
            phoneNumber = phoneNumber.replace(/[^\d-+]/g, "").replace(/\+61/g, "").trim();
            var phoneNumberShort = "";
            var phoneNumberFull = "";
        
            if (phoneNumber.indexOf("13") === 0 || phoneNumber.indexOf("18") === 0 || phoneNumber.indexOf("19") === 0){
                return specialPhoneFormat(phoneNumber);
            } else {
                return mobileAndLandlineFormat(phoneNumber);
            }
        }

        function mobileEnableShow(){
            $('.mobileEnable').show();
            $('.mobileDisable').hide();
        }

        function mobileDisableShow(){
            $('.mobileEnable').hide();
            $('.mobileDisable').show();
        }

        $(".businesspromo-text__phone").each(function(){
            var $this = $(this);
            var phoneNumberSpan = $this.find('.businesspromo-text-phonenumber');

            // add condition to avoid double set attribution
            if (typeof phoneNumberSpan.data("full") === "undefined") {
              var phoneNumber =  phoneNumberSpan.text();

              //format the phone number
              var phoneObj = formatPhoneNumber(phoneNumber);

              phoneNumberSpan.data("short", phoneObj.short);
              phoneNumberSpan.data("full", phoneObj.full);
    
              phoneNumberSpan.text(phoneObj.short);

              //show the phone div
              $this.show();
            }
        });

        $('.businesspromo-text__phone  > .businesspromo-text-show-more, .businesspromo-text-phonenumber').on("click", function(e){
            e.preventDefault()
            var $this = $(this);
            var phoneNumberSpan = $this.parent().find(".businesspromo-text-phonenumber")
            phoneNumberSpan.text(phoneNumberSpan.data("full"));
            $this.parent().find(".businesspromo-text-show-more").hide();
            return false;
        });

        $('#id_ad_contact_me').on("click", function(e){
            if ($("#id_telephone").val().trim() !== "" && $('#id_ad_contact_me').is(':checked')) {
                mobileEnableShow();
            } else {
                mobileDisableShow();     
            }
        });       

        if ($("#id_telephone").val().trim() !== "" && $('#id_ad_contact_me').is(':checked')) {
            mobileEnableShow();
        } else {
            mobileDisableShow();     
        }

    }

    function init(){
        initMastheadDisplay();
    }
    
    init();
}); 