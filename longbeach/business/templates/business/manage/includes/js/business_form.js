$(function () {
    function initDatepicker() {
        $('#id_listing_start').datepicker({todayHighlight: true});
        if ($('#id_listing_start').datepicker('getDate') === null) {
            $('#id_listing_start').datepicker('setDate', new Date());
        }
        $("#id_mastheads").chosen();
        $("#id_listing_mastheads").chosen();
    }

    //jquery timepicker widget initialisation
    function initTimeSelect(cls) {
        if (typeof cls === "undefined" || cls === null)
            cls = '.opening_time_select';
        $(cls).timepicker({
            timeFormat: 'hh:mm p',
            interval: 30,
            scrollbar: true,
            dynamic: true
        });
    }  

    function bindClickAddTemplate(btnClass, groupContainer, template, formTotalElement, invokeFn, predicateRemove) {
        $(btnClass).click(function (e) {
            //generic function to add new form elements on click.
            e.preventDefault();
            var count = $(groupContainer).children().length;
            var tmplMarkup = $(template).html();
            var compiledTmpl = tmplMarkup.replace(/__prefix__/g, count);
            $(groupContainer).append(compiledTmpl);
            if (invokeFn) { invokeFn() };
            // update form count
            $(formTotalElement).attr('value', count + 1);
            if (predicateRemove) {
                if (count + 1 >= predicateRemove) { $(this).remove() }
            }
        });
    }

    //Photogallery uploads
    function initPhotoGalleryListners(cls) {
        if (typeof cls === "undefined" || cls === null) 
            cls = '.gallery-image input[type="file"]';
        var inputs = document.querySelectorAll(cls);
        Array.prototype.forEach.call(inputs, function (input) {
            var label = input.nextElementSibling,
                labelVal = label.innerHTML;
            input.addEventListener('change', function (e) {
                var fileName = '';
                if (this.files && this.files.length > 1)
                    fileName = (this.getAttribute('data-multiple-caption') || '').replace('{count}', this.files.length);
                else
                    fileName = e.target.value.split('\\').pop();

                if (fileName)
                    label.querySelector('span').innerHTML = fileName;
                else
                    label.innerHTML = labelVal;
            });
        });
    }

    function initImpactSubtype() {
        var field_listing_start = $('.field_listing_start');
        $('#id_listing_start').css({"border-radius": "4px", "border": "1px solid #ccc"})
        function toggle() {
            var field_impact_subtype = $('.field_impact_subtype');
            if ($("input[name='account_type']:checked").val() === '2') {
                field_impact_subtype.show();
                field_listing_start.show();
            }
            else {
                field_impact_subtype.hide();
                field_listing_start.hide();
            }
        }

        $("input[name='account_type']").change(function () {
            toggle();
        });
        toggle();
    }

    function init(){

        initDatepicker();

        initImpactSubtype();

        initTimeSelect();

        bindClickAddTemplate('.add_bus_hours', '#opening_hours_container', '#openinghours-template', '#id_opening_hours-TOTAL_FORMS', initTimeSelect, 7);

        bindClickAddTemplate('.add-image', '#gallery_images_container', '#galleryimage-template', '#id_images-TOTAL_FORMS', initPhotoGalleryListners, null);

        initPhotoGalleryListners();
    }
    
    init();
}); 