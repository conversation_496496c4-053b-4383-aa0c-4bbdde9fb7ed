.page-header {padding-bottom: 0; margin: 0; border: 0;}
textarea,
input {
    width: 50% !important;
}

.image input,
input.btn {
    width: auto !important;
    margin-right: 20px;
}

.image {
    padding: 10px;
    margin: 10px 0;
    width: 98%;
}

.image input[type="file"] {
    line-height: 1;
    margin-top: 7px;
}

.image input[type="number"] {
    width: 35px !important;
}

.gallery-image.add-image {
    cursor: pointer;
}

.gallery-image label {
    display: inline-block;
}

.gallery-image {
    display: inline-block;
    line-height: 100px;
    text-align: center;
    vertical-align: middle;
    margin: 0 10px 10px 0;
    position: relative;
    border: 1px solid #f6f6f6;
    width: 100px;
    height: 100px;
    overflow: hidden;
    word-wrap: break-word;
}

.gallery-image img {
    display: inline-block;
    position: relative;
    z-index: 50;
    border: 0;
}

.gallery-image .remove {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 100;
    width: 20px;
    height: 20px;
    line-height: 20px;
    text-align: center;
}

.gallery-image input[type="file"] {
    position: absolute;
    left: -99999px
}

.hide {
    visibility: hidden;
}

.errorlist {
    color: #f00;
    font-weight: bold;
    list-style-type: none;
    margin-left: 0;
    font-size: 12px;
}

.image .errorlist {
    display: none;
}

.image .errorlist:first-child {
    display: inline;
}

.right {
    float: right;
}

.novmargin {
    margin: 0 10px;
}

.grey {
    color: grey;
}

.red {
    color: red;
}

.opacity70 {
    opacity: 0.7;
}

form {
    position: relative;
}

.form-inline label {
    width: 100px;
}

.form-inline textarea,
.form-inline select,
.form-inline input,
.form-inline .help-inline,
.form-inline .uneditable-input,
.form-inline .input-prepend,
.form-inline .input-append {
    margin-bottom: 1em !important;
}

.form-inline .help-block {
    margin-left: 105px;
    font-style: italic;
}

#sidenav.shortbox {
    width: 150px;
    position: absolute;
    right: 0;
}

.alignr {
    text-align: right;
}

.opening_day input,
input.btn {
    width: auto !important;
    margin-right: 20px;
}

.opening_day select {
    width: 150px;
}

.opening_day {
    padding: 0 0 10px;
    margin: 10px 0;
    width: 98%;
}

.opening_day input[type="text"] {
    width: 100px !important;
    margin-right: 0;
}

.opening_day .errorlist {
    display: none;
}

.opening_day .errorlist:first-child {
    display: inline;
}

#id_account_type li input {
    width: auto !important;
    margin-right: 1em;
}

.previewbox {
    display: block;
    overflow: hidden;
    width: 350px;
    position: absolute;
    background: white;
    top: 100px;
    right: -50px;
}

@media (max-width: 1200px) {
    .previewbox {
        position: static;
        margin: 0 auto;
        margin-bottom: 9.5px;
    }
}

.section-wrapper {
    width: 300px;
    padding-top: 10px;
}

.businesspromo__heading {
    text-align: left !important;
    border-bottom: 1px solid #ddd;
    background: #f2f6f9;
    padding: 3px 5px;
    font-size: 21px;
    margin-bottom: 5px;
}

.businesspromo-block,
.businesspromo-block:visited,
.businesspromo-block:active {
    overflow: visible !important;
    text-align: left !important;
    box-sizing: border-box;
    position: relative;
    color: #000;
    line-height: 1rem;
    height: 110px !important;
    margin-left: 10px !important;
    width: 300px !important;
    border-radius: 4px;
    display: block;
    background: #fff;
    margin: 15px 0;
    -webkit-box-shadow: 0px 1px 2px 1px #d1d1d1;
    -moz-box-shadow: 0px 1px 2px 1px #d1d1d1;
    box-shadow: 0px 1px 2px 1px #d1d1d1;
}

.businesspromo-block__image {
    width: 75px;
    height: 75px;
    position: absolute;
    top: 15px;
    left: 22px;
    overflow: hidden;
}

.businesspromo-text {
    line-height: 1rem;
    display: inline-block;
    padding-left: 110px;
    margin-top: 16px;
}

.businesspromo-text__title {
    font-family: NimbusSanNovCon-Bol, Helvetica, Arial, sans-serif;
    color: #333;
    font-size: 1.1rem;
    line-height: 1rem;
    clear: both;
    font-size: 1.1rem;
    line-height: 1.3rem;
    white-space: nowrap;
    clear: both;
    color: #0235a0;
    margin-bottom: 5px;
    font-weight: bold;
}

.businesspromo-text__summary {
    font-size: 0.8rem;
    line-height: 1rem;
    font-family: Helvetica, Arial, sans-serif;
}

.businesspromo h2,
h3,
h4,
h5,
h6 {
    font-family: NimbusSanNovCon-Bol, Helvetica, Arial, sans-serif;
    font-style: normal;
    font-weight: normal;
    color: #333;
    margin: 0;
}

.add_bus_hours {
    display: inline-block;
    padding: 3px 5px;
    background: whitesmoke;
    border: 1px solid #333;
    cursor: pointer;
    margin-bottom: 15px;
}

.opening_hours_divider {
    padding: 0 10px;
}