{% extends "base.html" %}

{% block content_title %}
<h2>{% if editing %}Change{% else %}Add{% endif %} Business Category</h2>
{% endblock %}

{% block content %}
<style>
textarea, input {width:50% !important;}
.image input, input.btn {width:auto !important; margin-right:20px;}
.image {background:#f6f6f6; padding:10px; margin:10px 0;}
.errorlist {color:#f00; font-weight:bold; list-style-type:none; margin-left:0; font-size:14px;}
</style>
<form method="POST" style="margin-top:15px;" enctype="multipart/form-data">
    {% csrf_token %}

    {% for field in category_form %}
        <p>
        {% if field.errors %}
        <span class="errorlist">
            {% for e in field.errors %}
            {% if not forloop.first %} / {% endif %}{{ e }}
            {% endfor %}
        </span><br>
        {% endif %}

        {% if not field.is_hidden %}{{ field.label }}:<br> {% endif %}
        {% if editing and field.name == "image" and category.image %}
        <img src="{{ category.image.url }}">
        {% endif %}<br>
        {{ field }}
    {% endfor %}
    </p>


    <div class="form-actions">
        <input type="submit" class="btn btn-primary" value="Save category" name="_s">
        {% if editing and perms.business.delete_businesscategory %}
            {% if category.businesses.count > 0 %}
                <button class="btn btn-danger"
                    onclick="alert('There are still businesses with this category. Please reassign the category for these businesses before deleting.'); return false;">Delete Category</button>
            {% else %}
                <input type="submit" class="btn btn-danger" value="Delete category" name="_d"
                    onclick="return confirm('Are you sure you want to delete?');">
            {% endif %}
        {% endif %}
    </div>
</form>
{% endblock %}
