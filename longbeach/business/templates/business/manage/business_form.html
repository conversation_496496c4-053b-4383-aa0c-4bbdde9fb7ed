{% extends "base.html" %}

{% load search_tags static image_tags util_tags %}
{% block content_title %} {% endblock %}

{% block body_extra %}
{{ block.super }}
<style>
    .page-header {padding-bottom: 0; margin: 0; border: 0;}
</style>
<link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/timepicker/1.3.5/jquery.timepicker.min.css">
<link rel="stylesheet" href="//maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">

<script src="//cdnjs.cloudflare.com/ajax/libs/timepicker/1.3.5/jquery.timepicker.min.js"></script>
<script>
    {% include 'business/manage/includes/js/business_form.js' %}
    {% include 'business/manage/includes/js/masthead_preview.js' %}
</script>

<style type="text/css">
    {% include 'business/manage/includes/css/business_form.css' %}
    {% include 'business/manage/includes/css/masthead.css' %}
</style>
{% endblock %}

{# side bar global navigation #}
{% block navigation %}
<ul class="nav nav-list">
    <li class="nav-header">Business</li>
    <li class="active"><a href="{% url 'business_list' %}"><i class="icon-briefcase"></i> Profiles</a></li>
    {% if perms.business.add_businesscategory or perms.business.change_businesscategory or perms.business.delete_businesscategory %}
    <li><a href="{% url 'category_list' %}"><i class="icon-folder-open"></i> Categories</a></li>
    {% endif %}
</ul>
{% endblock %}

{% block content %}

<form method="POST" enctype="multipart/form-data" class="form-horizontal">

<legend>{% if editing %}
      Edit your Business: {{ business }} <i class="icon-edit opacity70"></i>
      <br>

      {% if business.has_business_feature_ads %}
        <a style="font-size:12px;" href="{% url 'business_feature_ad_list' business.id %}">View/Edit Business Feature Ad(s)</a>
      {% else %}
        <a style="font-size:12px;" href="{% url 'business_feature_ad_create' business.id  %}">Create Business Feature Ad</a>
      {% endif %}
    {% else %}
      Add your Business profile
    {% endif %}</legend>

    {% csrf_token %}

    {{ business_form.organization }}
    {{ business_form.is_active }}
    {% for name, fieldset in business_form.Meta.fieldsets %}
        <div style="position: relative;" class="section {% if name %}{{ name|slugify }}{% else %}none{% endif %}">
        {% if name %}<h3>{{ name }}</h3>{% endif %}
        {% if name == 'Photo Gallery' %}
            {{ business_formset.management_form }}
            <script type="text/html" id="galleryimage-template">
                <div class="gallery-image">
                    {% for field in business_formset.empty_form %}
                        {% if field.name == 'DELETE' %}
                            <div class="remove hide" title="Remove">
                            {{ field }}
                            </div>
                        {% elif field.name == 'image' %}
                            {{field}}
                            <label for="{{ field.id_for_label }}"><span>Select Image</span></label>
                        {% else %}
                            {{field}}
                        {% endif %}
                    {% endfor %}
                </div>
            </script>
            <span id="gallery_images_container">
                {% for business_form in business_formset %}
                    <div class="gallery-image">
                    {% for field in business_form %}
                        {% if business_form.image.value %}
                            {% if field.name == 'image' %}
                                <img src="{% transform_url image=field.value width=100 height=100 %}" width="100" height="100">
                            {% endif %}
                            {% if field.name == 'DELETE' %}
                                <div class="remove" title="Remove">
                                {{ field }}
                                </div>
                            {% elif field.name == 'id' %}
                                {{field}}
                            {% endif %}
                        {% else %}
                            {% if field.name == 'DELETE' %}
                                <div class="remove hide" title="Remove">
                                {{ field }}
                                </div>
                            {% elif field.name == 'image' %}
                                {{field}}
                                <label for="{{ field.id_for_label }}"><span>Select Image</span></label>
                            {% else %}
                                {{field}}
                            {% endif %}
                        {% endif %}
                    {% endfor %}
                    </div>
                {% endfor %}
            </span>
            <div class="gallery-image add-image">
                <span>Add Image +</span>
            </div>
        {% endif %}
        {% if name == 'Opening Hours' %}
            {{ business_hours_formset.management_form }}
            <script type="text/html" id="openinghours-template">
                <div class="opening_day">
                    {% for field in business_hours_formset.empty_form %}
                        {% if not field.is_hidden and field.name == 'DELETE' %}{{ field.label }}: {% endif %}
                        {{ field }}
                        {% if field.name == 'from_hour' %}
                        <span class="opening_hours_divider">to</span>
                        {% endif %}
                    {% endfor %}
                </div>
            </script>

            <div class="controls">
            <div id="opening_hours_container">
            {% for opening_hours_form in business_hours_formset %}
                <div class="control opening_day">
                    {% for field in opening_hours_form %}
                        {% if field.errors %}
                        <span class="errorlist">{{ field.errors }}<br></span>
                        {% endif %}
                    {% endfor %}
                    {% for field in opening_hours_form %}
                        {% if not field.is_hidden and field.name == 'DELETE' %}{{ field.label }}: {% endif %}
                        {{ field }}
                        {% if field.name == 'from_hour' %}
                        <span class="opening_hours_divider">to</span>
                        {% endif %}
                    {% endfor %}
                </div>
            {% endfor %}
            </div>
                {% if business_hours_formset.initial_form_count < business_hours_formset.max_num %}
                    <span class="add_bus_hours">Add</span>
                {% endif %}
            </div>
        {% endif %}
        {% for field_name in fieldset.fields %}
            {% with field=business_form|form_field_lookup:field_name %}
                {% if field %}
                    {% if not field.is_hidden %}
                        {% if field.name == "logo" or field.name == "hero" %}
                            <div class="image">
                                {{ field.errors }}
                        {% endif %}
                    <div class="control-group field_{{ field.name }}">
                        <label class="control-label" for="{{ field.label }}">{{ field.label }} {% if field.field.required %} <span class="red">*</span>{% endif %}:</label>
                    {% endif %}
                        <div class="controls">
                            {% if field.name == 'listing_start' or field.name == 'listing_end' %}
                                <input id="{{ field.id_for_label }}" value="{{ field.value|date:"d/m/Y" }}" name="{{ field.name }}" data-provide="datepicker" data-date-format="dd/mm/yyyy" />
                            {% elif field.name == 'logo' %}
                                {% if business.logo %}Change:{% endif %}
                                <input id="{{ field.id_for_label }}" type="file" name="{{ field.name }}" />
                                {% if business.logo and editing %}
                                    <div>
                                        <img src="{% transform_url image=field.value width=300 height=300 %}">
                                    </div>
                                {% endif %}
                           {% elif field.name == 'hero' %}
                                {% if business.hero %}Change:{% endif %}
                                <input id="{{ field.id_for_label }}" type="file" name="{{ field.name }}" />
                                {% if business.hero and editing %}
                                    <div>
                                        <img src="{{business.hero.url}}">
                                    </div>
                                {% endif %}
                            {% elif field.name == 'ad_image' %}
                                {% if business.ad_image %}Change:{% endif %}
                                <input id="{{ field.id_for_label }}" type="file" name="{{ field.name }}" />
                                <div>
                                    {% if business.ad_image %}
                                            <img src="{% transform_url image=field.value width=200 height=200 %}">
                                    {% else %}
                                        {% if business.logo %}
                                            <img src="{% transform_url image=business.logo width=200 height=200 %}">
                                        {% endif %}
                                    {% endif %}
                                </div>
                            {% else %}
                                {{ field }}
                            {% endif %}
                            {% if field.errors %}
                            <div class="errorlist">
                                {% for e in field.errors %}
                                {% if not forloop.first %} / {% endif %}{{ e }}
                                {% endfor %}
                            </div>
                            {% endif %}

                            {% if field.help_text %}
                                <span class="help-block">{{ field.help_text }}</span>
                            {% endif %}
                        </div>
                    </div>

                    {% if editing %}
                        {% if field.name == "address" and business.address %}
                            <div class="control-group">
                                <label class="control-label"></label>
                                <div class="controls">
                                    {% if business.is_resolved %}
                                        {{ business.map_link|safe }}
                                    {% else %}
                                        <span class="errorlist">Invalid</p>
                                    {% endif %}
                                </div>
                            </div>
                        {% endif %}
                    {% endif %}

                    {% if not field.is_hidden %}
                        {% if field.name == "logo" or field.name == "hero" %}
                            </div>
                        {% endif %}
                    {% endif %}
                {% endif %}
            {% endwith %}
        {% endfor %}
        {% if name == 'Website Content Ad' and business_form.ad_title.value %}
            <div class="previewbox">
            <h3>Preview</h3>
            <div class="section-wrapper column1">
                <div class="businesspromo box-content clear">
                <!-- <h4 class="section-heading list-heading businesspromo__heading">Local Business</h4> -->
                    <div class="businesspromo-block-device">Desktop, Tablet</div>
                    <div class="businesspromo-block mobileEnable">
                        <div class="businesspromo-block__image">
                            <img src="{% transform_url image=business_form.ad_image.value width=75 height=75 %}" />
                        </div>
                        <div class="businesspromo-text">
                            <h5 class="businesspromo-text__title">{{business_form.ad_title.value}}</h5>
                            <p class="businesspromo-text__summary">{{business_form.ad_copy.value}}</p>
                            <a href="#" class="businesspromo-text__phone">
                                <i class="fa fa-phone"></i>
                                <span class="businesspromo-text-phonenumber">{{business_form.telephone.value}} </span>
                                <span class="businesspromo-text-show-more">Show Number</span>
                            </a>
                        </div>
                    </div>

                    <div class="businesspromo-block mobileDisable">
                        <div class="businesspromo-block__image">
                            <img src="{% transform_url image=business_form.ad_image.value width=75 height=75 %}" />
                        </div>
                        <div class="businesspromo-text">
                            <h5 class="businesspromo-text__title">{{business_form.ad_title.value}}</h5>
                            <p class="businesspromo-text__summary">{{business_form.ad_copy.value}}</p>
                            <a href="#" class="businesspromo-text__phone">Find out More</a>
                        </div>
                    </div>

                    <div class="businesspromo-block-device">Mobile</div>

                    <div class="businesspromo-block mobileEnable businesspromo-block-mobile-call">
                        <div class="businesspromo-text businesspromo-text-adcontact-mobile">
                            <h5 class="businesspromo-text__title">{{business_form.ad_title.value}}</h5>
                            <p class="businesspromo-text__summary">{{business_form.ad_copy.value}}</p>
                        </div>
                        <div class="businesspromo-text-mobile-call-large">
                            <a href="#" class="businesspromo-text__phone businesspromo-text-mobile-call">
                                <i class="fa fa-phone mobile-call-large"></i>
                            </a>
                            <div class="businesspromo-text-call-label">CALL</div>
                        </div>
                    </div>

                    <div class="businesspromo-block mobileDisable">
                        <div class="businesspromo-block__image">
                            <img src="{% transform_url image=business_form.ad_image.value width=75 height=75 %}" />
                        </div>
                        <div class="businesspromo-text">
                            <h5 class="businesspromo-text__title">{{business_form.ad_title.value}}</h5>
                            <p class="businesspromo-text__summary">{{business_form.ad_copy.value}}</p>
                        </div>
                    </div>

                </div>
            </div>
            </div>
        {% endif %}
        </div>
    {% endfor %}
    <div class="form-actions alignr">

        {% if editing and perms.business.change_business %}
        <input type="submit" class="btn btn-danger btn-large" value="Delete business" name="_d"
            onclick="return confirm('Are you sure you want to delete?');">
        {% endif %}

        {% if perms.business.add_business %}
        <input type="submit" class="btn btn-primary btn-large" value="Save business" name="_s">
        {% endif %}
    </div>

</form>
{% endblock %}