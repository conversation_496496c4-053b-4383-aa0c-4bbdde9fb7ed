{% if requests %}
	<table>
		<thead>
			<tr>
				<th class="djdt-query" colspan="2">Request</th>
				<th class="djdt-timeline">Timeline</th>
				<th class="djdt-time">Time (ms)</th>
			</tr>
		</thead>
		<tbody>
			{% for request in requests %}
				<tr class="djDebugHoverable {% cycle 'djDebugOdd' 'djDebugEven' %}">
					<td class="djdt-toggle">
						<a class="djToggleSwitch" data-toggle-name="sqlMain" data-toggle-id="{{ forloop.counter }}" data-toggle-open="+" data-toggle-close="-" href="">+</a>
					</td>
					<td class="djdt-query">
						<div class="djDebugSqlWrap">
							<div class="djDebugSql">{{ request.method }} {{ request.url }}</div>
						</div>
					</td>
					<td class="djdt-timeline">
						<div class="djDebugTimeline"><div class="djDebugLineChart"><strong style="left: {{ request.start_offset }}%; position: relative; width: {{ request.width_ratio }}%">{{ request.width_ratio }}%</strong></div></div>
					</td>
					<td class="djdt-time">
						{{ request.duration|floatformat:"2" }}
					</td>
				</tr>
				<tr class="djUnselected djDebugHoverable {% cycle 'djDebugOdd' 'djDebugEven' %} djToggleDetails_{{ forloop.counter }}" id="sqlDetails_{{ forloop.counter }}">
                    <td></td>
                    <td colspan="3">
                        <h4>URL</h4>
                        <a href="{{ request.url }}" target="_blank">
                            {{ request.url }}
                        </a>

                        {% if request.headers %}
                            <h4>Headers</h4>
                            {% for header, value in request.headers.items %}
                                <p><code>{{ header|title }}: {{ value }}</code></p>
                            {% endfor %}
                        {% endif %}

                        {% if request.body %}
                            <h4>Body</h4>
                            <pre>{{ request.body }}</pre>
                        {% endif %}

                        <h4>Response</h4>
                        <h5>Status</h5>
                        <pre>{{ request.response.status_code }}</pre>

                        <h5>Headers</h5>
                        {% for header, value in request.response.headers.items %}
                            <p><code>{{ header|title }}: {{ value }}</code></p>
                        {% endfor %}

                        <h5>Body</h5>
                        <pre>{{ request.response.text }}</pre>

                        {% if request.stacktrace %}
                            <h4>Stacktrace</h4>
                            <pre class="djdt-stack">{{ request.stacktrace }}</pre>
                        {% endif %}
					</td>
				</tr>
			{% endfor %}
		</tbody>
	</table>
{% else %}
    <p>No requests were recorded during this request.</p>
{% endif %}
