"""
A panel for Django Debug Toolbar that shows HTTP requests.

Currently only supports those made through the `requests` library.

Usage:
1. Add `requests_panel.RequestsPanel` to your `DEBUG_TOOLBAR_PANELS` setting.
   If you don't have this setting, see the default value here
   https://github.com/jazzband/django-debug-toolbar/blob/master/debug_toolbar/settings.py
2. Add `requests_panel` to your `INSTALLED_APPS` setting.
"""

from timeit import default_timer as timer

import requests
from debug_toolbar import settings
from debug_toolbar.panels import Panel
from debug_toolbar.utils import get_stack, render_stacktrace, tidy_stacktrace

# Save the original method
_send = requests.sessions.Session.send


class RequestsPanel(Panel):
    """A panel for Django Debug Toolbar that shows HTTP requests."""

    template = "requests_panel.html"
    title = nav_title = "HTTP Requests"

    requests = None
    total_time = 0

    @property
    def nav_subtitle(self):
        """Generate the subtitle shown in the toolbar."""
        if not self.requests:
            return "0 requests"

        return "%d requests in %.2fms" % (len(self.requests), self.total_time)

    def enable_instrumentation(self):
        """Monkey patch `requests` to log each call."""

        def send(session, prepared_request, **kwargs):
            """Function to replace `Session.request`."""
            if not self.requests:
                self.requests = []

            start = timer()
            response = _send(session, prepared_request, **kwargs)
            end = timer()

            if settings.get_config()["ENABLE_STACKTRACES"]:
                # Skip the first stacktrace so we exclude this function
                stacktrace = tidy_stacktrace(reversed(get_stack()[1:]))
            else:
                stacktrace = []

            duration = (end - start) * 1000
            self.total_time += duration
            stacktrace = render_stacktrace(stacktrace)

            req = {
                "duration": duration,
                "headers": prepared_request.headers,
                "method": prepared_request.method,
                "response": response,
                "stacktrace": stacktrace,
                "url": prepared_request.url,
            }

            self.requests.append(req)

            return response

        requests.sessions.Session.send = send

    def disable_instrumentation(self):
        """Remove the patch."""
        requests.sessions.Session.send = _send

    def generate_stats(self, request, response):
        """
        Perform calculations for the timeline chart.

        Taken from the Debug Toolbar's SQL panel.
        """
        if not self.requests:
            return

        width_ratio_tally = 0

        for request in self.requests:
            try:
                request["width_ratio"] = (
                    request["duration"] / self.total_time
                ) * 100
                request["width_ratio_relative"] = (
                    100.0
                    * request["width_ratio"]
                    / (100.0 - width_ratio_tally)
                )
            except ZeroDivisionError:
                request["width_ratio"] = 0
                request["width_ratio_relative"] = 0

            request["start_offset"] = width_ratio_tally
            request["end_offset"] = (
                request["width_ratio"] + request["start_offset"]
            )
            width_ratio_tally += request["width_ratio"]

        self.record_stats({"requests": self.requests})
