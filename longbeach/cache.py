from calm_cache.backends.memcached import ZippedMCMixin
from django_elasticache.memcached import ElastiCache


class ZippyElastiCache(ZippedMCMixin, ElastiCache):
    """
    An extension of `django_elasticache.memcached.ElastiCache`
    supporting calm cache's optional compression of stored values.
    Optional binary memcached protocol implemented by ElastiCache.

    Example configuration:

        CACHES = {
            'default': {
                'BACKEND': 'racetrack.cache.ZippyElastiCache',
                'LOCATION': 'stuff.draaaf.cfg.use1.cache.amazonaws.com:11211',
                'BINARY': True,
                'OPTIONS': {
                    'MIN_COMPRESS_LEN': 1024
                },
            },
        }
    """
