{% load i18n %}
{% if page_obj.has_next or page_obj.has_previous %}
    <ul class="pager">
        <li{% if not page_obj.has_previous %} class="disabled"{% endif %}><a href="?page={% if page_obj.previous_page_number > 1 %}{{ page_obj.previous_page_number }}{% else %}1{% endif %}{{ getvars }}{{ hashtag }}" rel="prev">&larr; {% trans "Previous" %}</a></li>
        <li class="count">Page {{ page_obj.number }} of {{ paginator.num_pages }}</li>
        <li{% if not page_obj.has_next %} class="disabled"{% endif %}><a href="?page={% if page_obj.next_page_number < paginator.num_pages %}{{ page_obj.next_page_number }}{% else %}{{ paginator.num_pages }}{% endif %}{{ getvars }}{{ hashtag }}" rel="next">{% trans "Next" %} &rarr;</a></li>
    </ul>
{% endif %}
