{% extends request.user.is_authenticated|yesno:"pitcrews/base_sidebar.html,base.html" %}

{% load orgs_tags static %}

{% block breadcrumbs %}{% endblock %}

{% block head_title %}Longbeach{% endblock %}

{% block nav_title %}Longbeach{% endblock %}

{% block nav_url %}{% if request.user.is_authenticated %}{% url 'business_list' %}{% else %}{% url 'home' %}{% endif %}{% endblock %}
{% block nav_action %}{% endblock %}

{% block nav_right %}
    {{ block.super }}
    {% current_org_selector 'pitcrews/current_org_selector.html' %}
{% endblock %}

{% block navigation %}
<ul class="nav nav-list">
    <li class="nav-header">Business</li>
    <li><a href="{% url 'business_list' %}"><i class="icon-briefcase"></i> Profiles</a></li>
    {% if perms.business.add_businesscategory or perms.business.change_businesscategory or perms.business.delete_businesscategory %}
    <li><a href="{% url 'category_list' %}"><i class="icon-folder-open"></i> Categories</a></li>
    {% endif %}
</ul>
{% endblock %}

{% block head_styles %}
    {{ block.super }}
    <link href="{% get_static_prefix %}js/plugins/chosen/chosen.css" media="all" rel="stylesheet" type="text/css" />
{% endblock %}
{% block body_scripts %}
    {{ block.super }}
    <script src="{% get_static_prefix %}js/plugins/chosen/chosen.jquery.min.js"></script>
{% endblock %}
