{% if widget.is_initial %}
  {% if widget.value and widget.attrs.accept == "image/*" %}
  <img alt="" src="{{ widget.value.url }}" width="100" height="100"/>
{% else %}
  {{ widget.initial_text }}: <a href="{{ widget.value.url }}">{{ widget.value }}</a>
{% endif %}
{% if not widget.required %}
<label class="checkbox" for="{{ widget.checkbox_id }}">
  <input type="checkbox" name="{{ widget.checkbox_name }}" id="{{ widget.checkbox_id }}"{% if widget.attrs.disabled %} disabled{% endif %}>
  {{ widget.clear_checkbox_label }}
</label>{% endif %}<br>
{{ widget.input_text }}:{% endif %}
<input type="{{ widget.type }}" name="{{ widget.name }}"{% include "django/forms/widgets/attrs.html" %}>
