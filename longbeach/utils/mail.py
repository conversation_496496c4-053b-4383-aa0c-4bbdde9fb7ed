"""Utilities for sending email."""

import logging

from django.conf import settings
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string

logger = logging.getLogger(__name__)


def send_html_email(
    template_name: str, context: dict, subject: str, to: tuple[str]
) -> int:
    """
    Send an HTML email using text and HTML templates.

    `template_name` should not have an extension. `txt` and `html` are used.
    """
    context.setdefault("omit_signature", False)
    context["subject"] = subject

    text_content = render_to_string(
        f"{template_name}.txt",
        context,
    )

    html_content = render_to_string(
        f"{template_name}.html",
        context,
    )

    from_email = settings.ACM_NOTICEBOARD_EMAIL

    msg = EmailMultiAlternatives(
        subject,
        text_content,
        from_email,
        to,
    )

    msg.attach_alternative(html_content, "text/html")

    logger.info(
        f"Sending email from {from_email} to {','.join(to)} subject: {subject}"
    )

    return msg.send(fail_silently=True)
