"""Utilities for searching."""

import operator
from functools import reduce
from typing import Iterable

from django.db.models import Q


def term_to_query(term: str, fields: Iterable[str]) -> list[Q]:
    """
    Create queryset filters based on a search term across the given fields.

    Based on the Django admin search. Queries whether each field contains any
    word in the term (case insensitive).
    """
    terms = term.split()

    # Makes a query by ANDing all the terms together and ORing those term
    # groups for each field
    return reduce(
        operator.or_,
        (
            reduce(
                operator.and_,
                [Q(**{f"{field}__icontains": word}) for word in terms],  # type: ignore[misc]
            )
            for field in fields
        ),
    )
