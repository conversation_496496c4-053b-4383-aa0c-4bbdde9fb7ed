from django.conf import settings
from django.contrib import admin
from django.urls import include, re_path
from django.views.i18n import JavaScriptCatalog
from pitcrews.layouts.views import landing
from tastypie.api import Api

from longbeach.business.api import (
    BusinessCategoryResource,
    BusinessFeatureAdResource,
    BusinessResource,
)

admin.autodiscover()

v1_api = Api(api_name="")
v1_api.register(BusinessResource())
v1_api.register(BusinessCategoryResource())
v1_api.register(BusinessFeatureAdResource())

js_info_dict = {
    "packages": ("recurrence",),
}

urlpatterns = [
    re_path(r"^admin/", admin.site.urls),
    re_path(r"^shared_login/", include("shared_login.login_consumer.urls")),
    re_path(
        r"^shared_login/webhook/",
        include("shared_login.login_consumer.webhooks.urls"),
    ),
    re_path(r"^api", include(v1_api.urls)),
    re_path(r"^manage/", include("longbeach.business.manage.urls")),
    re_path(r"^health/", include("pitcrews_health.urls")),
    re_path(r"^cognito/", include("newsnow_cognito.consumer.urls")),
    re_path(r"^$", landing, name="home"),
    re_path(r"^", include("longbeach.localads.urls")),
    re_path(r"^", include("longbeach.classifieds.urls")),
    re_path(r"^manage/ugc/", include("longbeach.ugc.manage.urls")),
    re_path(r"^", include("longbeach.ugc.urls")),
    # Internationalization
    re_path(
        r"^jsi18n/$", JavaScriptCatalog.as_view(**js_info_dict), name="jsi18n"
    ),
]

# Manual set up for debug toolbar. App will not load properly when
# running under Gunicorn with DEBUG_TOOLBAR_PATCH_SETTINGS=True.
if settings.DEBUG:
    import debug_toolbar

    urlpatterns += [
        re_path(r"^__debug__/", include(debug_toolbar.urls)),
    ]

from django.contrib.staticfiles.urls import (  # noqa: E402
    staticfiles_urlpatterns,
)

urlpatterns += staticfiles_urlpatterns()
