# Shared logging settings
import logging
import logging.config
import os
import platform

import sentry_sdk
from decouple import AutoConfig, UndefinedValueError
from sentry_sdk.integrations.django import DjangoIntegration

env = AutoConfig(search_path=os.environ["BUILD_CONFIG_PATH"])

_PROJECT_PATH = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
LOG_DIRECTORY = os.path.join(
    os.path.dirname(os.path.dirname(os.path.dirname(_PROJECT_PATH))), "log"
)
JSON_LOG_FILE = os.path.join(LOG_DIRECTORY, "django-json.log")

DEBUG = env("DJANGO_DEBUG", default=False, cast=bool)


class RequireSettingTrue(logging.Filter):
    """
    Allows the mesage to be logged if Django setting `setting` is
    set to `True`
    """

    def __init__(self, name="", setting=None):
        super().__init__(name)
        from django.conf import settings

        self.settings = settings
        self.setting = setting

    def filter(self, record):
        if self.setting:
            return getattr(self.settings, self.setting, False)
        return True

    def __repr__(self):
        return str(
            "<RequireSettingTrue: %s = %s>" % (self.setting, self.filter(None))
        )


class AppContextFilter(logging.Filter):
    """
    Adds some application context information to the logging record
    """

    def __init__(self, name=""):
        super().__init__(name)
        self.host = platform.node()
        from django.conf import settings

        self.settings = settings

    def filter(self, record):
        record.host = self.host
        settings = self.settings
        record.app_name = getattr(settings, "PITCREWS_APPNAME", None)
        record.deploy_id = getattr(settings, "PITCREWS_DEPLOY_ID", None)
        record.deploy_timestamp = getattr(
            settings, "PITCREWS_DEPLOY_TIMESTAMP", None
        )
        return True


# Configure JSON formatter if the module is installed, otherwise log nothing
try:
    from pythonjsonlogger.jsonlogger import JsonFormatter
except ImportError:
    json_formatter = {"format": ""}
else:
    json_formatter = {
        "()": JsonFormatter,
        "format": "%(host)s %(app_name)s %(deploy_id)s %(deploy_timestamp)s "
        "%(processName)s %(threadName)s "
        "%(filename)s %(lineno)d %(module)s %(funcName)s %(name)%s "
        "%(levelname)s %(asctime)s %(message)s %(exc_text)s",
    }

if os.path.isdir(os.path.dirname(JSON_LOG_FILE)):
    json_handler = {
        "level": "INFO",
        "class": "logging.handlers.WatchedFileHandler",
        "filename": JSON_LOG_FILE,
        "formatter": "json",
        "filters": ["app_context"],
    }
else:
    json_handler = {"class": "logging.NullHandler"}

LOGGING_LEVEL = "WARNING"

LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "simple": {
            "format": "[%(name)s] %(message)s",
        },
        "verbose": {
            "format": "%(asctime)s [%(name)s] %(levelname)s: %(message)s",
        },
        "json": json_formatter,
    },
    "filters": {
        "require_debug_false": {"()": "django.utils.log.RequireDebugFalse"},
        "app_context": {"()": AppContextFilter},
        "require_allow_mail_tracebacks": {
            "()": RequireSettingTrue,
            "setting": "ALLOW_MAIL_TRACEBACKS",
        },
    },
    "handlers": {
        "console": {
            "level": "DEBUG" if DEBUG else "INFO",
            "class": "logging.StreamHandler",
            "formatter": "verbose",
        },
        "json": json_handler,
    },
    # Base settings for all loggers that aren't explicitly configured or
    # don't have have 'propagate' : False
    #
    # By default, every logger emits messages with severity >= INFO
    # to `console` handler that is able to receive any level, including DEBUG
    "root": {
        "handlers": ["console", "json"],
        "level": "DEBUG" if DEBUG else "INFO",
    },
    "loggers": {
        # Silence excessively verbose loggers
        "django.db.backends": {
            "level": LOGGING_LEVEL,
        },
        "cors": {
            "level": LOGGING_LEVEL,
        },
        "requests": {
            "level": LOGGING_LEVEL,
        },
        "elasticsearch": {
            "level": LOGGING_LEVEL,
        },
    },
}


# Error Logging: Sentry

try:
    sentry_dsn = env("DJANGO_SENTRY_DSN")
    if sentry_dsn:
        sentry_sdk.init(
            dsn=sentry_dsn,
            environment=env("DJANGO_ENVIRONMENT"),
            integrations=[DjangoIntegration()],
            # If you wish to associate users to errors (assuming you are using
            # django.contrib.auth) you may enable sending PII data.
            send_default_pii=True,
        )
except UndefinedValueError:
    pass


# Disable Django's configuration of logging
LOGGING_CONFIG = None

# Apply the configuration manually
# http://stackoverflow.com/a/22336174
# If the LOGGING dictionary is changed later, the procedure should be repeated
logging.config.dictConfig(LOGGING)
