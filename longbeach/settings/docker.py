import os
import platform
import re
import socket
from typing import Union

from decouple import AutoConfig, Csv
from django.urls import reverse_lazy

# Used for coersion of string environment variables to python types. Note that
# there doesn't have to be any config files in the search path. However if an
# `.env` file is present, it will take precedence over environment variables.
# We use this file in our AWS stacks for loading secrets from S3.
env = AutoConfig(search_path=os.environ["BUILD_CONFIG_PATH"])

ENVIRONMENT = env("DJANGO_ENVIRONMENT", default="development")

PROJECT_PATH = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

DEPLOY_TIMESTAMP = os.environ.get("DEPLOY_TIMESTAMP", "")
if not DEPLOY_TIMESTAMP and "BUILD_TIMESTAMP_FILE" in os.environ:
    DEPLOY_TIMESTAMP = (
        open(os.environ["BUILD_TIMESTAMP_FILE"]).readline().strip()
    )


# Apps

INSTALLED_APPS: tuple[str, ...] = (
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.gis",
    "django.contrib.humanize",
    "django.contrib.sessions",
    "django.contrib.sites",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django.contrib.admin",
    "django.contrib.admindocs",
    "django.forms",
    "django_filters",
    "taggit",
    "haystack",
    "recurrence",
    "rest_framework",
    "reversion",
    "reversion_compare",
    # Must be placed before pitcrews.layouts for template override
    "newsnow_cognito.common",
    "newsnow_cognito.consumer",
    "oauthsome.oauth_client",
    "oauthsome.oauth_server",
    "shared_login.login_consumer",
    "shared_orgs_client",
    "pitcrews.layouts",
    "pitcrews_health",
    "longbeach.settings",
    "longbeach.business",
    "longbeach.localads",
    "longbeach.classifieds",
    "longbeach.ugc",
    "valencia_storage",
)


# Middleware

MIDDLEWARE = [
    "django.middleware.common.CommonMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "shared_login.login_consumer.middleware.SharedLoginRedirect",
    "shared_orgs_client.middleware.OrgsMiddleware",
    "django.middleware.security.SecurityMiddleware",
]


# Debugging

DEBUG = env("DJANGO_DEBUG", default=False, cast=bool)
TASTYPIE_FULL_DEBUG = DEBUG

# Required for running debug toolbar under Gunicorn.
DEBUG_TOOLBAR_PATCH_SETTINGS = False

if DEBUG:
    for optional in ("django_extensions", "debug_toolbar", "haystack_panel"):
        try:
            __import__(optional)
        except ImportError:
            pass
        else:
            INSTALLED_APPS += (optional,)

if "debug_toolbar" in INSTALLED_APPS:
    from debug_toolbar.settings import PANELS_DEFAULTS

    MIDDLEWARE += ("debug_toolbar.middleware.DebugToolbarMiddleware",)
    DEBUG_TOOLBAR_CONFIG = {
        "DISABLE_PANELS": [
            "debug_toolbar.panels.redirects.RedirectsPanel",
            "debug_toolbar.panels.sql.SQLPanel",
        ]
    }
    DEBUG_TOOLBAR_PANELS = PANELS_DEFAULTS + [
        "haystack_panel.panel.HaystackDebugPanel",
        "longbeach.requests_panel.RequestsPanel",
    ]
    INSTALLED_APPS += ("longbeach.requests_panel",)

if DEBUG:
    try:
        # Address when running locally on docker compose.
        INTERNAL_IPS = [socket.gethostbyname("app-proxy")]
    except socket.gaierror:
        # Address when running on ECS.
        INTERNAL_IPS = [socket.gethostbyname(socket.gethostname())]


# Internationalisation / Localisation

USE_TZ = True
TIME_ZONE = "Australia/Sydney"

USE_I18N = True
USE_L10N = True
LANGUAGE_CODE = "en-au"

TIME_INPUT_FORMATS = [
    "%H:%M:%S",
    "%H:%M:%S.%f",
    "%H:%M",
    "%I:%M %p",
]

PHONENUMBER_DB_FORMAT = "NATIONAL"
PHONENUMBER_DEFAULT_REGION = "AU"


# Cache

MEMCACHE_LOCATION = env("DJANGO_MEMCACHE_LOCATION", default="local")
MEMCACHE_LOCATION_BACKENDS = {
    "local": "django.core.cache.backends.memcached.PyLibMCCache",
    "elasticache": "longbeach.cache.ZippyElastiCache",
}

MEMCACHE_HOSTS = env("DJANGO_MEMCACHE_HOSTS", default="", cast=Csv())

MEMCACHE_MIN_COMPRESS_LEN = 1024

if MEMCACHE_HOSTS:
    _CACHE_OPTIONS = {
        "behaviors": {
            "tcp_nodelay": True,
            "ketama": True,
            "remove_failed": 1,
            "retry_timeout": 1,
            "dead_timeout": 600,
        },
    }

    CACHES = {
        "default": {
            "BACKEND": MEMCACHE_LOCATION_BACKENDS[MEMCACHE_LOCATION],
            "LOCATION": MEMCACHE_HOSTS,
            "KEY_PREFIX": "/elc/default/",
            "BINARY": True,
            "OPTIONS": _CACHE_OPTIONS,
        },
        "global_sessions": {
            "BACKEND": MEMCACHE_LOCATION_BACKENDS[MEMCACHE_LOCATION],
            "LOCATION": MEMCACHE_HOSTS,
            "KEY_PREFIX": "/elc/sessions/",
            "BINARY": True,
            "OPTIONS": _CACHE_OPTIONS,
        },
    }
else:
    CACHES = {
        "default": {
            "BACKEND": "django.core.cache.backends.locmem.LocMemCache",
            "LOCATION": "default",
        },
        "global_sessions": {
            "BACKEND": "django.core.cache.backends.locmem.LocMemCache",
            "LOCATION": "global_session",
        },
    }


# Database

pg_conn_max_age: Union[str, int] = os.environ.get("PGCONNMAXAGE", 0)

DATABASES = {
    "default": {
        "ENGINE": "django.contrib.gis.db.backends.postgis",
        "NAME": os.environ["PGDATABASE"],
        "USER": env("PGUSER"),
        "PASSWORD": env("PGPASSWORD"),
        "CONN_MAX_AGE": None
        if pg_conn_max_age == ""
        else int(pg_conn_max_age),
    }
}


# Security

SECRET_KEY = "*sj3b^pd^2&amp;vrci3c(095!bj%exjru%ef937wp7%slnppv&amp;$93"

ALLOWED_HOSTS = env("DJANGO_ALLOWED_HOSTS", default="*", cast=Csv())

CORS_ORIGIN_REGEX_WHITELIST = env(
    "DJANGO_CORS_ORIGIN_WHITELIST", default="^.*$", cast=Csv()
)

SESSION_ENGINE = "shared_login.shared_session"
SESSION_COOKIE_SECURE = True

SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTOCOL", "https")
SECURE_SSL_REDIRECT = not DEBUG
SECURE_REDIRECT_EXEMPT = (r"^health/check/",)
SECURE_HSTS_SECONDS = 60 * 60 * 12  # 1 hour sts
SECURE_FRAME_DENY = True

X_FRAME_OPTIONS = "DENY"

FILE_UPLOAD_MAX_MEMORY_SIZE = 30 * 1024 * 1024

# Disable SSL certificate verification.
SSL_VERIFY_CERTIFICATE = env(
    "DJANGO_SSL_VERIFY_CERTIFICATE", default=True, cast=bool
)
OAUTH_CLIENT_VERIFY_SSL = SLUMBER_VERIFY_SSL = SSL_VERIFY_CERTIFICATE

if not SSL_VERIFY_CERTIFICATE:
    # Prevent oauthlib InsecureTransportError
    os.environ["OAUTHLIB_INSECURE_TRANSPORT"] = "True"


# Logging / Error reporting

from .logging_settings import *  # noqa: E402, F403

# Templates

_TEMPLATE_LOADERS = [
    "django.template.loaders.filesystem.Loader",
    "django.template.loaders.app_directories.Loader",
]

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [
            os.path.join(PROJECT_PATH, "templates"),
        ],
        "OPTIONS": {
            "debug": DEBUG,
            "loaders": _TEMPLATE_LOADERS
            if DEBUG
            else [
                ("django.template.loaders.cached.Loader", _TEMPLATE_LOADERS)  # type: ignore[list-item]
            ],
            "context_processors": [
                "django.contrib.auth.context_processors.auth",
                "django.template.context_processors.debug",
                "django.template.context_processors.i18n",
                "django.template.context_processors.media",
                "django.template.context_processors.static",
                "django.template.context_processors.request",
                "django.contrib.messages.context_processors.messages",
                "pitcrews.layouts.context_processors.settings",
                "newsnow_cognito.consumer.context_processors.config",
            ],
            "builtins": [
                "pitcrews.layouts.templatetags.pitcrews_layouts",
            ],
        },
    },
]

FORM_RENDERER = "django.forms.renderers.TemplatesSetting"

# Email

DEFAULT_FROM_EMAIL = "Longbeach <<EMAIL>>"
NEWSNOW_FROM_EMAIL = "NewsNow Publishing Platform <<EMAIL>>"

# Must be verified in AWS
NOREPLY_NEWSNOW_FROM_EMAIL = "<EMAIL>"

EMAIL_DOMAIN = env("DJANGO_EMAIL_DOMAIN", default="")

ACM_NOTICEBOARD_EMAIL = f"noticeboard@{EMAIL_DOMAIN}"

ADMINS = (("Pitcrews Team", "<EMAIL>"),)
MANAGERS = ADMINS
PITCREWS_APPNAME = PITCREWS_APP_NAME = "Longbeach"
PITCREWS_APP_LABEL = "Business Listings"

SERVER_EMAIL = "<EMAIL>"
try:
    SERVER_EMAIL = "%s %s <%s>" % (
        PITCREWS_APPNAME,
        os.environ["DJANGO_ENVIRONMENT"],
        SERVER_EMAIL,
    )
except Exception:
    pass

EMAIL_SUBJECT_PREFIX = "[%s] " % PITCREWS_APPNAME
try:
    EMAIL_SUBJECT_PREFIX += "[%s:%s@%s] " % (
        PITCREWS_APPNAME,
        DEPLOY_TIMESTAMP,
        platform.node().split(".")[0],
    )
except Exception:
    pass

EMAIL_HOST = env("DJANGO_EMAIL_HOST", default=None)
EMAIL_PORT = env("DJANGO_EMAIL_PORT", default=25, cast=int)
EMAIL_HOST_USER = env("DJANGO_EMAIL_HOST_USER", default=None)
EMAIL_HOST_PASSWORD = env("DJANGO_EMAIL_HOST_PASSWORD", default=None)
EMAIL_USE_SSL = env("DJANGO_EMAIL_USE_SSL", default=False)

if EMAIL_USE_SSL:
    EMAIL_BACKEND = "django_smtp_ssl.SSLEmailBackend"
elif EMAIL_HOST is None:
    EMAIL_BACKEND = "django.core.mail.backends.console.EmailBackend"

ALLOW_MAIL_TRACEBACKS = True


# Static files

STATICFILES_DIRS = (os.path.join(PROJECT_PATH, "static"),)

STATICFILES_FINDERS = (
    "django.contrib.staticfiles.finders.FileSystemFinder",
    "django.contrib.staticfiles.finders.AppDirectoriesFinder",
)

STATIC_ROOT = os.path.join(os.environ["DJANGO_STATIC_DIR"], DEPLOY_TIMESTAMP)
STATIC_URL = (
    os.path.join(
        os.environ.get(
            "DJANGO_STATIC_URL",
            "/static/",
        ),
        DEPLOY_TIMESTAMP,
    )
    + "/"
)


# Media

MEDIA_ROOT = os.environ["DJANGO_MEDIA_DIR"]
MEDIA_URL = os.environ.get("DJANGO_MEDIA_URL", "/media/")


# Admin / Dashboard

SITE_ID = 1

NEWSNOW_DASHBOARD_URL = env("DJANGO_NEWSNOW_URL", default="https://newsnow.io")

PITCREWS_SERVICE_HOSTS = {
    "monza": os.environ["DJANGO_MONZA_URL"],
    "silverstone": os.environ["DJANGO_SILVERSTONE_URL"],
    "suzuka": "%smanage" % os.environ["DJANGO_SUZUKA_URL"],
    "valencia": os.environ["DJANGO_VALENCIA_URL"],
    "sepang": "//%(host)s",
    "newsnow": NEWSNOW_DASHBOARD_URL,
}


# Django

ROOT_URLCONF = "longbeach.urls"


# Authentication (oAuth)

AUTHENTICATION_BACKENDS = (
    "newsnow_cognito.consumer.auth.CognitoAuthBackend",
    "shared_login.login_consumer.auth.SharedLoginBackend",
    "django.contrib.auth.backends.ModelBackend",
)

LOGIN_URL = reverse_lazy("cognito_consumer:login")
LOGIN_REDIRECT_URL = reverse_lazy("business_list")
LOGOUT_REDIRECT_URL = reverse_lazy("home")

REMOTE_USER_ID_CALLBACK = lambda u: u.remote_user.remote_user_id


def logout_redirect_callback(request, **kwargs):
    from django.contrib import messages

    messages.add_message(request, messages.INFO, "You have been logged out.")
    return reverse_lazy("home")


SHARED_LOGIN_LOGOUT_REDIRECT_URL = logout_redirect_callback
SHARED_LOGIN_SERVER_NAME = "monza"
SHARED_LOGIN_SESSION_CACHE = "global_sessions"
SHARED_LOGIN_IGNORABLE_URLS = (
    re.compile(r"^/cognito/login/"),
    r"/shared_login/webhook/[\w\d/]+",
    r"/api/.+",
)

MONZA_ACCOUNT_UPDATE_TOPICS = env(
    "DJANGO_MONZA_ACCOUNT_UPDATE_TOPICS",
    default="",
    cast=lambda v: [s.strip() for s in v.split(",")],
)

OAUTH_CLIENT_SERVER_BACKEND = (
    "oauthsome.oauth_client.backends.database.SettingsBackend"
)


# Valencia

# FIXME: django-valencia-storage gets key/secret from settings, but it
# really should be getting it from `OAUTH_CLIENT_SERVER_BACKEND` instead.
VALENCIA_KEY = "JDCAZzLWisOdMjjX2X7tCMtLHmgjrF0r"
VALENCIA_SECRET = "RMDU42KGsidJ7b6kM6xBlCMbutP4lZCS"
VALENCIA_BUCKET = "longbeach"

GEOCODE_GOOGLE_CLIENT_ID = "gme-fairfax"
GEOCODE_GOOGLE_SECRET_KEY = "46p1L30bqYcasBfBoyKx--mpRVc="

# Host URLs

MONZA_HOST = os.environ["DJANGO_MONZA_URL"]
SILVERSTONE_HOST = os.environ["DJANGO_SILVERSTONE_URL"]
LONGBEACH_HOST = os.environ["DJANGO_LONGBEACH_URL"]
SUZUKA_HOST = os.environ["DJANGO_SUZUKA_URL"]
VALENCIA_HOST = os.environ["DJANGO_VALENCIA_URL"]
CDN_HOST = os.environ["DJANGO_CDN_URL"]
FUJI_HOST = os.environ["DJANGO_FUJI_URL"]

SUZUKA_API_HOST = "%sapi/v1" % SUZUKA_HOST
SILVERSTONE_API_HOST = "%sapi/v1" % SILVERSTONE_HOST

VALENCIA_SERVER = VALENCIA_HOST
VALENCIA_CDN = VALENCIA_HOST + "media"
VALENCIA_TRANSFORM_HOST = os.environ["DJANGO_TRANSFORM_URL"]

AKAMAI_TRANSFORM_URL = os.environ.get(
    "AKAMAI_TRANSFORM_URL", VALENCIA_TRANSFORM_HOST
)

SHARED_ORGS_API_PROVIDER = "%sorgs/api/v2" % MONZA_HOST


# Haystack / Elasticsearch

HAYSTACK_SIGNAL_PROCESSOR = os.environ.get(
    "DJANGO_HAYSTACK_SIGNAL_PROCESSOR", "haystack.signals.BaseSignalProcessor"
)

ELASTICSEARCH_INDEX = os.environ.get(
    "ELASTICSEARCH_INDEX", os.environ["DJANGO_PROJECT"]
)

ELASTICSEARCH_URL = os.environ.get("ELASTICSEARCH_URL", None)

if ELASTICSEARCH_URL:
    es_backend: dict = {
        "ENGINE": "haystack.backends.elasticsearch7_backend.Elasticsearch7SearchEngine",
        "URL": (ELASTICSEARCH_URL,),
        "INDEX_NAME": ELASTICSEARCH_INDEX,
    }

    if "es.amazonaws.com" in ELASTICSEARCH_URL:
        from aws_elasticsearch import AWSRequestsHttpConnection, url_to_esdict

        es_backend.update(
            {
                "URL": (url_to_esdict(ELASTICSEARCH_URL),),
                "KWARGS": {
                    "connection_class": AWSRequestsHttpConnection,
                    "verify_certs": True,
                },
            }
        )

    HAYSTACK_CONNECTIONS = {"default": es_backend}

else:
    HAYSTACK_CONNECTIONS = {
        "default": {
            "ENGINE": "haystack.backends.simple_backend.SimpleEngine",
        },
    }

SESSION_ENGINE = "shared_login.shared_session"


# Application Status

PITCREWS_HEALTH_CHECKS = ["databases", "caches", "content"]

# Static URLS
TERMS_CONDITIONS_URL = "https://www.adcentre.com.au/terms-conditions/"
PRIVACY_POLICY_URL = "http://www.fairfaxmedia.com.au/company/conditions-of-use/conditions-of-use"

FUJI_CLASSIFIEDS_SOURCE_ID = 151
# There may be a bug with Tastypie pagination that returns results not correctly
# ordered by order_by so try avoiding pagination.
# 1000 is the default pagination max limit in Tastypie and each Pongrass upload,
# 4 times every night at 1, 2, 3 and 4 AM, is unlikely to have more than 1000
FUJI_CLASSIFIEDS_LIMIT = 1000


# Newsnow Cognito Common/Consumer settings

COGNITO_AUTH_URL = env("DJANGO_COGNITO_AUTH_URL", default="")
COGNITO_APP_CLIENT_ID = env("DJANGO_COGNITO_APP_CLIENT_ID", default="")
COGNITO_LOGIN_REDIRECT_URL = env(
    "DJANGO_COGNITO_LOGIN_REDIRECT_URL",
    default="{}/cognito/authorize/".format(LONGBEACH_HOST.rstrip("/")),
)
COGNITO_LOGIN_SERVER_CLIENT = env(
    "DJANGO_COGNITO_LOGIN_SERVER_CLIENT", default="longbeach"
)


# Local settings

try:
    from .local_settings import *  # noqa: E402, F403
except ImportError:
    pass
