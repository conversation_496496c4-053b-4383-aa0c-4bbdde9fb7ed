# See: http://stackoverflow.com/questions/9700019/detect-eventlet-environment
#
# Code stolen and adapted from:
# https://github.com/celery/kombu/blob/master/kombu/syn.py
#


import sys

_environment = None


def _detect_environment(default):
    ## -eventlet-
    if "eventlet" in sys.modules:
        try:
            import socket

            from eventlet.patcher import is_monkey_patched as is_eventlet

            if is_eventlet(socket):
                return "eventlet"
        except ImportError:
            pass

    # -gevent-
    if "gevent" in sys.modules:
        try:
            import socket

            from gevent import socket as _gsocket

            if socket.socket is _gsocket.socket:
                return "gevent"
        except ImportError:
            pass

    return default


def detect_environment(default="default"):
    global _environment
    if _environment is None:
        _environment = _detect_environment(default)
    return _environment
