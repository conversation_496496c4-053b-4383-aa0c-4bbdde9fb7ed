from .docker import *  # noqa: F403

try:
    MIDDLEWARE.remove("django.middleware.security.SecurityMiddleware")  # noqa: F405
except ValueError:
    pass

SESSION_ENGINE = "django.contrib.sessions.backends.db"

CACHES = {
    "default": {
        "BACKEND": "django.core.cache.backends.dummy.DummyCache",
        "LOCATION": "default",
    },
    "global_sessions": {
        "BACKEND": "django.core.cache.backends.dummy.DummyCache",
        "LOCATION": "global_sessions",
    },
}

HAYSTACK_SIGNAL_PROCESSOR = "haystack.signals.BaseSignalProcessor"

HAYSTACK_CONNECTIONS = {
    "default": {
        "ENGINE": "haystack.backends.whoosh_backend.WhooshEngine",
        "STORAGE": "ram",
    },
}

OAUTH_CLIENT_SERVERS = {
    "suzuka": {
        "name": "suzuka",
        "key": "testkey-suzuka",
        "secret": "testsecret-suzuka",
    },
}

# Static URLS
TERMS_CONDITIONS_URL = "https://www.adcentre.com.au/terms-conditions/"
PRIVACY_POLICY_URL = "http://www.fairfaxmedia.com.au/company/conditions-of-use/conditions-of-use"

try:
    from local_settings_test import *  # noqa: F403
except ImportError:
    pass
