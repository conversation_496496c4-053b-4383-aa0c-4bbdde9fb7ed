# Generated by Django 3.1.14 on 2025-02-18 04:43

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Settings',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('to_email', models.EmailField(blank=True, help_text='A destination email address for internal emails. eg. UGC content creation notification. Will be ignored for production environment.', max_length=100)),
            ],
        ),
    ]
