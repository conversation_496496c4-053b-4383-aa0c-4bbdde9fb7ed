from django.core.cache import cache
from django.db import models

TO_MAIL_CACHE_KEY = "settings-to-mail"
TO_MAIL_CACHE_TIMEOUT = 60 * 30  # 30 mins


class SettingsManager(models.Manager["Settings"]):
    def get_settings(self):
        return self.get_queryset().first()


class Settings(models.Model):
    to_email = models.EmailField(
        blank=True,
        help_text=(
            "A destination email address for internal emails. eg. UGC content creation notification."
            " Will be ignored for production environment."
        ),
        max_length=100,
    )

    objects = SettingsManager()

    class Meta:
        verbose_name_plural = "Settings"

    @staticmethod
    def get_to_email():
        to_email = cache.get(TO_MAIL_CACHE_KEY)
        if to_email is None:
            settings = Settings.objects.get_settings()
            to_email = settings.to_email if settings else ""
            cache.set(TO_MAIL_CACHE_KEY, to_email, TO_MAIL_CACHE_TIMEOUT)
        return to_email

    def delete(self, *args, **kwargs):
        super().delete(*args, **kwargs)
        self.clear_cache()

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        self.clear_cache()

    def clear_cache(self):
        cache.get(TO_MAIL_CACHE_KEY)
