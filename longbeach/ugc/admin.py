from django.contrib.gis import admin
from reversion_compare.admin import CompareVersionAdmin

from .models import (
    UGCCategory,
    UGCImage,
    UGCMasthead,
    UGCNotification,
    UGCOrganiser,
    UGCUserProfile,
    UserGeneratedContent,
)


@admin.register(UGCMasthead)
class UGCMastheadAdmin(admin.ModelAdmin):
    list_display = ("site_id", "site_name")
    search_fields = ("site_id",)
    readonly_fields = ("site_name",)

    def site_name(self, obj):
        return obj.site_name

    site_name.short_description = "Site Name"  # type: ignore[attr-defined]


@admin.register(UGCUserProfile)
class UGCUserProfileAdmin(admin.ModelAdmin):
    list_display = ("piano_user_id", "user_name", "user_email")
    search_fields = ("user_email",)
    readonly_fields = ("user_email", "piano_user_id")


class UserInline(admin.TabularInline):
    model = UGCUserProfile


@admin.register(UGCOrganiser)
class UGCOrganiserAdmin(admin.ModelAdmin):
    list_display = ("name", "email")
    search_fields = ("name",)
    inlines = (UserInline,)


@admin.register(UGCCategory)
class UGCCategoryAdmin(admin.ModelAdmin):
    list_display = ("content_type", "name", "slug")
    search_fields = ("name",)


class ImageInLine(admin.TabularInline):
    model = UGCImage


class NotificationInLine(admin.TabularInline):
    model = UGCNotification
    readonly_fields = ("created_on",)


@admin.register(UserGeneratedContent)
class UserGeneratedContentAdmin(CompareVersionAdmin):
    list_display = (
        "title",
        "category",
        "location",
        "start_datetime",
        "end_datetime",
        "status",
        "created_on",
    )
    list_filter = ("status", "start_datetime", "created_on")
    search_fields = ("title", "content_type")
    date_hierarchy = "start_datetime"
    ordering = ("-created_on",)
    inlines = (ImageInLine, NotificationInLine)
    readonly_fields = ("created_on", "recurrence_text", "location_point")
