{% csrf_token %}
{% if form.non_field_errors %}
  <div class="alert alert-error">
    <ul>
      {% for error in form.non_field_errors %}
        <li>{{ error }}</li>
      {% endfor %}
    </ul>
  </div>
{% endif %}
{% for field in form.hidden_fields %}
  {{ field }}
{% endfor %}
{% for field in form.visible_fields %}
  <div class="control-group{% if field.errors %} error{% endif %}">
    {% if field.field.widget.input_type != "checkbox" and field.field.widget.input_type != "radio" %}
      <label class="control-label" for="{{ field.id_for_label }}">
        {{ field.label }}
        {% if field.field.required %}
          <span class="text-error">*</span>
        {% endif %}
      </label>
    {% endif %}
    <div class="controls">
      {% if field.field.widget.input_type == "checkbox" or field.field.widget.input_type == "radio" %}
        <label class="{{ field.field.widget.input_type }}" for="{{ field.id_for_label }}">
          {{ field }}
          {{ field.label }}
          {% if field.field.required %}
            <span class="text-error">*</span>
          {% endif %}
        </label>
      {% else %}
        {{ field }}
      {% endif %}
      {% if field.help_text %}
        <p class="help-block">{{ field.help_text }}</p>
      {% endif %}
      {% for error in field.errors %}
        <span class="help-inline">{{ error }}</span>
      {% endfor %}
    </div>
  </div>
{% endfor %}
