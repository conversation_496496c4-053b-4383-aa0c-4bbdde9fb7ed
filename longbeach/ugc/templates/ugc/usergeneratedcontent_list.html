{% extends "base_no_sidebar.html" %}
{% load search_tags %}
{% block title %}Moderation | {{ block.super }}{% endblock %}

{% block head_styles %}
  {{ block.super }}
  <style>
    .modal textarea {
      width: 97%;
    }
  </style>
{% endblock %}

{% block base_content %}
  <div class="container">
    <h1>Contributed content moderation</h1>
    <ul class="nav nav-tabs">
      <li class="active">
        <a href="{% url 'ugc_list' %}">User Content</a>
      </li>
      <li>
        <a href="{% url 'ugc_user_list' %}">Users</a>
      </li>
    </ul>
    <form action="" class="form-inline">
      {{ form }}
      <button class="btn btn-primary" type="submit">Filter</button>
      {% if form.cleaned_data %}
        <a class="btn btn-default" href="{% url 'ugc_list' %}">Show all</a>
      {% endif %}
    </form>
    {% if page_obj %}
      <form action="" class="form-inline" method="POST">
        <div style="padding-bottom:1rem">
          {% csrf_token %}
          <strong>With selected:</strong> set status
          <button class="btn btn-default" disabled name="status" value="pending">
            Pending
          </button>
          <button class="btn btn-default" disabled name="status" value="approved">
            Approve
          </button>
          <button class="btn btn-default" disabled name="status" value="rejected">
            Reject
          </button>
        </div>
        <table class="table table-bordered table-striped">
          <thead>
            <tr>
              <th style="width:0.8rem;padding-top: 0.3rem; padding-bottom: 0; vertical-align: top;">
                <input id="check-all" type="checkbox" />
              </th>
              <th style="width:7rem">Date</th>
              <th style="width:3rem">Status</th>
              <th style="width:5rem">Content type</th>
              <th>Author</th>
              <th>Title</th>
              <th style="width:10rem">Masthead</th>
              <th style="width:4rem">Action</th>
            </tr>
          </thead>
          <tbody>
            {% for ugc in page_obj %}
              <tr data-id="{{ ugc.pk }}">
                <td style="width:0.8rem;padding-top: 0.3rem; padding-bottom: 0; vertical-align: top;"><input name="ugc" type="checkbox" value="{{ ugc.pk }}" /></td>
                <td>{{ ugc.created_on|date:'j/n/y g:i A' }}</td>
                <td>
                  <span class="ugc-status label {{ ugc.get_status_classnames }}">
                    {{ ugc.get_status_display }}
                  </span>
                </td>
                <td>{{ ugc.get_content_type_display }}</td>
                <td>
                  <a href="{% url 'ugc_user_update' ugc.user_profile_id %}">
                    {{ ugc.user_profile.user_name|default:'(unnamed user)' }}
                  </a>
                </td>
                <td><a href="{% url 'ugc_change' ugc.pk %}">{{ ugc.title }}</a></td>
                <td>{{ ugc.masthead.site_name }}</td>
                <td>
                  <div class="btn-group">
                    <a class="btn btn-mini dropdown-toggle" id="dropdown-label-{{ ugc.pk }}" data-toggle="dropdown" href="#">
                      Action
                      <span class="caret"></span>
                    </a>
                    <ul class="dropdown-menu" role="menu" aria-labelledby="dropdown-label-{{ ugc.pk }}">
                      <li>
                        <a href="{% url 'ugc_change' ugc.pk %}" tabindex="-1">
                          <i class="icon-edit"></i>
                          Edit
                        </a>
                      </li>
                      {% if ugc.status != 'approved' %}
                        <li>
                          <a data-action="approve" tabindex="-1" href="#">
                            <i class="icon-ok"></i>
                            Approve
                          </a>
                        </li>
                      {% endif %}
                      {% if ugc.status != 'rejected' %}
                        <li>
                          <a tabindex="-1" href="{{ ugc.detail_page_url }}" target="_blank">
                            <i class="icon-eye-open"></i>
                            View
                          </a>
                        </li>
                        <li>
                          <a data-action="reject" tabindex="-1" href="#">
                            <i class="icon-remove"></i>
                            Reject
                          </a>
                        </li>
                      {% endif %}
                      <li>
                        <a data-action="report" tabindex="-1" href="#">
                          <i class="icon-flag"></i>
                          Reject &amp; ban
                        </a>
                      </li>
                    </ul>
                  </div>
                </td>
              </tr>
            {% endfor %}
          </tbody>
        </table>
      </form>
    {% endif %}
  </div>

    {% if not page_obj %}
      <div class="alert alert-info">
        <span class="icon-exclamation-sign"></span>
        No results
      </div>
    {% endif %}

    {% include 'ugc/_pagination.html' %}

  <form action="" class="modal hide fade" method="POST" tabindex="-1" role="dialog" aria-labelledby="modal-header" aria-hidden="true">
    <div class="modal-header" id="modal-header">
      <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
        &times;
      </button>
      <h3>Update content status</h3>
    </div>
    <div class="modal-body">
      {% include "ugc/_form.html" with form=action_form %}
    </div>
    <div class="modal-footer">
      <button class="btn" data-dismiss="modal" aria-hidden="true" type="button">Cancel</button>
      <button class="btn btn-primary" type="submit">Update status</button>
    </div>
  </form>
{% endblock %}

{% block body_scripts %}
  {{ block.super }}
  <script>
    const $bulkStatus = $('button[name=status]');
    const $checkboxes = $('td input[type=checkbox]').on('click', () => {
      const count = $checkboxes.filter(':checked').length;
      $checkAll.prop('checked', count === $checkboxes.length);
      $checkAll.prop('indeterminate', count && count !== $checkboxes.length);
      $bulkStatus.prop('disabled', count === 0);
    });
    const $checkAll = $('#check-all').on('change', () => {
      const isChecked = $checkAll.prop('checked');
      $checkboxes.prop('checked', isChecked);
      $bulkStatus.prop('disabled', !isChecked);
    });

    const csrfmiddlewaretoken = $('input[name=csrfmiddlewaretoken]').val();
    const labelClasses = {
      approved: 'label-success',
      rejected: 'label-important',
    };
    const $modal = $('.modal');
    const $modalId = $modal.find('input[name=id]');

    $('a[data-action]').click(function (event) {
      event.preventDefault();
      const $this = $(this);
      const action = $this.data('action');
      const $parent = $this.parents('tr');
      const id = $parent.data('id');

      new Promise((resolve, reject) => {
        $modalId.val(id);
        $modal.modal();
        $modal.on('submit', (event) => {
          event.preventDefault();
          $modal.off('submit');
          resolve($('#id_note').val());
          $modal.modal('hide');
        });
        $modal.on('hide', () => {
          // Clear the form
          $modal[0].reset();
          $modal.off('submit');
        });
      }).then((note) => {
        $.post('/manage/ugc/action/', {
          action,
          csrfmiddlewaretoken,
          id,
          note,
        }).then(() => {
          window.location.reload();
        });
      });
    });
  </script>
{% endblock %}
