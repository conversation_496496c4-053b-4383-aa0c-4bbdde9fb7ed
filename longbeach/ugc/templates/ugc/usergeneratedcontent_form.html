{% extends "base_no_sidebar.html" %}

{% block title %}{{ object }} | Moderation | {{ block.super }}{% endblock %}

{% block head_styles %}
  {{ block.super }}
  <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
  <script src="{% url 'jsi18n' %}"></script>
  <style>
    input[type=text], textarea {
      width: 30rem;
    }

    select {
      width: 30.9rem;
    }
  </style>
  {{ form.media.css }}
{% endblock %}

{% block base_content %}
  <div class="container" id="container">
    <a href="{% url 'ugc_list' %}">
      <span class="icon-chevron-left"></span>
      Back
    </a>
    <form action="{% url 'ugc_action' %}" class="pull-right" method="post">
      {% csrf_token %}
      <input name="id" type="hidden" value="{{ object.pk }}">
      <input id="action-note" name="note" type="hidden" value="">
      <button class="btn btn-success btn-large" name="action" type="submit" value="approve">
        Approve
      </button>
      <button class="btn btn-default btn-large" name="action" type="submit" value="reject">
        Reject
      </button>
      <button class="btn btn-danger btn-large" name="action" type="submit" value="report">
        Reject &amp; ban
      </button>
    </form>
    <h1>Edit {{ object }}</h1>
    <form action="" method="post" class="form-horizontal" enctype="multipart/form-data">
      {% if compare_url %}
      <div class="controls" style="padding-top:10px; padding-bottom: 10px;">
         <strong>The owner made changes to this previously approved content.
          <a href="{{ compare_url }}" target="_blank">Click here</a> to see what's changed.
        </strong>
      </div>
      {% endif %}

      <div class="control-group">
        <span class="control-label">URL</span>
        <div class="controls" style="padding-top:5px">
          {% if object.status == object.APPROVED %}
            <a href="{{ object.detail_page_url }}" target="_blank">
              {{ object.detail_page_url }}
            </a>
          {% else %}
            URL will be displayed once the contribution has been approved
          {% endif %}
        </div>
      </div>

      {% include "ugc/_form.html" %}

      <h2>Images</h2>
      {% include "ugc/_formset.html" with formset=image_formset %}

      {% if object.content_type == 'event' %}
        <h2>Organiser</h2>
        {{ organiser_form.as_p }}
      {% endif %}

      <div class="form-actions">
        <a class="btn" href="{% url 'ugc_list' %}">
          Cancel
        </a>
        <button class="btn btn-primary" type="submit">Save</button>
      </div>
    </form>
  </div>
{% endblock %}

{% block body_scripts %}
  {{ block.super }}
  <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
  <script>
    $(() => {
      $('#id_user_profile').select2({
        ajax: {
          url: '{% url "ugc_user_list" %}',
        },
      });

      const $note = $('#id_note').on('input', (e) => {
        $('#action-note').val(e.target.value);
      }).trigger('input');

    });


  </script>
  {{ form.media.js }}
  {% endblock %}
