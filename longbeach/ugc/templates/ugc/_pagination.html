{% load search_tags %}
{% if page_obj.has_previous or page_obj.has_next %}
  <div class="pagination pagination-centered">
    <ul>
      {% if page_obj.has_previous %}
        <li>
          <a href="{% if page_obj.previous_page_number > 1 %}{% page_url page=page_obj.previous_page_number %}{% else %}{% page_url delete page %}{% endif %}" class="prev">◄</a>
        </li>
      {% endif %}
      {% paginator_page_list as pages %}
      {% for page in pages %}
        <li{% if page.is_current %} class="active"{% endif %}>
          <a href="{{ page.url }}">{{ page.number }}</a>
        </li>
      {% endfor %}
      {% if page_obj.has_next %}
        <li>
          <a href="{% page_url page=page_obj.next_page_number %}">►</a>
        </li>
      {% endif %}
    </ul>
  </div>
{% endif %}
