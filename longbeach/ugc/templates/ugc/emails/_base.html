<!doctype html>
<html lang="en">
  <head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>{{ subject }}</title>
    <style media="all" type="text/css">
      @media only screen and (max-width: 640px) {
        body,
        .body {
          background-color: #fff !important;
        }

        .main p,
        .main td,
        .main span {
          font-size: 16px !important;
        }

        .wrapper {
          padding: 8px !important;
        }

        .content {
          padding: 0 !important;
        }

        .container {
          padding: 0 !important;
          padding-top: 8px !important;
          width: 100% !important;
        }

        .main {
          border-left-width: 0 !important;
          border-radius: 0 !important;
          border-right-width: 0 !important;
        }

        .note {
          background: #f3f4f6;
          font-weight: normal !important;
          padding: 10px;
        }

        .community {
          width: 100% !important;
          margin: 50px 0 !important;
        }

        .btn table {
          max-width: 100% !important;
          width: 100% !important;
        }

        .btn a {
          font-size: 16px !important;
          max-width: 100% !important;
          width: 100% !important;
        }
      }
      @media all {
        .ExternalClass {
          width: 100%;
        }

        .ExternalClass,
        .ExternalClass p,
        .ExternalClass span,
        .ExternalClass font,
        .ExternalClass td,
        .ExternalClass div {
          line-height: 100%;
        }

        .apple-link a {
          color: inherit !important;
          font-family: inherit !important;
          font-size: inherit !important;
          font-weight: inherit !important;
          line-height: inherit !important;
          text-decoration: none !important;
        }

        #MessageViewBody a {
          color: inherit;
          text-decoration: none;
          font-size: inherit;
          font-family: inherit;
          font-weight: inherit;
          line-height: inherit;
        }
      }
    </style>
  </head>
  <body
    style="
      font-family: Helvetica, sans-serif;
      -webkit-font-smoothing: antialiased;
      font-size: 14px;
      line-height: 1.3;
      -ms-text-size-adjust: 100%;
      -webkit-text-size-adjust: 100%;
      background-color: #f4f3f3;
      margin: 0;
      padding: 0;
    "
  >
    <table
      role="presentation"
      border="0"
      cellpadding="0"
      cellspacing="0"
      class="body"
      style="
        border-collapse: separate;
        mso-table-lspace: 0pt;
        mso-table-rspace: 0pt;
        background-color: #f4f3f3;
        width: 100%;
      "
      width="100%"
      bgcolor="#f4f5f6"
    >
      <tr>
        <td
          style="
            font-family: Helvetica, sans-serif;
            font-size: 14px;
            vertical-align: top;
          "
          valign="top"
        >
          &nbsp;
        </td>
        <td
          class="container"
          style="
            font-family: Helvetica, sans-serif;
            font-size: 14px;
            vertical-align: top;
            max-width: 600px;
            padding: 0;
            padding-top: 24px;
            width: 600px;
            margin: 0 auto;
          "
          width="600"
          valign="top"
        >
          <div
            class="content"
            style="
              box-sizing: border-box;
              display: block;
              margin: 0 auto;
              max-width: 600px;
              padding: 0;
            "
          >
            <span
              class="preheader"
              style="
                color: transparent;
                display: none;
                height: 0;
                max-height: 0;
                max-width: 0;
                opacity: 0;
                overflow: hidden;
                mso-hide: all;
                visibility: hidden;
                width: 0;
              "
              >{% block preheader %}{% endblock %}</span
            >
            <table
              role="presentation"
              border="0"
              cellpadding="0"
              cellspacing="0"
              class="main"
              style="
                border-collapse: separate;
                mso-table-lspace: 0pt;
                mso-table-rspace: 0pt;
                background: #ffffff;
                width: 100%;
              "
              width="100%"
            >
              <tr>
                <td
                  class="wrapper"
                  style="
                    font-family: Helvetica, sans-serif;
                    font-size: 14px;
                    vertical-align: top;
                    box-sizing: border-box;
                    padding: 24px;
                  "
                  valign="top"
                >
                  {% block content %}{% endblock %}
                  {% if not omit_signature %}
                  <p
                    style="
                      font-family: Helvetica, sans-serif;
                      font-size: 14px;
                      font-weight: normal;
                      margin: 0;
                      margin-bottom: 16px;
                    "
                  >
                    Best regards,<br />
                    The Noticeboard Team
                    {% if noticeboard_email %}
                    &bull;
                    <a
                      href="mailto:{{ noticeboard_email }}"
                      style="
                        cursor: pointer;
                        display: inline;
                        font-weight: normal;
                        text-decoration: underline;
                        color: #1D1D1D;
                      "
                    >{{ noticeboard_email }}</a>
                    {% endif %}
                  </p>
                  {% endif %}
                  {% block content_end %}{% endblock %}
                  {% if not omit_signature %}
                  <p
                    style="
                      color: #353535;
                      font-family: Helvetica, sans-serif;
                      font-size: 12px;
                      font-weight: normal;
                      margin: 0;
                      margin-bottom: 16px;
                      text-align: center;
                    "
                  >
                    &copy; {% now 'Y' %} Australian Community Media<br />
                    <span
                      class="apple-link"
                      style="font-size: 12px; text-align: center"
                      >159 Bells Line of Road, North Richmond, NSW (2754)
                      Aus</span
                    >
                  </p>
                  {% endif %}
                </td>
              </tr>
            </table>
          </div>
        </td>
        <td
          style="
            font-family: Helvetica, sans-serif;
            font-size: 14px;
            vertical-align: top;
          "
          valign="top"
        >
          &nbsp;
        </td>
      </tr>
    </table>
  </body>
</html>
