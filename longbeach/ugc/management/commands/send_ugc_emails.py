import logging

from django.conf import settings
from django.core.management.base import BaseCommand

from longbeach.ugc.models import UserGeneratedContent

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Sends notification summary and reminder emails for UGC submissions"

    def send_ugc_event_reminder_emails(self) -> None:
        """Send reminder emails to UGC Event contributors before their event goes live."""

        ugc_items = UserGeneratedContent.objects.upcoming_events()  # type: ignore[attr-defined]

        for ugc_item in ugc_items.iterator():
            try:
                ugc_item.send_upcoming_event_email()
            except Exception:
                if settings.ENVIRONMENT != "production":
                    raise
                logger.exception("Failed to send upcoming UGC event email")

    def send_follow_up_emails(self) -> None:
        """Send follow-up emails to UGC contributors after their content goes live."""

        ugc_items = UserGeneratedContent.objects.follow_ups()  # type: ignore[attr-defined]

        for ugc_item in ugc_items.iterator():
            try:
                ugc_item.send_follow_up_email()
            except Exception:
                if settings.ENVIRONMENT != "production":
                    raise
                logger.exception("Failed to send upcoming UGC follow up email")

    def handle(self, *args, **options):
        self.stdout.write(self.style.NOTICE("Running send_ugc_emails"))

        self.send_ugc_event_reminder_emails()
        self.send_follow_up_emails()
