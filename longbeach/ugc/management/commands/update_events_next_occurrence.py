import logging

from django.core.management.base import BaseCommand
from django.db import transaction

from longbeach.ugc.models import ContentType, UserGeneratedContent

logger = logging.getLogger(__name__)

BATCH_SIZE = 200


class Command(BaseCommand):
    help = (
        "Updates the next_occurrence field for UGC events based on their recurrence field. "
        "Defaults to start_datetime or current date if event is non-recurring."
    )

    def handle(self, *args, **options):
        processed_count = 0

        qs = UserGeneratedContent.objects.filter(
            content_type=ContentType.EVENT,
            start_datetime__isnull=False,
            status__in=[
                UserGeneratedContent.PENDING,
                UserGeneratedContent.APPROVED,
            ],
        ).iterator(chunk_size=BATCH_SIZE)

        for ugc in qs:
            try:
                next_occurrence = ugc.set_next_occurrence()
                logger.info(f"Updated UGC event {ugc.id}: {next_occurrence}")

                processed_count += 1
                if processed_count % BATCH_SIZE == 0:
                    transaction.commit()
                    logger.info(
                        f"Processed {processed_count} UGC events so far."
                    )

            except Exception as e:
                logger.error(f"Error processing UGC event {ugc.id}: {e}")

        logger.info("Finished updating UGC event next_occurrence dates.")
