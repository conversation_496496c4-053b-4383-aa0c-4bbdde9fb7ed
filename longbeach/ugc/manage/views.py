from django import forms
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.messages.views import SuccessMessageMixin
from django.db.models import F
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, redirect
from django.urls import reverse_lazy
from django.views.generic import ListView, UpdateView
from django.views.generic.edit import FormMixin
from reversion.models import Version

from longbeach.ugc.models import (
    ContentType,
    UGCCategory,
    UGCUserProfile,
    UserGeneratedContent,
)
from longbeach.utils.search import term_to_query

from .forms import (
    UGCImageFormset,
    UGCOrganiserForm,
    UserGeneratedContentActionForm,
    UserGeneratedContentFilterForm,
    UserGeneratedContentForm,
)


class UserGeneratedContentListView(LoginRequiredMixin, FormMixin, ListView):
    form_class = UserGeneratedContentFilterForm
    model = UserGeneratedContent
    paginate_by = 50
    ordering = "-created_on"

    def get_context_data(self):
        kwargs = super().get_context_data()
        kwargs["action_form"] = UserGeneratedContentActionForm()
        return kwargs

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        if self.request.GET:
            kwargs["data"] = self.request.GET
        return kwargs

    def get_queryset(self):
        queryset = (
            super().get_queryset().select_related("masthead", "user_profile")
        )
        form = self.get_form()

        if form.is_valid():
            queryset = queryset.filter(
                **{k: v for k, v in form.cleaned_data.items() if v},
            )

        return queryset

    def post(self, request):
        """Bulk update status on UGC."""
        status = request.POST.get("status")

        if status not in [s[0] for s in UserGeneratedContent.STATUS_CHOICES]:
            return HttpResponse(status=400)

        ids = []

        for pk in request.POST.getlist("ugc"):
            try:
                pk = int(pk)
            except ValueError:
                continue

            ids.append(pk)

        content = UserGeneratedContent.objects.filter(pk__in=ids)

        if status == UserGeneratedContent.PENDING:
            content.update(status=status)
        else:
            # Edit individually to trigger emails to users
            for ugc in content.iterator():
                ugc.set_status(status)

        return redirect("ugc_list")


class UserGeneratedContentUpdateView(
    LoginRequiredMixin, UpdateView, SuccessMessageMixin
):
    fields = (
        "category",
        "masthead",
        "user_profile",
        "organiser",
        "title",
        "description",
        "location",
        "start_datetime",
        "end_datetime",
        "status",
    )
    model = UserGeneratedContent
    success_message = "UGC updated"
    success_url = reverse_lazy("ugc_list")

    def get_context_data(self, **kwargs):
        kwargs = super().get_context_data(**kwargs)

        kwargs["initial_status"] = self.initial_status

        ugc = self.object
        versions = Version.objects.get_for_object(self.object)
        if len(versions) >= 2:
            latest_status = versions[0].field_dict["status"]
            previous_status = versions[1].field_dict["status"]
            if (
                previous_status == UserGeneratedContent.APPROVED
                and latest_status == UserGeneratedContent.PENDING
            ):
                kwargs["compare_url"] = (
                    f"/admin/ugc/usergeneratedcontent/{ugc.pk}/history/compare/?version_id2={versions[1].id}&version_id1={versions[0].id}"
                )

        if self.request.method == "POST":
            kwargs["image_formset"] = UGCImageFormset(
                self.request.POST,
                self.request.FILES,
                instance=kwargs["object"],
            )
        else:
            kwargs["image_formset"] = UGCImageFormset(
                instance=kwargs["object"],
            )

        if self.object.content_type == ContentType.EVENT:
            organiser = self.object.organiser
            if self.request.method == "POST":
                kwargs["organiser_form"] = UGCOrganiserForm(
                    self.request.POST, self.request.FILES, instance=organiser
                )
            else:
                kwargs["organiser_form"] = UGCOrganiserForm(instance=organiser)

        return kwargs

    def get_form(self, form_class=None):
        form = super().get_form(UserGeneratedContentForm)

        form.fields["note"] = forms.CharField(
            help_text="This will be sent to the user.",
            widget=forms.Textarea,
            required=False,
        )
        form.fields["category"].queryset = UGCCategory.objects.filter(
            content_type=self.object.content_type
        )
        if self.object.content_type != ContentType.EVENT:
            form.fields.pop("organiser", None)
            form.fields.pop("ical_string", None)

        return form

    def get_object(self, queryset=None):
        """Store the initial status to handle when it changes."""
        obj = super().get_object(queryset)

        self.initial_status = obj.status

        return obj

    def form_valid(self, form):
        """Send an email if the status changed and save images."""
        context = self.get_context_data()

        image_formset = context["image_formset"]

        if image_formset.is_valid():
            image_formset.save()
        else:
            messages.error(
                self.request,
                "There was an error saving the images. Please review and try again.",
            )
            return super().form_invalid(form)

        if self.object.content_type == ContentType.EVENT:
            organiser_form = context["organiser_form"]
            if organiser_form.is_valid():
                organiser_form.save()
            else:
                messages.error(
                    self.request,
                    "There was an error saving the organiser. Please review and try again.",
                )
                return super().form_invalid(form)

        new_status = self.object.status

        if new_status != self.initial_status:
            self.object.set_status(
                new_status,
                form.cleaned_data["note"],
            )

        messages.success(self.request, "Changes saved successfully.")
        return super().form_valid(form)


@login_required
def action(request):
    try:
        pk = request.POST["id"]
    except KeyError:
        return HttpResponse(status=404)

    ugc = get_object_or_404(UserGeneratedContent, pk=pk)

    try:
        user_action = request.POST["action"]
    except KeyError:
        return HttpResponse(status=400)

    note = request.POST["note"]

    if (
        user_action == "approve"
        and ugc.status != UserGeneratedContent.APPROVED
    ):
        ugc.set_status(UserGeneratedContent.APPROVED, note)
    elif (
        user_action == "reject" and ugc.status != UserGeneratedContent.REJECTED
    ):
        ugc.set_status(UserGeneratedContent.REJECTED, note)
    elif user_action == "report":
        if ugc.user_profile:
            ugc.user_profile.ban()
        if ugc.status != UserGeneratedContent.REJECTED:
            ugc.set_status(UserGeneratedContent.REJECTED, note)

    else:
        if request.is_ajax():
            return HttpResponse()

        return redirect("ugc_list")

    ugc.save()

    if request.is_ajax():
        return JsonResponse(
            data={
                "status": ugc.status,
            },
        )

    return redirect("ugc_list")


class UserListView(LoginRequiredMixin, ListView):
    model = UGCUserProfile
    paginate_by = 50

    def get(self, request, *args, **kwargs):
        """Search for select2 via Ajax."""
        if request.is_ajax():
            queryset = self.get_queryset().values(
                id=F("pk"), text=F("user_name")
            )

            if term := request.GET.get("q", "").strip():
                queryset = queryset.filter(term_to_query(term, ("user_name",)))

            return JsonResponse(
                {
                    "results": tuple(queryset),
                },
            )

        return super().get(request, *args, **kwargs)


class UserUpdateView(LoginRequiredMixin, UpdateView):
    model = UGCUserProfile
    fields = (
        "user_name",
        "user_email",
        "description",
        "avatar",
        "is_active",
    )
    success_url = reverse_lazy("ugc_user_list")

    def get_object(self, queryset=None):
        """Record the initial active status to email if banned."""
        obj = super().get_object(queryset)
        obj.initial_is_active = obj.is_active
        return obj

    def form_valid(self, form):
        """Email the user if they have been banned."""
        if (
            self.object.initial_is_active
            and not form.cleaned_data["is_active"]
        ):
            # The user is saved below; avoid saving twice
            self.object.ban(save=False)

        return super().form_valid(form)
