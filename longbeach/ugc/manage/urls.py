from django.urls import re_path

from . import views

urlpatterns = [
    re_path(
        r"^$",
        views.UserGeneratedContentListView.as_view(),
        name="ugc_list",
    ),
    re_path(
        r"^(?P<pk>\d+)/$",
        views.UserGeneratedContentUpdateView.as_view(),
        name="ugc_change",
    ),
    re_path(r"^action/$", views.action, name="ugc_action"),
    re_path(r"^users/$", views.UserListView.as_view(), name="ugc_user_list"),
    re_path(
        r"^users/(?P<pk>[a-zA-Z0-9-]+)/$",
        views.UserUpdateView.as_view(),
        name="ugc_user_update",
    ),
]
