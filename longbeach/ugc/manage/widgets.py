from django import forms
from django.utils.html import format_html


class HtmlEditorWidget(forms.Textarea):
    class Media:
        css = {"all": ["ugc/manage/html-editor-widget.css"]}
        js = (
            "tiptap/tiptap-bundle.js",
            "ugc/manage/html-editor-widget.js",
        )

    def render(self, name, value, attrs=None, renderer=None):
        required_attrib = 'required="" ' if self.is_required else ""
        return format_html(
            f'<textarea id="id_{name}" name="{name}" {required_attrib} '
            f'style="display: none">{value}</textarea>',
        )
