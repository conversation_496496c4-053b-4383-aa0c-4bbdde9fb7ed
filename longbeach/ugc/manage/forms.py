import logging

import recurrence
from bleach import clean
from django import forms
from django.core.exceptions import ValidationError
from django.forms import TextInput
from recurrence.exceptions import DeserializationError

from longbeach.ugc.models import (
    ContentType,
    UGCImage,
    UGCMasthead,
    UGCOrganiser,
    UGCUserProfile,
    UserGeneratedContent,
)

from .widgets import HtmlEditorWidget

logger = logging.getLogger(__name__)


class UserGeneratedContentForm(forms.ModelForm):
    ical_string = forms.CharField(
        required=False,
        label="Rule override",
        widget=forms.TextInput(),
    )

    class Meta:
        model = UserGeneratedContent
        fields = "__all__"
        widgets = {"description": HtmlEditorWidget()}

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["location_point"].widget = TextInput(
            attrs={"readonly": "readonly"}
        )

        if self.instance and self.instance.pk and self.instance.recurrences:
            current_rule = str(self.instance.recurrences)
            self.fields[
                "ical_string"
            ].help_text = f"Edit with caution. Current rule: {current_rule}"

    def clean_ical_string(self):
        ical_string = self.cleaned_data.get("ical_string")
        if ical_string:
            try:
                recurrence.deserialize(ical_string)
            except DeserializationError as e:
                logger.exception(
                    f"Exception type: {type(e).__name__} - {str(e)}"
                )
                raise ValidationError(f"Invalid rule string: {e}")
            except Exception as e:
                logger.exception(
                    f"Unexpected error parsing rule string: {str(e)}"
                )
                raise ValidationError(
                    "Unexpected error while parsing rule string. Please check the syntax and try again."
                )
        return ical_string

    def clean_description(self):
        description = clean(
            self.cleaned_data.get("description", ""),
            tags={"a", "b", "em", "li", "p", "strong", "ul"},
            strip=True,
        )
        if not description:
            raise ValidationError("Description empty after sanitizing.")
        return description

    def save(self, commit=True):
        instance = super().save(commit=False)

        current_recurrence_rule = self.cleaned_data.get("recurrences", None)
        ical_string = self.cleaned_data.get("ical_string")
        if ical_string:
            instance.recurrences = recurrence.deserialize(ical_string)
        elif current_recurrence_rule:
            instance.recurrences = current_recurrence_rule

        if commit:
            instance.save()
        return instance


class UserGeneratedContentFilterForm(forms.Form):
    masthead = forms.ModelChoiceField(
        UGCMasthead.objects.all(), required=False, empty_label="All"
    )
    content_type = forms.ChoiceField(
        choices=(
            (None, "All"),
            *ContentType.choices,
        ),
        required=False,
    )
    user_profile = forms.ModelChoiceField(
        UGCUserProfile.objects.all(), required=False, empty_label="All"
    )


class UserGeneratedContentActionForm(forms.Form):
    id = forms.IntegerField(widget=forms.HiddenInput)
    note = forms.CharField(
        required=False,
        help_text="This will be sent to the user.",
        widget=forms.Textarea,
    )


UGCImageFormset = forms.inlineformset_factory(  # type: ignore[var-annotated]
    UserGeneratedContent,
    UGCImage,
    fields=("image",),
)


class UGCOrganiserForm(forms.ModelForm):
    class Meta:
        model = UGCOrganiser
        fields = ("name", "email", "contact_number", "website_url", "logo")
