# Generated by Django 3.1.14 on 2024-09-02 03:32

import autoslug.fields
from django.db import migrations, models
import django.db.models.deletion
import valencia_storage.storage


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='UGCCategory',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content_type', models.CharField(blank=True, choices=[('event', 'Event'), ('story', 'Story'), ('photos', 'Photos')], max_length=100)),
                ('name', models.CharField(db_index=True, max_length=100)),
                ('slug', autoslug.fields.AutoSlugField(blank=True, default='', editable=False, max_length=100, populate_from='name')),
            ],
            options={
                'verbose_name': 'Category',
                'verbose_name_plural': 'Categories',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='UGCMasthead',
            fields=[
                ('site_id', models.IntegerField(primary_key=True, serialize=False, unique=True)),
            ],
        ),
        migrations.CreateModel(
            name='UGCUserProfile',
            fields=[
                ('piano_user_id', models.CharField(blank=True, max_length=100, primary_key=True, serialize=False, unique=True)),
                ('user_name', models.CharField(blank=True, max_length=100)),
                ('user_email', models.EmailField(blank=True, max_length=100, unique=True)),
            ],
            options={
                'verbose_name': 'User Profile',
            },
        ),
        migrations.CreateModel(
            name='UserGeneratedContent',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content_type', models.CharField(blank=True, choices=[('event', 'Event'), ('story', 'Story'), ('photos', 'Photos')], max_length=100)),
                ('title', models.CharField(max_length=100)),
                ('logo', models.ImageField(blank=True, storage=valencia_storage.storage.ValenciaStorage(), upload_to='images')),
                ('location', models.CharField(blank=True, max_length=100)),
                ('start_datetime', models.DateTimeField(blank=True)),
                ('end_datetime', models.DateTimeField(blank=True)),
                ('description', models.TextField(max_length=300)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected')], default='pending', max_length=20)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ugc', to='ugc.ugccategory')),
                ('masthead', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ugc.ugcmasthead')),
                ('user_profile', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ugc.ugcuserprofile')),
            ],
            options={
                'verbose_name': 'User Generated Content',
                'verbose_name_plural': 'User Generated Content',
                'ordering': ['created_on'],
            },
        ),
        migrations.CreateModel(
            name='UGCImage',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(blank=True, storage=valencia_storage.storage.ValenciaStorage(), upload_to='images')),
                ('ugc', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='ugc.usergeneratedcontent')),
            ],
        ),
    ]
