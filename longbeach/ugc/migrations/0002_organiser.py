# Generated by Django 3.1.14 on 2024-10-04 02:27

from django.db import migrations, models
import django.db.models.deletion
import valencia_storage.storage


class Migration(migrations.Migration):

    dependencies = [
        ('ugc', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='UGCOrganiser',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(blank=True, max_length=100)),
                ('email', models.EmailField(blank=True, max_length=100, unique=True)),
                ('contact_number', models.CharField(blank=True, max_length=100)),
                ('website_url', models.CharField(blank=True, max_length=100)),
                ('logo', models.ImageField(blank=True, storage=valencia_storage.storage.ValenciaStorage(), upload_to='images')),
            ],
            options={
                'verbose_name': 'Organiser Profile',
            },
        ),
        migrations.RemoveField(
            model_name='usergeneratedcontent',
            name='logo',
        ),
        migrations.AlterField(
            model_name='usergeneratedcontent',
            name='description',
            field=models.TextField(),
        ),
        migrations.AddField(
            model_name='usergeneratedcontent',
            name='organiser',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ugc.ugcorganiser'),
        ),
    ]
