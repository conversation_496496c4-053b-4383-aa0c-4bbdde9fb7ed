# Generated by Django 3.1.14 on 2025-02-25 04:15

from django.db import migrations, models
import recurrence.fields


def set_next_occurrence_for_events(apps, schema_editor):
    """
    Set next_occurrence to start_datetime for all existing event-type UGC records.
    """
    UserGeneratedContent = apps.get_model('ugc', 'UserGeneratedContent')
    UserGeneratedContent.objects.filter(
        content_type='event',
    ).update(next_occurrence=models.F('start_datetime'))


def reverse_func(apps, schema_editor):
    """
    Reverse migration - set next_occurrence to None for all events
    """
    UserGeneratedContent = apps.get_model('ugc', 'UserGeneratedContent')
    UserGeneratedContent.objects.filter(content_type='event').update(next_occurrence=None)


class Migration(migrations.Migration):

    dependencies = [
        ('ugc', '0011_ugcnotification'),
    ]

    operations = [
        migrations.AddField(
            model_name='usergeneratedcontent',
            name='next_occurrence',
            field=models.DateTimeField(blank=True, null=True, db_index=True),
        ),
        migrations.AddField(
            model_name='usergeneratedcontent',
            name='recurrences',
            field=recurrence.fields.RecurrenceField(blank=True, null=True),
        ),
        migrations.RunPython(
            set_next_occurrence_for_events,
            reverse_func
        ),
    ]
