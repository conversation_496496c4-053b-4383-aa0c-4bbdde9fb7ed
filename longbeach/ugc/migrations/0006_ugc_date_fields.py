# Generated by Django 3.1.14 on 2024-10-04 03:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ugc', '0005_ugcuserprofile_is_active'),
    ]

    operations = [
        migrations.AlterField(
            model_name='usergeneratedcontent',
            name='end_datetime',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='usergeneratedcontent',
            name='start_datetime',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='usergeneratedcontent',
            name='status',
            field=models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected')],
                                   default='pending', help_text='Changing the status will notify the user.', max_length=20),
        ),
    ]
