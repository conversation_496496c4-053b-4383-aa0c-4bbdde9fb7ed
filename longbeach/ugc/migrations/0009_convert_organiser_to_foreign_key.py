from django.db import migrations, models
import django.db.models.deletion


def migrate_organiser_to_userprofile_as_foreign_key(apps, schema_editor):
    UGCOrganiser = apps.get_model('ugc', 'UGCOrganiser')

    for organiser in UGCOrganiser.objects.all().iterator():
        if organiser.user_profile:
            user_profile = organiser.user_profile
            user_profile.organiser = organiser
            user_profile.save()

class Migration(migrations.Migration):

    dependencies = [
        ('ugc', '0008_relate_user_organiser'),
    ]

    operations = [
        migrations.AddField(
            model_name='ugcuserprofile',
            name='organiser',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='user_profiles', to='ugc.ugcorganiser'),
        ),
        migrations.RunPython (migrate_organiser_to_userprofile_as_foreign_key),
        migrations.RemoveField(
            model_name='ugcorganiser',
            name='user_profile',
        ),
    ]
