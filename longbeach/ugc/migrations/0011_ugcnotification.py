# Generated by Django 3.1.14 on 2025-01-29 23:35

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('ugc', '0010_user_profile_order'),
    ]

    operations = [
        migrations.CreateModel(
            name='UGCNotification',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notification_type', models.CharField(choices=[('upcoming_event', 'Upcoming Event'), ('follow_up', 'Follow up')], max_length=100)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('ugc', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to='ugc.usergeneratedcontent')),
            ],
            options={
                'verbose_name': 'Notification',
                'verbose_name_plural': 'Notifications',
                'ordering': ('-created_on', 'id'),
            },
        ),
    ]
