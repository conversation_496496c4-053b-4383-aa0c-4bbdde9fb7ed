# Generated by Django 3.1.14 on 2024-10-21 08:50

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('ugc', '0007_image_max_size'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='usergeneratedcontent',
            options={'ordering': ('-created_on', 'id'), 'verbose_name': 'User Generated Content', 'verbose_name_plural': 'User Generated Content'},
        ),
        migrations.AddField(
            model_name='ugcorganiser',
            name='user_profile',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='organiser_profile', to='ugc.ugcuserprofile'),
        ),
        migrations.AddField(
            model_name='usergeneratedcontent',
            name='published_on',
            field=models.DateTimeField(blank=True, null=True),
        ),
    ]
