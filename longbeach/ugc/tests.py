from datetime import datetime, timedelta
from unittest.mock import Mock, patch

import pytz
from django.contrib.auth import get_user_model
from django.core.management import call_command
from django.shortcuts import reverse
from django.test import TestCase
from django.test.utils import override_settings
from django.utils.timezone import now
from freezegun import freeze_time

from longbeach.ugc.filters import DateRangeOptions, UserGeneratedContentFilter
from longbeach.ugc.models import (
    ContentType,
    NotificationType,
    UGCCategory,
    UGCMasthead,
    UGCOrganiser,
    UGCUserProfile,
    UserGeneratedContent,
)


class UserGeneratedContentTest(TestCase):
    def setUp(self):
        now_ = now()
        user_profile = UGCUserProfile.objects.create(
            description="It's just a matter of time",
            piano_user_id="anakin",
            user_email="<EMAIL>",
            user_name="Anakin Slayer Skywalker",
        )
        masthead = UGCMasthead.objects.create(site_id=317)
        organiser = UGCOrganiser.objects.create(
            name="Darth Vader", email="<EMAIL>"
        )
        category = UGCCategory.objects.create(
            content_type=ContentType.EVENT,
            name="Dark Side",
        )
        event_kwargs = dict(
            category=category,
            content_type=ContentType.EVENT,
            description="<p>RSVP or be choked out</p>",
            location="Tatooine",
            masthead=masthead,
            organiser=organiser,
            title="Storm Trooper Xmas Party",
            user_profile=user_profile,
        )
        self.pending_upcoming_event_1 = UserGeneratedContent.objects.create(
            end_datetime=now_ + timedelta(days=3),
            start_datetime=now_ + timedelta(hours=12),
            status=UserGeneratedContent.PENDING,
            **event_kwargs,
        )
        self.approved_upcoming_event_1 = UserGeneratedContent.objects.create(
            end_datetime=now_ + timedelta(days=3),
            published_on=now_,
            start_datetime=now_ + timedelta(hours=12),
            status=UserGeneratedContent.APPROVED,
            **event_kwargs,
        )
        self.approved_upcoming_event_2 = UserGeneratedContent.objects.create(
            end_datetime=now_ + timedelta(days=3),
            published_on=now_,
            start_datetime=now_ + timedelta(hours=12),
            status=UserGeneratedContent.APPROVED,
            **event_kwargs,
        )
        self.approved_future_event_1 = UserGeneratedContent.objects.create(
            end_datetime=now_ + timedelta(days=5),
            published_on=now_,
            start_datetime=now_ + timedelta(days=2),
            status=UserGeneratedContent.APPROVED,
            **event_kwargs,
        )
        self.approved_future_event_2 = UserGeneratedContent.objects.create(
            end_datetime=now_ + timedelta(days=5),
            published_on=now_ - timedelta(days=8),
            start_datetime=now_ + timedelta(days=2),
            status=UserGeneratedContent.APPROVED,
            **event_kwargs,
        )
        self.approved_past_event_1 = UserGeneratedContent.objects.create(
            end_datetime=now_ - timedelta(days=2),
            published_on=now_ - timedelta(days=8),
            start_datetime=now_ - timedelta(days=5),
            status=UserGeneratedContent.APPROVED,
            **event_kwargs,
        )
        photos_kwargs = dict(
            category=category,
            content_type=ContentType.PHOTOS,
            description="<p>bring it homies</p>",
            location="Underground bunker, Hoth",
            masthead=masthead,
            organiser=organiser,
            title="Badass party hats",
            user_profile=user_profile,
        )
        self.pending_photos_1 = UserGeneratedContent.objects.create(
            status=UserGeneratedContent.PENDING,
            **photos_kwargs,
        )
        self.approved_photos_1 = UserGeneratedContent.objects.create(
            published_on=now_ - timedelta(days=8),
            status=UserGeneratedContent.APPROVED,
            **photos_kwargs,
        )

    def test_upcoming_events(self):
        self.assertEqual(
            UserGeneratedContent.objects.upcoming_events().count(), 2
        )
        self.approved_upcoming_event_1.notifications.create(
            notification_type=NotificationType.UPCOMING_EVENT
        )
        self.assertEqual(
            UserGeneratedContent.objects.upcoming_events().count(), 1
        )

    def test_follow_ups(self):
        self.assertEqual(UserGeneratedContent.objects.follow_ups().count(), 2)
        self.approved_photos_1.notifications.create(
            notification_type=NotificationType.FOLLOW_UP
        )
        self.assertEqual(UserGeneratedContent.objects.follow_ups().count(), 1)


class UpdateNextOccurrenceTest(TestCase):
    def setUp(self):
        user_profile = UGCUserProfile.objects.create(
            description="It's just a matter of time",
            piano_user_id="anakin",
            user_email="<EMAIL>",
            user_name="Anakin Slayer Skywalker",
        )
        masthead = UGCMasthead.objects.create(site_id=317)
        organiser = UGCOrganiser.objects.create(
            name="Darth Vader", email="<EMAIL>"
        )
        category = UGCCategory.objects.create(
            content_type=ContentType.EVENT,
            name="Dark Side",
        )
        event_kwargs = dict(
            category=category,
            content_type=ContentType.EVENT,
            description="<p>RSVP or be choked out</p>",
            location="Tatooine",
            masthead=masthead,
            organiser=organiser,
            status=UserGeneratedContent.PENDING,
            title="Storm Trooper Xmas Party",
            user_profile=user_profile,
        )

        self.non_repeating = UserGeneratedContent.objects.create(
            start_datetime=datetime(2025, 2, 28, 0, 0, tzinfo=pytz.UTC),
            end_datetime=datetime(2025, 6, 1, 0, 0, tzinfo=pytz.UTC),
            next_occurrence=datetime(2025, 2, 28, 0, 0, tzinfo=pytz.UTC),
            **event_kwargs,
        )

        self.repeats_monthly_last_day = UserGeneratedContent.objects.create(
            start_datetime=datetime(2025, 2, 28, 10, 0, tzinfo=pytz.UTC),
            end_datetime=datetime(2025, 6, 1, 10, 0, tzinfo=pytz.UTC),
            recurrences="RRULE:FREQ=MONTHLY;UNTIL=20250601T000000Z;BYMONTHDAY=-1",
            next_occurrence=datetime(2025, 2, 28, 10, 0, tzinfo=pytz.UTC),
            **event_kwargs,
        )

        self.weekly_event = UserGeneratedContent.objects.create(
            start_datetime=datetime(2025, 2, 28, 10, 0, tzinfo=pytz.UTC),
            end_datetime=datetime(2025, 6, 1, 10, 0, tzinfo=pytz.UTC),
            recurrences="RRULE:FREQ=WEEKLY;UNTIL=20250601T000000Z;BYDAY=FR",
            next_occurrence=datetime(2025, 2, 28, 10, 0, tzinfo=pytz.UTC),
            **event_kwargs,
        )

    def test_command_runs_successfully(self):
        call_command("update_events_next_occurrence")

    @freeze_time("2025-02-28 00:00:00")
    def test_next_occurrence_updates_correctly_same_day_of_creation(self):
        call_command("update_events_next_occurrence")
        self.repeats_monthly_last_day.refresh_from_db()
        expected_date = datetime(2025, 2, 28, 0, 0, tzinfo=pytz.UTC)
        self.assertEqual(
            self.repeats_monthly_last_day.next_occurrence, expected_date
        )

    @freeze_time("2025-03-01 00:00:00")
    def test_next_occurrence_updates_correctly_past_first_occurrence(self):
        call_command("update_events_next_occurrence")
        self.repeats_monthly_last_day.refresh_from_db()
        expected_date = datetime(2025, 3, 31, 0, 0, tzinfo=pytz.UTC)
        self.assertEqual(
            self.repeats_monthly_last_day.next_occurrence, expected_date
        )

    @freeze_time("2025-05-31 00:00:00")
    def test_next_occurrence_updates_correctly_past_last_occurrence(self):
        call_command("update_events_next_occurrence")
        self.repeats_monthly_last_day.refresh_from_db()
        expected_date = datetime(2025, 5, 31, 0, 0, tzinfo=pytz.UTC)
        self.assertEqual(
            self.repeats_monthly_last_day.next_occurrence, expected_date
        )

    @freeze_time("2025-06-01 00:00:00")
    def test_next_occurrence_updates_correctly_no_more_occurrence(self):
        call_command("update_events_next_occurrence")
        self.repeats_monthly_last_day.refresh_from_db()
        self.assertEqual(self.repeats_monthly_last_day.next_occurrence, None)

    @freeze_time("2025-01-01 00:00:00")
    def test_non_repeating_event_before_start_date(self):
        """Test that next_occurrence is set to start_datetime when current date is before event start"""
        call_command("update_events_next_occurrence")
        self.non_repeating.refresh_from_db()
        self.assertEqual(
            self.non_repeating.next_occurrence,
            self.non_repeating.start_datetime,
        )

    @freeze_time("2025-02-28 00:00:00")
    def test_non_repeating_event_on_start_date(self):
        call_command("update_events_next_occurrence")
        self.non_repeating.refresh_from_db()
        self.assertEqual(
            self.non_repeating.next_occurrence,
            self.non_repeating.start_datetime,
        )

    @freeze_time("2025-05-01 00:00:00")
    def test_non_repeating_event_after_start_before_end(self):
        call_command("update_events_next_occurrence")
        self.non_repeating.refresh_from_db()
        midnight_utc = now()
        self.assertEqual(
            self.non_repeating.next_occurrence,
            midnight_utc,
        )

    @freeze_time("2025-06-02 00:00:00")
    def test_non_repeating_event_after_end_date(self):
        call_command("update_events_next_occurrence")
        self.non_repeating.refresh_from_db()
        self.assertIsNone(self.non_repeating.next_occurrence)

    @freeze_time("2025-03-06 00:00:00")
    def test_weekly_event(self):
        call_command("update_events_next_occurrence")
        self.weekly_event.refresh_from_db()
        expected_date = datetime(2025, 3, 7, 0, 0, tzinfo=pytz.UTC)
        self.assertEqual(self.weekly_event.next_occurrence, expected_date)


class UGCUpdateViewTestCase(TestCase):
    """Tests for UGC update view."""

    def setUp(self):
        """Create a UGC item."""
        user_profile = UGCUserProfile.objects.create(
            description="It's just a matter of time",
            piano_user_id="anakin",
            user_email="<EMAIL>",
            user_name="Anakin Slayer Skywalker",
        )
        masthead = UGCMasthead.objects.create(site_id=317)
        organiser = UGCOrganiser.objects.create(
            name="Darth Vader", email="<EMAIL>"
        )
        category = UGCCategory.objects.create(
            content_type=ContentType.EVENT,
            name="Dark Side",
        )

        self.ugc = UserGeneratedContent.objects.create(
            status=UserGeneratedContent.APPROVED,
            start_datetime=datetime(2025, 2, 28, 0, 0, tzinfo=pytz.UTC),
            end_datetime=datetime(2025, 6, 1, 0, 0, tzinfo=pytz.UTC),
            category=category,
            content_type=ContentType.EVENT,
            description="<p>RSVP or be choked out</p>",
            location="Tatooine",
            masthead=masthead,
            organiser=organiser,
            title="Storm Trooper Xmas Party",
            user_profile=user_profile,
        )

        self.user = get_user_model().objects.create_user(
            "test", "", "test", is_superuser=True
        )

    @override_settings(TEST_ORG_IDS=[1])
    @patch("longbeach.ugc.models.get_sites")
    @patch(
        "newsnow_cognito.consumer.templatetags.login_consumer_tags.requests.get"
    )
    def test_detail_url(self, mock_get, mock_get_sites):
        """Test the detail URL is displayed for approved items."""
        mock_get_sites.return_value = {
            self.ugc.masthead.site_id: {
                "id": self.ugc.masthead.site_id,
                "domain": "www.areanews.com.au",
                "name": "The Area News",
            },
        }
        mock_response = Mock()
        mock_response.json.return_value = {"id": 1, "disabled": True}
        mock_response.status_code = 200
        mock_get.return_value = mock_response
        self.client.force_login(self.user)

        # Approved item
        response = self.client.get(reverse("ugc_change", args=(self.ugc.pk,)))
        url = f"https://www.areanews.com.au/notice-board/whats-on/{self.ugc.pk}/storm-trooper-xmas-party/"

        self.assertContains(response, url)

        # Pending item
        self.ugc.status = UserGeneratedContent.PENDING
        self.ugc.save()

        response = self.client.get(reverse("ugc_change", args=(self.ugc.pk,)))

        self.assertNotContains(response, url)


class DateRangeFilterTest(TestCase):
    def setUp(self):
        user_profile = UGCUserProfile.objects.create(
            description="Test profile",
            piano_user_id="test_user",
            user_email="<EMAIL>",
            user_name="Test User",
        )
        masthead = UGCMasthead.objects.create(site_id=123)
        organiser = UGCOrganiser.objects.create(
            name="Test Organiser", email="<EMAIL>"
        )
        category = UGCCategory.objects.create(
            content_type=ContentType.EVENT,
            name="Test Category",
        )

        self.event_kwargs = dict(
            category=category,
            content_type=ContentType.EVENT,
            description="<p>Test description</p>",
            location="Test Location",
            masthead=masthead,
            organiser=organiser,
            title="Test Event",
            user_profile=user_profile,
            status=UserGeneratedContent.APPROVED,
        )

        self.now = now()

        # non-recurring weekend event
        weekend_start = self.now + timedelta(
            days=(5 - self.now.weekday()) % 7
        )  # next saturday
        self.weekend_event = UserGeneratedContent.objects.create(
            start_datetime=weekend_start,
            end_datetime=weekend_start + timedelta(days=1),
            next_occurrence=weekend_start,
            **self.event_kwargs,
        )

        # recurring daily event
        self.recurring_event = UserGeneratedContent.objects.create(
            start_datetime=self.now,
            end_datetime=self.now + timedelta(days=1),
            next_occurrence=self.now,
            recurrences="RRULE:FREQ=DAILY;UNTIL=20251212T000000Z",
            **self.event_kwargs,
        )

    def test_this_weekend_filter(self):
        filter_data = {"date_range": DateRangeOptions.THIS_WEEKEND}
        filter_instance = UserGeneratedContentFilter(
            filter_data, queryset=UserGeneratedContent.objects.all()
        )
        filtered_events = filter_instance.qs

        # should include both the weekend event and the daily recurring event
        self.assertEqual(filtered_events.count(), 2)
        self.assertIn(self.weekend_event, filtered_events)
        self.assertIn(self.recurring_event, filtered_events)

    def test_today_filter(self):
        with freeze_time(self.now):
            filter_data = {"date_range": DateRangeOptions.TODAY}
            filter_instance = UserGeneratedContentFilter(
                filter_data, queryset=UserGeneratedContent.objects.all()
            )
            filtered_events = filter_instance.qs

            self.assertEqual(filtered_events.count(), 1)
            self.assertIn(self.recurring_event, filtered_events)
