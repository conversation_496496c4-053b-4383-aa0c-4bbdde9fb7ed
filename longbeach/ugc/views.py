import logging
from typing import Optional

import reversion
from django.conf import settings
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters, status, viewsets
from rest_framework.decorators import api_view
from rest_framework.exceptions import NotFound
from rest_framework.pagination import LimitOffsetPagination
from rest_framework.response import Response
from rest_framework.serializers import ValidationError

from longbeach.ugc.filters import UserGeneratedContentFilter
from longbeach.ugc.serializers import (
    UGCCategorySerializer,
    UGCOrganiserSerializer,
    UGCOrganiserUpdateSerializer,
    UGCUserProfileSerializer,
    UserGeneratedContentSerializer,
)

from .models import (
    UGCCategory,
    UGCImage,
    UGCMasthead,
    UGCOrganiser,
    UGCUserProfile,
    UserGeneratedContent,
)

logger = logging.getLogger(__name__)


class CustomPagination(LimitOffsetPagination):
    def get_paginated_response(self, data):
        return Response(
            {
                "next_offset": self.offset + self.limit
                if self.offset + self.limit < self.count
                else None,
                "previous_offset": self.offset - self.limit
                if self.offset > 0 and self.offset - self.limit > 0
                else None,
                "count": self.count,
                "results": data,
            }
        )


class UserGeneratedContentViewSet(viewsets.ModelViewSet):
    queryset = UserGeneratedContent.objects.all()
    serializer_class = UserGeneratedContentSerializer
    pagination_class = CustomPagination
    filterset_class = UserGeneratedContentFilter
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]

    def create(self, request):
        request_data = request.data.copy()
        # user profile creation
        user_profile, errors = self.create_or_update_user()
        if not user_profile:
            return Response(
                {
                    "success": False,
                    "errors": errors,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        request_data["user_profile"] = user_profile.piano_user_id

        # organiser creation for event content type
        if (
            request_data.get("organiser_name")
            and request_data.get("content_type") == "event"
        ):
            organiser = self.create_or_update_organiser(
                user_profile=user_profile, request_data=request_data
            )
            if not organiser:
                return Response(
                    {
                        "success": False,
                        "errors": errors,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
            request_data["organiser"] = organiser.id if organiser else None

        if masthead_id := request_data.get("masthead"):
            masthead, created = UGCMasthead.objects.get_or_create(
                site_id=masthead_id
            )
            request_data["masthead"] = masthead.site_id

        serializer = self.get_serializer(data=request_data)
        if serializer.is_valid():
            with reversion.create_revision():
                ugc = serializer.save()
                reversion.set_comment(
                    f"Created by UGCUserProfile {user_profile.piano_user_id}"
                )

            images = request.FILES.getlist("images")
            ugc_images = [UGCImage(ugc=ugc, image=img) for img in images]
            UGCImage.objects.bulk_create(ugc_images)

            # Don't let email failures prevent successful response.
            try:
                ugc.send_status_email()
            except Exception:
                if settings.ENVIRONMENT != "production":
                    raise
                logger.exception("Failed to send UGC status email")
            try:
                ugc.send_new_content_email()
            except Exception:
                if settings.ENVIRONMENT != "production":
                    raise
                logger.exception("Failed to send UGC new content email")

            return Response(
                {
                    "success": True,
                    "message": "UGC created successfully.",
                },
                status=status.HTTP_201_CREATED,
            )

        return Response(
            {"success": False, "errors": serializer.errors},
            status=status.HTTP_400_BAD_REQUEST,
        )

    def create_or_update_user(self):
        request = self.request
        serializer = UGCUserProfileSerializer(data=request.data)

        if serializer.is_valid():
            validated_data = serializer.validated_data
            defaults = {
                "user_name": validated_data["user_name"],
                "user_email": validated_data["user_email"],
                "is_active": True,
            }

            if avatar := validated_data.get("avatar"):
                defaults["avatar"] = avatar

            with reversion.create_revision():
                user_profile, created = (
                    UGCUserProfile.objects.update_or_create(
                        piano_user_id=serializer.validated_data[
                            "piano_user_id"
                        ],
                        defaults=defaults,
                    )
                )
                user_profile.save()
                reversion.set_comment(
                    f"{'Created' if created else 'Updated'} by UGCUserProfile {user_profile.piano_user_id}"
                )

            return user_profile, None

        return None, serializer.errors

    def create_or_update_organiser(
        self, user_profile: UGCUserProfile, request_data: dict
    ):
        serializer = UGCOrganiserUpdateSerializer(data=request_data)
        if serializer.is_valid():
            validated_data = serializer.validated_data

            defaults = {
                "name": validated_data["organiser_name"],
                "contact_number": validated_data["organiser_contact_number"],
                "website_url": validated_data["organiser_website_url"],
            }

            # If a new logo is provided, add it to the defaults
            if organiser_logo := validated_data.get("organiser_logo"):
                defaults["logo"] = organiser_logo

            with reversion.create_revision():
                organiser, created = UGCOrganiser.objects.update_or_create(
                    email=validated_data["organiser_email"],
                    defaults=defaults,
                )
                organiser.save()
                user_profile.organiser = organiser
                user_profile.save(update_fields=["organiser"])
                reversion.set_comment(
                    f"{'Created' if created else 'Updated'} by UGCUserProfile {user_profile.piano_user_id}"
                )

            return organiser
        return None

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop("partial", False)
        instance = self.get_object()

        piano_user_id = instance.user_profile.piano_user_id

        data = request.data.copy()
        data["user_profile"] = piano_user_id
        data["status"] = UserGeneratedContent.PENDING

        # user profile update
        user_profile, errors = self.create_or_update_user()
        if not user_profile:
            return Response(
                {
                    "success": False,
                    "errors": errors,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # organiser update
        if data.get("organiser_name") and data.get("content_type") == "event":
            organiser = self.create_or_update_organiser(
                user_profile=user_profile, request_data=data
            )
            if not organiser:
                return Response(
                    {"success": False, "errors": "Invalid organiser data."},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            data["organiser"] = organiser.id

        serializer = self.get_serializer(instance, data=data, partial=partial)
        if serializer.is_valid():
            with reversion.create_revision():
                ugc = serializer.save()

                deleted_image_ids = serializer.validated_data.get(
                    "deleted_image_ids", []
                )
                if isinstance(deleted_image_ids, list):
                    ugc.images.filter(id__in=deleted_image_ids).delete()

                if request.FILES.getlist("images"):
                    images = request.FILES.getlist("images")
                    ugc_images = [
                        UGCImage(ugc=ugc, image=img) for img in images
                    ]
                    UGCImage.objects.bulk_create(ugc_images)

                reversion.set_comment(
                    f"Updated by UGCUserProfile {user_profile.piano_user_id}"
                )
            return Response(
                {"success": True, "message": "UGC updated successfully."},
                status=status.HTTP_200_OK,
            )

        return Response(
            {"success": False, "errors": serializer.errors},
            status=status.HTTP_400_BAD_REQUEST,
        )


class UGCCategoryViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = UGCCategory.objects.all()
    serializer_class = UGCCategorySerializer

    def list(self, request, *args, **kwargs):
        """
        Get list of categories per content type or get all categories.
        """
        content_type = request.query_params.get("content_type")
        if content_type:
            categories = UGCCategory.objects.filter(content_type=content_type)
        else:
            categories = self.queryset
        serializer = self.get_serializer(categories, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


@api_view(["GET"])
def get_organiser_by_user_email(request):
    user_email = request.query_params.get("email")

    if not user_email:
        raise ValidationError({"email": "This query parameter is required."})

    try:
        user_profile = UGCUserProfile.objects.get(user_email=user_email)
    except UGCUserProfile.DoesNotExist:
        raise NotFound("User does not exist.")

    organiser = getattr(user_profile, "organiser", None)
    if not organiser:
        raise NotFound("User has no associated organiser.")

    serializer = UGCOrganiserSerializer(organiser)
    return Response(serializer.data)


@api_view(["GET"])
def get_user_profile_by_email(request):
    user_email = request.query_params.get("email")
    if not user_email:
        raise ValidationError({"email": "This query parameter is required."})

    try:
        profile = UGCUserProfile.objects.get(user_email=user_email)
    except UGCUserProfile.DoesNotExist:
        raise NotFound("User profile not found for this email.")

    serializer = UGCUserProfileSerializer(profile)
    return Response(serializer.data)
