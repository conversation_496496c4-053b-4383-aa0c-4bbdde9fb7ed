from django.urls import include, re_path
from rest_framework import routers

from .views import (
    UGCCategoryViewSet,
    UserGeneratedContentViewSet,
    get_organiser_by_user_email,
    get_user_profile_by_email,
)

router = routers.DefaultRouter()
router.register(r"ugc", UserGeneratedContentViewSet)
router.register(r"ugc-categories", UGCCategoryViewSet)

urlpatterns = [
    re_path(r"^api/", include(router.urls)),
    re_path(r"api/ugc-organiser/$", get_organiser_by_user_email),
    re_path(r"api/ugc-user/$", get_user_profile_by_email),
]
