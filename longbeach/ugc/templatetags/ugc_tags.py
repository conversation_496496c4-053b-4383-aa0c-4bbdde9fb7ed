from django import template

register = template.Library()


@register.filter
def definite_article(noun: str) -> str:
    """
    Convert noun to definite article usage.
    For use on site names for example.
    eg.
    Already definite:
    - Read The Area News paper today
    Not definite:
    - Read Border Mail paper today
    Made definite:
    - Read the Border Mail paper today
    """
    if not noun or not noun.strip():
        return ""
    return noun if noun.lower().startswith("the") else f"the {noun}"
