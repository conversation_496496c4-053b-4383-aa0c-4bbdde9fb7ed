from django.template import Context, Template
from django.test import SimpleTestCase


class DefiniteArticleFilterTests(SimpleTestCase):
    def setUp(self):
        self.template = Template(
            "{% load ugc_tags %}{{ noun|definite_article }}"
        )

    def test_noun_already_definite(self):
        context = Context({"noun": "The Area News"})
        rendered = self.template.render(context)
        self.assertEqual(rendered, "The Area News")

    def test_noun_not_definite(self):
        context = Context({"noun": "Border Mail"})
        rendered = self.template.render(context)
        self.assertEqual(rendered, "the Border Mail")

    def test_no_noun(self):
        context = Context({"noun": ""})
        rendered = self.template.render(context)
        self.assertEqual(rendered, "")
