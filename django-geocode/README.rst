==============
django-geocode
==============


Overview
========

An abstract base model for storing latitude and longitude for a particular \
location as returned by a geocoding service. The values are requested upon \
``save()`` using the location string returned by the ``get_lookup_location()`` \
method, which must be implemented in the child model.

Inspired by `django-easy-maps <https://bitbucket.org/kmike/django-easy-maps>`_.


Usage
=====

Subclass the abstract base model in your application::

    from django.db import models
    from geocode.models import GeocodeAbstract

    class Business(GeocodeAbstract):
        name = models.Char<PERSON>ield(max_length=20)
        address = models.TextField()

        def get_lookup_location(self):
            return self.address


Latitude and longitude can also be added to an existing application without \
modifying existing models by using ``geocode_post_save_update`` to connect a \
post-save signal to the existing model::

    from django.db import models
    from geocode.models import GeocodeAbstract
    from geocode.signals import geocode_post_save_update

    class Business(GeocodeAbstract):
        name = models.CharField(max_length=20)
        address = models.TextField()

    class BusinessCoordinates(GeocodeAbstract):
        business = models.OneToOneField(Business, primary_key=True, related_name='coordinates')

        def get_lookup_location(self):
            return self.business.address

    geocode_post_save_update(Business, 'coordinates')


Management Commands
===================

Geocode all instances of a model which haven't been geocoded::

    python manage.py geocode [appname.ModelName ...]


TODO
====

* write tests with mock backend
* add flag to indicate a manual or overridden value, refactor ``address_has_changed()`` \
  method to skip geocode lookup if flag is set
