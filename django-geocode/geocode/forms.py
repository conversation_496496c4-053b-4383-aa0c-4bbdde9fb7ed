import math

from django import forms
from django.core.exceptions import ValidationError


class GeocodeBaseForm(forms.ModelForm):
    """
    A base form for GeocodeBase subclasses. Adds an optional non-model field
    for setting coordinates explicitly.

    """

    precise_coordinates = forms.CharField(
        required=False,
        label="Set precise coordinates",
        help_text="Leave blank to lookup automatically.",
    )

    def __init__(self, instance=None, *args, **kwargs):
        if instance and instance.coordinates and not instance.lookup_address:
            # Coordinates in this instance have been set explicitly. Populate
            # the form field with these coordinates.
            default = {
                "precise_coordinates": "%s, %s" % instance.coordinates,
            }
            default.update(kwargs.get("initial", {}))
            kwargs["initial"] = default
        super(GeocodeBaseForm, self).__init__(
            instance=instance, *args, **kwargs
        )

    def clean_precise_coordinates(self):
        """
        Ensure a valid comma-separated latitude and longitude pair is given.

        """

        val = self.cleaned_data["precise_coordinates"].lstrip().rstrip()

        if val:
            try:
                latitude, longitude = val.split(",")
            except ValueError:
                raise ValidationError(
                    "Enter latitude and longitude separated by a comma."
                )
            try:
                latitude, longitude = float(latitude), float(longitude)
                assert (
                    not math.isnan(latitude)
                    and not math.isinf(latitude)
                    and not math.isnan(longitude)
                    and not math.isinf(longitude)
                )
            except (ValueError, AssertionError):
                raise ValidationError(
                    "Enter numeric values for latitude and longitude."
                )
            if not -90 <= latitude <= 90 or not -180 <= longitude <= 180:
                raise ValidationError(
                    "Ensure latitude and longitude values are within range."
                )
            self.instance.latitude = latitude
            self.instance.longitude = longitude
            self.instance.lookup_address = None
        elif not self.instance.lookup_address:
            self.instance.latitude = self.instance.longitude = None

        return val
