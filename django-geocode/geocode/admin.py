from django.contrib import admin
from django.forms.models import modelform_factory

from geocode.forms import GeocodeBaseForm

__all__ = ("geocode_inline_factory",)


def coordinates(obj):
    if obj.coordinates:
        return "%s, %s" % obj.coordinates
    return ""


def located_on_map(obj):
    return obj.coordinates is not None


located_on_map.boolean = True


def geocode_inline_factory(
    model,
    inline=admin.TabularInline,
    form=GeocodeBaseForm,
    readonly_callables={},
):
    """
    Generate an InlineModelAdmin for a GeocodeBase subclass.

    """

    # generate a ModelForm using fieldsets or fields
    # from the inline class
    if inline.fieldsets:
        declared_fields = admin.util.flatten_fieldsets(inline.fieldsets)
    else:
        declared_fields = inline.fields
    exclude = list(inline.exclude or [])
    exclude.extend(inline.readonly_fields)
    form = modelform_factory(model, form, declared_fields, exclude)

    if not declared_fields:
        # include useful readonly fields from GeocodeBase that
        # won't be automatically added by the ModelForm
        geocode_fields = ["resolved_address", "coordinates"]
        fields = list(form.base_fields.keys())
        if "precise_coordinates" in fields:
            fields.remove("precise_coordinates")
            geocode_fields.append("precise_coordinates")
        geocode_fields.append("located_on_map")
        fields.extend(geocode_fields)
        fields.extend(inline.readonly_fields)
        readonly_fields = ["resolved_address", "coordinates", "located_on_map"]
        readonly_fields.extend(inline.readonly_fields)
    else:
        fields = declared_fields
        readonly_fields = inline.readonly_fields

    # replace readonly fields with callables
    callables = {
        "coordinates": coordinates,
        "located_on_map": located_on_map,
    }
    callables.update(readonly_callables)

    def _replace(field):
        if field in readonly_fields and field in callables:
            return callables[field]
        return field

    fields = [_replace(f) for f in fields]
    readonly_fields = [_replace(f) for f in readonly_fields]

    # remove located_on_map field if this is a new unsaved instance
    def get_fieldsets(self, request, obj=None):
        fieldsets = super(inline, self).get_fieldsets(request, obj)
        if obj is None:
            fields = admin.util.flatten_fieldsets(fieldsets)
            fields.remove(callables["located_on_map"])
            fieldsets = [(None, {"fields": fields})]
        return fieldsets

    inline_class_attrs = {
        "__module__": __name__,
        "model": model,
        "form": form,
        "fields": fields,
        "readonly_fields": readonly_fields,
        "get_fieldsets": get_fieldsets,
    }

    class_name = "Inline" + model.__name__ + "Admin"
    return type(class_name, (inline,), inline_class_attrs)
