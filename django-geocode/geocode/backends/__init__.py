from importlib import import_module

from django.conf import settings
from django.core.exceptions import ImproperlyConfigured

from .base import E<PERSON>r<PERSON><PERSON><PERSON>, GeocodeError

__all__ = ("get_backend", "ErrorResult", "GeocodeError")


def load_backend(path):
    """Return an instance of the geocode backend at the specified path."""
    i = path.rfind(".")
    module, attr = path[:i], path[i + 1 :]
    try:
        mod = import_module(module)
    except ImportError as e:
        raise ImproperlyConfigured(
            'Error importing geocode backend %s: "%s"' % (path, e)
        )
    try:
        cls = getattr(mod, attr)
    except AttributeError:
        raise ImproperlyConfigured(
            'Module "%s" does not define a "%s" geocode backend'
            % (module, attr)
        )
    return cls()


def get_backend():
    """Return an instance of the geocode backend specified in settings."""
    backend_path = getattr(
        settings, "GEOCODE_BACKEND", "geocode.backends.geopy.GoogleBackend"
    )
    return load_backend(backend_path)
