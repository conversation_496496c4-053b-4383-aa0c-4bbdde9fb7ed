import importlib
import sys
from urllib.error import HTTPError, URLError

geocoders = importlib.import_module("geopy.geocoders")
geopyexc = importlib.import_module("geopy.exc")

from django.conf import settings
from django.utils.encoding import smart_str

from geocode.backends import GeocodeError as GE

from .base import BackendBase, ErrorResult, GeocodeError, ResultBase

__all__ = ("GoogleBackend", "GeocodeError")


class GeopyBase(BackendBase):
    """A base class to request latitude and longitude from geopy."""

    def __init__(self):
        raise NotImplementedError

    def geocode(self, address):
        try:
            response = self.geocoder.geocode(
                smart_str(address), exactly_one=False
            )
            return self._result(response)
        except (GE, geopyexc.GeocoderTimedOut, TypeError):
            # Re-raise geopy exception as a GeocodeError, maintaining traceback
            (cls, val, traceback) = sys.exc_info()
            raise GeocodeError.with_traceback(traceback)

    def _result(self, result):
        """Return a geocode result for the given geopy response."""
        if not result:
            return ErrorResult("No geocoding result found")
        return GeopyResult(result[0])


class GeopyResult(ResultBase):
    """A result obtained from a geopy backend."""

    def __init__(self, result):
        self.resolved_address, (latitude, longitude) = result
        super(GeopyResult, self).__init__(latitude, longitude)


class GoogleBackend(GeopyBase):
    """A class to request a result from Google Maps."""

    def __init__(self):
        api_settings = {
            "client_id": getattr(settings, "GEOCODE_GOOGLE_CLIENT_ID", None),
            "secret_key": getattr(settings, "GEOCODE_GOOGLE_SECRET_KEY", None),
        }
        self.geocoder = geocoders.GoogleV3(**api_settings)

    def geocode(self, *args, **kwargs):
        # The geopy Google backend doesn't catch urllib2 exceptions. Re-raise
        # them as GeocodeError exceptions.
        try:
            return super(GoogleBackend, self).geocode(*args, **kwargs)
        except (HTTPError, URLError):
            (cls, val, traceback) = sys.exc_info()
            raise GeocodeError.with_traceback(traceback)
