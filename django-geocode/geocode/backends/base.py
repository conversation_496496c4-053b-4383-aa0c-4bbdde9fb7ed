class BackendBase(object):
    """A base class for geocode backends."""

    def geocode(self, address):
        """Return a geocode result for the given address string."""
        raise NotImplementedError


class ResultBase(object):
    """A base class for storing results from a geocode backend."""

    latitude = None
    longitude = None
    resolved_address = None

    def __init__(self, latitude, longitude):
        self.latitude = latitude
        self.longitude = longitude

    @property
    def coordinates(self):
        """Return a (latitude, longitude) tuple."""
        if self.latitude is None or self.longitude is None:
            return None
        return (self.latitude, self.longitude)


class ErrorResult(ResultBase):
    """A result could not be obtained for the given address."""

    error_message = None

    def __init__(self, error_message=None):
        self.error_message = error_message


class GeocodeError(Exception):
    """An error occurred while submitting a request to the geocode backend."""

    pass
