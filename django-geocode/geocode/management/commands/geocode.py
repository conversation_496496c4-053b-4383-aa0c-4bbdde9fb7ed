from optparse import make_option

from django.core.exceptions import ImproperlyConfigured
from django.core.management.base import BaseCommand, CommandError


class Command(BaseCommand):
    help = "geocodes any instances of the given models which haven't been geocoded."
    args = "[appname.ModelName ...]"
    option_list = BaseCommand.option_list + (
        make_option(
            "--process-unresolved",
            action="store_true",
            dest="process_unresolved",
            default=False,
            help="include instances which don't have a resolved address.",
        ),
    )

    def handle(self, *model_labels, **options):
        from django.db.models import get_app, get_model

        if not model_labels:
            raise CommandError("Enter at least one model [appname.model].")

        process_unresolved = options.get("process_unresolved", False)
        verbosity = int(options.get("verbosity", 1))

        for label in model_labels:
            if verbosity >= 2:
                self.stdout.write("Processing '%s' \n" % label)

            app_label, model_label = label.split(".")
            try:
                app = get_app(app_label)
            except ImproperlyConfigured:
                raise CommandError("Unknown application: %s" % app_label)

            model = get_model(app_label, model_label)
            if model is None:
                raise CommandError(
                    "Unknown model: %s.%s" % (app_label, model_label)
                )

            for obj in model._default_manager.all():
                if process_unresolved:
                    do_lookup = obj.resolved_address is None
                else:
                    do_lookup = obj.lookup_required()

                if do_lookup:
                    if verbosity >= 2:
                        self.stdout.write(".")
                    obj.save(auto_geocode=True, force_geocode=True)

            if verbosity >= 2:
                self.stdout.write("\n")
