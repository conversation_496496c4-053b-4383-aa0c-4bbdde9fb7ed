from django.db.models.signals import post_save

__all__ = ("geocode_post_save_update",)


def geocode_post_save_update(model, related_field):
    """
    Connect a post-save signal to lookup and store latitude/longitude for the 
    given model.

    model -- the model that the signal will be connected to
    related_field -- the related name of the OneToOneField on the \
        corresponding GeocodeAbstract concrete model

    """

    geocode_model = getattr(model, related_field).related.model

    def callback(sender, instance, raw, **kwargs):
        if not raw:
            try:
                geocode = getattr(instance, related_field)
                assert geocode is not None
            except (geocode_model.DoesNotExist, AssertionError):
                setattr(instance, related_field, geocode_model())
                geocode = getattr(instance, related_field)
            geocode.save()

    dispatch_uid = "geocode_post_save_update-%s-%s" % (
        model.__name__,
        geocode_model.__name__,
    )

    post_save.connect(callback, model, weak=False, dispatch_uid=dispatch_uid)
