import logging

from django.contrib.gis.geos import Point
from django.db import models

from geocode.backends import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GeocodeError, get_backend

__all__ = ("GeocodeBase",)

logger = logging.getLogger(__name__)


class GeocodeBase(models.Model):
    """
    A base model to lookup and store latitude and longitude.

    """

    lookup_address = models.TextField(null=True, editable=False)
    resolved_address = models.TextField(null=True, editable=False)
    latitude = models.FloatField(null=True, editable=False)
    longitude = models.FloatField(null=True, editable=False)

    class Meta:
        abstract = True

    def __unicode__(self):
        if not self.coordinates:
            return "None"
        return "%s, %s" % self.coordinates

    def save(self, auto_geocode=True, force_geocode=False, *args, **kwargs):
        """
        Save the current instance. If lookup address has changed, first perform
        a geocode lookup and update the latitude and longitude values.

        auto_geocode -- set to False to prevent an automatic geocode lookup
        force_geocode -- set to True to force a geocode lookup

        """

        if auto_geocode and self.lookup_required() or force_geocode:
            try:
                self._geocode_lookup()
            except GeocodeError:
                created = self.pk is None
                if created:
                    # save object so the pk of this object can be logged
                    super(GeocodeBase, self).save(*args, **kwargs)
                logger.exception(
                    "_geocode_lookup() failed for %s(pk=%s)"
                    % (self.__class__.__name__, self.pk)
                )
                if created:
                    return
        super(GeocodeBase, self).save(*args, **kwargs)

    def get_lookup_address(self):
        """
        Return the address to submit to the geocode backend.

        """

        raise NotImplementedError

    def lookup_required(self):
        """
        Determine whether a geocode lookup is required.

        If there is an existing lookup address, a geocode lookup is required if
        the address has changed. Otherwise, a geocode lookup is required if the
        latitude and longitude are not already set.

        """

        if self.lookup_address:
            return self.lookup_address != self.new_lookup_address
        else:
            return self.new_lookup_address and not self.coordinates

    def is_resolved(self):
        """
        Determine if the object is resolved to a valid geocode value.

        """
        return self.coordinates is not None

    def map_link(self):
        """
        Return a Google Maps link for this location.

        """
        if not self.is_resolved():
            return ""

        return (
            '<a href="https://maps.google.com.au/maps?z=19&q=%s,%s" '
            'target="_blank">View on map</a>' % self.coordinates
        )

    @property
    def coordinates(self):
        """
        Return a (latitude, longitude) tuple.

        """

        if self.latitude is None or self.longitude is None:
            return None
        return (self.latitude, self.longitude)

    def _geocode_lookup(self):
        """
        Perform a geocode lookup with the configured backend and store the
        result in the current instance.

        """

        geocoder = get_backend()
        result = geocoder.geocode(self.new_lookup_address)

        self.lookup_address = self.new_lookup_address
        if isinstance(result, ErrorResult):
            self.resolved_address = self.latitude = self.longitude = None
        else:
            self.resolved_address = result.resolved_address
            self.latitude = result.latitude
            self.longitude = result.longitude

            if (
                hasattr(self, "point")
                and self.latitude is not None
                and self.longitude is not None
            ):
                self.point = Point(self.longitude, self.latitude)

    def _get_new_lookup_address(self):
        """
        The address that will be sent to the geocode backend.

        """

        if not hasattr(self, "_new_lookup_address"):
            self._new_lookup_address = self.get_lookup_address()
        return self._new_lookup_address

    new_lookup_address = property(_get_new_lookup_address)
