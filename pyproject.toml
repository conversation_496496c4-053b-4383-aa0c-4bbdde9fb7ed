[tool.poetry]
name = "longbeach"
version = "0.1.0"
description = "Business Profiles and Local Ads"
authors = ["Pitcrews"]

[tool.poetry.dependencies]
python = "^3.9"
Django = "~3.1"
psycopg2 = "2.9.9"
six = "*"
pytz = "*"
ipdb = "*"
pylibmc = "*"
# Pin due to issue with Django3.1.
# https://stackoverflow.com/questions/63394004/client-object-has-no-attribute-deletetouch-in-django-admin-logout-section
python-memcached = "1.59"
pycurl = "*"
setproctitle = "*"
gevent = "*"
greenlet = "*"
eventlet = "*"
requests = "~2.28"
gunicorn = "*"
cryptography = "*"

# Django
django-debug-toolbar = "3.0a2" # TODO: Using alpha to stop django 4 deprecation warnings.
django-passwords = "~0.3.11"
python-decouple = "~3.1"
django-smtp-ssl = "~1.0"
# TODO: Fix for tastypie until official one becomes available.
django-tastypie = { git = "https://github.com/fatcop/django-tastypie", branch = "fix/build_filters" }
django-array-field-select = "0.2.0"
django-taggit = "1.3"
django-autoslug = "~1.9.3"
django-model-utils = "~4.0.0"
django-recurrence = "~1.10.3"

# Date Utils
pendulum = "1.3.1"

# Image
pillow = "*"

# Elasticsearch / Haystack
# This is the only newest supported version as AWS ES and ES have parted ways.
# https://opensearch.org/docs/latest/clients/index/
elasticsearch = "7.13.4"
django-haystack = "^3"
django-haystack-panel = "0.2.1"

# Security
django-cors-headers = "~1.1.0"
# Pinning pyopenssl due do removal of deprecated functions in later versions
# needed by django-sns-view (pulled in via django-shared-login)
pyopenssl = "24.2.1"


# Phone Numbers
django-phonenumber-field = "~4.0.0"
phonenumberslite = "~8.12.7"

# Pitcrews
django-form-utils = { git = "https://gitlab.com/fairfax-acm/racetracks/django-form-utils", branch = "master" }
django-oauthsome = { git = "https://gitlab.com/fairfax-acm/racetracks/django-oauthsome", branch = "master" }
django-shared-login = { git = "https://gitlab.com/fairfax-acm/racetracks/django-shared-login", branch = "master" }
pitcrews-oauth2 = { git = "https://gitlab.com/fairfax-acm/racetracks/pitcrews-oauth2", branch = "master" }
django-shared-orgs-client = { git = "https://gitlab.com/fairfax-acm/racetracks/django-shared-orgs-client", branch = "master" }
django-valencia-storage = { git = "https://gitlab.com/fairfax-acm/racetracks/django-valencia-storage", branch = "master" }
pitcrews-layouts = { git = "https://gitlab.com/fairfax-acm/racetracks/pitcrews-layouts", branch = "master" }
slumber-party = { git = "https://gitlab.com/fairfax-acm/racetracks/slumber-party", branch = "master" }
django-geocode = { git = "https://gitlab.com/fairfax-acm/racetracks/django-geocode", branch = "master" }
pitcrews-api = { git = "https://gitlab.com/fairfax-acm/racetracks/pitcrews-api", branch = "master" }
pitcrews-health = { git = "https://gitlab.com/fairfax-acm/racetracks/pitcrews-health", branch = "master" }
django-calm-cache = { git = "https://gitlab.com/fairfax-acm/racetracks/django-calm-cache", branch = "master" }
aws-elasticsearch = { git = "https://gitlab.com/fairfax-acm/racetracks/aws-elasticsearch", branch = "master" }
django-newsnow-cognito = { git = "https://gitlab.com/fairfax-acm/racetracks/django-newsnow-cognito", branch = "master" }

# API v2
djangorestframework = "~3.11.1"
django-rest-swagger = "~2.2.0"
rest_condition = "~1.0.3"
django-filter = "~2.3.0"

# AWS
boto3 = "~1.7"
# Using our fork since django 1.11 as the following changes are not supported
# https://github.com/django/django/pull/12893/files
django-elasticache = { git = "https://gitlab.com/fairfax-acm/racetracks/django-elasticache", branch = "master" }

# HTML Sanitizing
bleach = "^6.2.0"

# Audit log
django-reversion = "^3.0"
django-reversion-compare = "^0.13.1"

# Monitoring / Logging
newrelic = "^9.0.0"
sentry-sdk = "^1"
python-json-logger = "0.1.5"
freezegun = "^1.5.1"
ruff = "*"

[tool.poetry.group.dev.dependencies]
boto3-stubs = "~1.15"                              # Matching boto3@1.15
botocore-stubs = "~1.20"                           # boto3@~1.15 should install 1.18.18. This is closest there is.
coverage = { version = "^7.6.4", extras = ["toml"] }
django-coverage-plugin = "^3.1.0"
django-stubs = "^1.10.1"
mock = "^2"
pytest-django = "^3"
types-bleach = "^6.2.0"
types-python-dateutil = "~2.8.2"
types-requests = "~2.28"
Whoosh = "2.5.7"

[tool.coverage.report]
skip_covered = true
skip_empty = true

[tool.coverage.run]
branch = true
omit = ["coverage/*", "*/migrations/*"]
plugins = ["django_coverage_plugin"]
source = ["."]

[tool.ruff]
exclude = ["migrations"]
line-length = 79

[tool.ruff.lint]
select = ["E4", "E7", "E9", "F", "I"]
ignore = ["E731"]

[tool.ruff.lint.flake8-self]
ignore-names = ["_meta"]

[tool.ruff.lint.isort]
combine-as-imports = true
split-on-trailing-comma = true

[tool.django-stubs]
django_settings_module = "longbeach.settings.docker"

[tool.mypy]
allow_untyped_globals = true
allow_redefinition = false
exclude = [
  '[a-zA-Z_]+.migrations.',
  '[a-zA-Z_]+.tests.',
  '[a-zA-Z_]+.testing.',
]
follow_imports_for_stubs = true
ignore_missing_imports = true
no_implicit_optional = true
plugins = ["mypy_django_plugin.main"]
python_version = "3.9"
show_error_codes = true
