daemon off;

error_log stderr info;

events {
    use epoll;
    worker_connections 128;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    access_log /dev/stdout;
    sendfile on;

    server {
        listen 80;
        listen 443 ssl http2;
        ssl_certificate ssl/cert.pem;
        ssl_certificate_key ssl/key.pem;

        resolver 127.0.0.11 valid=20s ipv6=off;
        port_in_redirect off;

        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Protocol $scheme;

        proxy_buffering off;
        proxy_request_buffering off;

        gzip                on;
        gzip_disable        "msie6";
        gzip_vary           on;
        gzip_proxied        any;
        gzip_comp_level     3;
        gzip_buffers        64 8k;
        gzip_http_version   1.1;
        gzip_types          text/plain text/css application/json application/x-javascript text/javascript image/svg+xml;

        location  /static/ {
            alias    /var/www/static/;
            expires max;
            add_header Cache-Control "public";
        }

        location  /media/ {
            alias    /var/www/media/;
            add_header  Access-Control-Allow-Origin $upstream_http_ACCESS_CONTROL_ALLOW_ORIGIN;
            add_header  X-Cache $upstream_http_X_CACHE;
            add_header  X-Cache-Stats $upstream_http_X_CACHE_STATS;
        }

        location / {
            proxy_pass http://backend:8000;
            proxy_redirect off;
            if ( $request_uri = /server-ping/ ) {
                access_log off;
            }

            client_max_body_size    25m;
            proxy_connect_timeout   15;
            proxy_read_timeout      120;
            proxy_send_timeout      10m;
            proxy_buffer_size       16k;
            proxy_buffers           32 16k;
        }

        location = /cert.pem {
            alias /etc/nginx/ssl/cert.pem;
        }

        location = /favicon.ico {
            alias /var/www/localhost/favicon.ico;
        }
    }

    # Expose Elasticsearch if present
    server {
        listen 9200;
        location / {
            proxy_pass http://elasticsearch:9200;
        }
    }
}
