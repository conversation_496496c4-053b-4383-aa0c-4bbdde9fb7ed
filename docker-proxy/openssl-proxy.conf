[req]
distinguished_name = req_distinguished_name
x509_extensions = x509_ext

[req_distinguished_name]
countryName = Country Name (2 letter code)
countryName_default = NA
stateOrProvinceName = State or Province Name (full name)
stateOrProvinceName_default = Virtual
localityName = Locality Name (eg, city)
localityName_default = Docker
organizationName = Organization name
organizationName_default = Fairfax
organizationalUnitName = Organizational Unit Name (eg, section)
organizationalUnitName_default = NewsNow
commonName = Common Name (e.g. server FQDN or YOUR name)
commonName_default = Docker app proxy for ${ENV::STACK_DOMAIN}

[ x509_ext ]
subjectAltName = @alt_names

[ v3_req ]
subjectAltName = @alt_names

[alt_names]
DNS.1 = ${ENV::STACK_DOMAIN}
DNS.2 = *.${ENV::STACK_DOMAIN}
