syntax: glob

xapian_index

# hide virtualenv working files
env
_env
activate.sh

# hide any dev dbs
*.db
*.sqlite
*.sqlite3

# hide bytecode
*.pyc

# hide common editor swaps
.*.sw?
.sw?
# hide OS X Meta files
._*
.DS_Store

# hide FUSE Junk
.fuse_hidden*

# hide nose testing output
nosetests.xml

# hide hg orig files
*.orig
xapian

# hide sass cache
.sass-cache

# hide test output
tox.ini
TEST-*.xml
.tox
junit-*.xml
MANIFEST
coverage.xml
.coverage

media
local_settings.py

*.bak*

*.log

*.zip

*.jpg

*.jpeg

syntax: regexp

node_modules
coverage
.pytest