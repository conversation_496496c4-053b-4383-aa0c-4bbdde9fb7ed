
Longbeach - Business Profiles service
-------------------------------------

Overview
========

Longbeach is a web based business profiles database and accompanying REST API.

Information such as address, contact numbers, website, email, opening hours can be managed by end users after creating an account and logging into the Longbeach web managment interface.

The REST API enables authorised 3rd parties to access the business profiles to use in their own applications, web or otherwise. A current example is Suzuka which hosts a local Business Profiles page.


REST API
========

Currently the API is read only, ie. only GET is supported. The following resources are available:

* Business
* BusinessCategory

WYSIWYG Editor
==============

See readme in longbeach/tiptap.

TESTING
=======

Pytest
------

Use pytest to run tests.

For local testing, excessive debug output has been muted:
- The default logging is set to WARNING level even for DEBUG=True case. See DEBUG_LOGGING_LEVEL in longbeach/settings/logging_settings.py.
- Some deprecation warnings have been filtered out in pytest.ini.

If you are doing django upgrade, deps bumps or need deeper testing of issues you are advised to temporarily adjust those to suit your needs.

Email
-----

Locally, the mailpit application is being used to capture SMTP email sends.

Browse http://localhost:8025/ to see captured emails.

