stages:
  - build
  - push image
  - test
  - non-prod deploy
  - deploy

variables:
  AWS_DEFAULT_REGION: ap-southeast-2
  AWS_ACCOUNTS: "production:************,uat:************,staging:************,development:************,ci:************"


build:
  image: ************.dkr.ecr.ap-southeast-2.amazonaws.com/devops/pipeline-images:assume-role-259
  stage: build
  tags:
    - newsnow.io/stability:spot-v4
    - newsnow.io/application:gitlab-runner-v4
    - newsnow.io/application-group:ci-v4
  services:
    - ${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/docker:24.0.4-dind
  variables:
    DOCKER_HOST: tcp://localhost:2375
    DOCKER_TLS_CERTDIR: ''
    DOCKER_DRIVER: overlay2
  before_script:
    - until docker info > /dev/null 2>&1; do echo -e "\033[32mPreparing dind service...\033[0m" && sleep 1; done
    - |
      infos=(${AWS_ACCOUNTS//,/ })
      info=${infos[3]}
      ac_num=${info#*:}
      ac=${info%:*}
      info_staging=${infos[2]}
      ac_num_staging=${info_staging#*:}
      ac_staging=${info_staging%:*}
      info_uat=${infos[1]}
      ac_num_uat=${info_uat#*:}
      ac_uat=${info_uat%:*}
    - ECR_REGISTRY_URL="${ac_num}.dkr.ecr.ap-southeast-2.amazonaws.com/racetracks"
    - ECR_REGISTRY_URL_STAGING="${ac_num_staging}.dkr.ecr.ap-southeast-2.amazonaws.com/racetracks"
    - ECR_REGISTRY_URL_UAT="${ac_num_uat}.dkr.ecr.ap-southeast-2.amazonaws.com/racetracks"
    - image_tag_commit_sha=${CI_COMMIT_REF_SLUG}-${CI_COMMIT_SHORT_SHA}
    - image_tag_pipeline_id=${CI_COMMIT_REF_SLUG}-${CI_PIPELINE_ID}
    - eval "$(assume-role.sh ${ac} terraform)"
    - docker version
    - docker images
  script:
    - DOCKER_BUILDKIT=0 docker build --build-arg GITLAB_ACCESS_USERNAME=${GITLAB_ACCESS_USERNAME} --build-arg GITLAB_ACCESS_PASSWORD=${GITLAB_ACCESS_PASSWORD} -t ${ECR_REGISTRY_URL}/${CI_PROJECT_NAME}:${image_tag_commit_sha} -t ${ECR_REGISTRY_URL}/${CI_PROJECT_NAME}:${image_tag_pipeline_id} .
    - docker push ${ECR_REGISTRY_URL}/${CI_PROJECT_NAME}:${image_tag_commit_sha}
    - docker push ${ECR_REGISTRY_URL}/${CI_PROJECT_NAME}:${image_tag_pipeline_id}
    - |
      unset AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY AWS_SESSION_TOKEN
      docker tag ${ECR_REGISTRY_URL}/${CI_PROJECT_NAME}:${image_tag_commit_sha} ${ECR_REGISTRY_URL_STAGING}/${CI_PROJECT_NAME}:${image_tag_commit_sha}
      docker tag ${ECR_REGISTRY_URL}/${CI_PROJECT_NAME}:${image_tag_pipeline_id} ${ECR_REGISTRY_URL_STAGING}/${CI_PROJECT_NAME}:${image_tag_pipeline_id}
      eval "$(assume-role.sh ${ac_staging} terraform)"
      docker push ${ECR_REGISTRY_URL_STAGING}/${CI_PROJECT_NAME}:${image_tag_commit_sha}
      docker push ${ECR_REGISTRY_URL_STAGING}/${CI_PROJECT_NAME}:${image_tag_pipeline_id}
      unset AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY AWS_SESSION_TOKEN
      docker tag ${ECR_REGISTRY_URL}/${CI_PROJECT_NAME}:${image_tag_commit_sha} ${ECR_REGISTRY_URL_UAT}/${CI_PROJECT_NAME}:${image_tag_commit_sha}
      docker tag ${ECR_REGISTRY_URL}/${CI_PROJECT_NAME}:${image_tag_pipeline_id} ${ECR_REGISTRY_URL_UAT}/${CI_PROJECT_NAME}:${image_tag_pipeline_id}
      eval "$(assume-role.sh ${ac_uat} terraform)"
      docker push ${ECR_REGISTRY_URL_UAT}/${CI_PROJECT_NAME}:${image_tag_commit_sha}
      docker push ${ECR_REGISTRY_URL_UAT}/${CI_PROJECT_NAME}:${image_tag_pipeline_id}
      docker images
  only:
    - branches
    - merge_requests
  retry:
    max: 2
    when: always
  except:
    - schedules

push image:production:
  stage: push image
  image: ************.dkr.ecr.ap-southeast-2.amazonaws.com/devops/pipeline-images:assume-role-259
  services:
    - ${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/docker:24.0.4-dind
  variables:
    DOCKER_HOST: tcp://localhost:2375
    DOCKER_TLS_CERTDIR: ''
    DOCKER_DRIVER: overlay2
  tags:
    - newsnow.io/stability:on-demand-v4
    - newsnow.io/application:gitlab-runner-v4
    - newsnow.io/application-group:ci-v4
  before_script:
    - until docker info > /dev/null 2>&1; do echo -e "\033[32mPreparing dind service...\033[0m" && sleep 1; done
  script:
    - |
      infos=(${AWS_ACCOUNTS//,/ })
      info_dev=${infos[3]}
      ac_num_dev=${info_dev#*:}
      ac_dev=${info_dev%:*}
      info_production=${infos[0]}
      ac_num_production=${info_production#*:}
      ac_production=${info_production%:*}
    - |
      ECR_REGISTRY_URL_DEV="${ac_num_dev}.dkr.ecr.ap-southeast-2.amazonaws.com/racetracks"
      ECR_REGISTRY_URL_PRODUCTION="${ac_num_production}.dkr.ecr.ap-southeast-2.amazonaws.com/racetracks"
    - image_tag_commit_sha=${CI_COMMIT_REF_SLUG}-${CI_COMMIT_SHORT_SHA}
    - image_tag_pipeline_id=${CI_COMMIT_REF_SLUG}-${CI_PIPELINE_ID}
    - |
      eval "$(assume-role.sh ${ac_dev} terraform)"
      docker pull ${ECR_REGISTRY_URL_DEV}/${CI_PROJECT_NAME}:${image_tag_commit_sha}
      unset AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY AWS_SESSION_TOKEN
      docker tag ${ECR_REGISTRY_URL_DEV}/${CI_PROJECT_NAME}:${image_tag_commit_sha} ${ECR_REGISTRY_URL_PRODUCTION}/${CI_PROJECT_NAME}:${image_tag_commit_sha}
      docker tag ${ECR_REGISTRY_URL_DEV}/${CI_PROJECT_NAME}:${image_tag_commit_sha} ${ECR_REGISTRY_URL_PRODUCTION}/${CI_PROJECT_NAME}:${image_tag_pipeline_id}
      eval "$(assume-role.sh ${ac_production} terraform)"
      docker push ${ECR_REGISTRY_URL_PRODUCTION}/${CI_PROJECT_NAME}:${image_tag_commit_sha}
      docker push ${ECR_REGISTRY_URL_PRODUCTION}/${CI_PROJECT_NAME}:${image_tag_pipeline_id}
      docker images
  only:
    - master
  except:
    - schedules

test:unit: &unit_test # workflows: MR, production release
  image: ************.dkr.ecr.ap-southeast-2.amazonaws.com/devops/pipeline-images:assume-role-259
  stage: test
  services:
    - ${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/postgis/postgis:16-3.4
    - ${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/docker:24.0.4-dind
    - name: ${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/elasticsearch:7.10.1
      variables:
        discovery.type: single-node
        ES_JAVA_OPTS: -Xms512m -Xmx512m
    - memcached:alpine
  variables:
    DOCKER_HOST: tcp://localhost:2375
    DOCKER_TLS_CERTDIR: ''
    DOCKER_DRIVER: overlay2
    RACETRACK_USER: ${RACETRACK_USER}
    RACETRACK_PASSWORD: ${RACETRACK_PASSWORD}
    OAUTH_KEY: longbeach-key
    OAUTH_SECRET: ${OAUTH_SECRET}
    POSTGRES_HOST_AUTH_METHOD: trust
  tags:
    - newsnow.io/stability:spot-v4
    - newsnow.io/application:gitlab-runner-v4
    - newsnow.io/application-group:ci-v4
  before_script:
    - until docker info > /dev/null 2>&1; do echo -e "\033[32mPreparing dind service...\033[0m" && sleep 1; done
    - ECR_REGISTRY_URL="${DEVELOPMENT_ACCOUNT}.dkr.ecr.ap-southeast-2.amazonaws.com/racetracks"
    - image_tag=${CI_COMMIT_REF_SLUG}-${CI_PIPELINE_ID}
    - eval "$(assume-role.sh development terraform)"
    - docker version
    - docker images
    - |
      eval "cat <<EOF
      $(<.gitlab-ci/env.tpl)
      EOF
      " | tee .env
  script:
    - docker pull ${ECR_REGISTRY_URL}/${CI_PROJECT_NAME}:${image_tag}
    - docker run --net=host --env-file .env -t -v "$PWD:/artifacts" ${ECR_REGISTRY_URL}/${CI_PROJECT_NAME}:${image_tag} /bin/bash -c "ruff format --check && coverage run ./manage.py test && coverage report && coverage xml -o /artifacts/coverage.xml && coverage html -d /artifacts/coverage && mypy ."
  only:
    - master
    - merge_requests
  except:
    - schedules
  artifacts:
    paths:
      - coverage/
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml
  coverage: /(?i)total.*? (100(?:\.0+)?\%|[1-9]?\d(?:\.\d+)?\%)$/
  allow_failure: false

deploy development: &deploy
  image: ************.dkr.ecr.ap-southeast-2.amazonaws.com/devops/pipeline-images:tf-1.2.2-147
  stage: deploy
  variables:
    NEWRELIC_APP_ID:  $NEWRELIC_APP_ID_DEVELOPMENT
    NEWRELIC_API_KEY_ENV: $NEWRELIC_API_KEY_NONPROD
  environment:
    name: development
  tags:
    - newsnow.io/stability:on-demand-v4
    - newsnow.io/application:gitlab-runner-v4
    - newsnow.io/application-group:ci-v4
  before_script:
    - |
      infos=(${AWS_ACCOUNTS//,/ })
      info=${infos[3]}
      ac_num=${info#*:}
      ac=${info%:*}
  script:
    - ECR_REGISTRY_URL="${ac_num}.dkr.ecr.ap-southeast-2.amazonaws.com/racetracks"
    - image_tag_commit_sha=${CI_COMMIT_REF_SLUG}-${CI_COMMIT_SHORT_SHA}
    - image_tag_pipeline_id=${CI_COMMIT_REF_SLUG}-${CI_PIPELINE_ID}
    - eval "$(assume-role.sh ${ac} terraform)"
    - PROJECT_NAME=${CI_PROJECT_NAME}
    - git config --global user.name "acm-bot-pr"
    - git config --global user.email "<EMAIL>"
    - git remote -v
    - git branch -av
    - git clone -b master https://gitlab-ci-token:${CI_JOB_TOKEN}@gitlab.com/fairfax-acm/deployers/tf_${CI_PROJECT_NAME}_ecs_service.git
    - cd tf_${CI_PROJECT_NAME}_ecs_service
    - git config --global user.name "acm-bot-pr"
    - git config --global user.email "<EMAIL>"
    - printf "https://gitlab-ci-token:%<EMAIL>\n" $CI_JOB_TOKEN > ~/.git-credentials
    - git config --global credential.helper 'store --file ~/.git-credentials'
    - git --version
    - terraform -v
    - export TF_VAR_app_image=racetracks/${PROJECT_NAME}:${image_tag_pipeline_id}
    - environment=${CI_JOB_NAME#* }
    - terraform init -backend-config=`pwd`/${ac}/backend-${ac}-main.conf -lock=true -backend=true -lock-timeout=60s
    - terraform get --update=true
    - terraform plan -var-file=`pwd`/${ac}/${ac}-main.tfvars -out=plan -lock=true -lock-timeout=120s
    - terraform apply -lock=true --lock-timeout=120s plan
    - terraform output -raw ecs_cluster_name > ecs_cluster_name
    - terraform output -raw task_definition_family > task_definition
    - cat ecs_cluster_name task_definition
    - ls -lah
    - ./db_migration.sh
  after_script:
    - IMAGE_BUILD=${CI_COMMIT_REF_SLUG}-${CI_PIPELINE_ID}
    - APP=$CI_PROJECT_NAME
    - COMMIT_MSG=$(echo $CI_COMMIT_MESSAGE | sed 's/"/\\"/g')
    - AUTHOR=$(git --no-pager show -s --format='%an' $CI_COMMIT_SHA| sed 's/"/\\"/g')
    - template='{ "deployment":{ "revision":"%s", "changelog":"%s", "description":"%s", "user":"%s" } }'
    - printf "$template" "$IMAGE_BUILD" "$COMMIT_MSG" "$COMMIT_MSG" "$AUTHOR" > deploymarker.json
    - curl -X POST "https://api.newrelic.com/v2/applications/$NEWRELIC_APP_ID/deployments.json" -H "X-Api-Key:$NEWRELIC_API_KEY_ENV" -i -H "Content-Type:application/json" -d @deploymarker.json
  only:
    - merge_requests
  when: manual

deploy development:master: # workflow: production release
  <<: *deploy
  stage: non-prod deploy
  only:
    - master
  when: manual
  allow_failure: true

deploy staging:
  <<: *deploy
  variables:
    NEWRELIC_APP_ID: $NEWRELIC_APP_ID_STAGING
    NEWRELIC_API_KEY_ENV: $NEWRELIC_API_KEY_NONPROD
  before_script:
    - |
      infos=(${AWS_ACCOUNTS//,/ })
      info=${infos[2]}
      ac_num=${info#*:}
      ac=${info%:*}
  environment:
    name: staging

deploy staging:master: # workflow: production release
  <<: *deploy
  stage: non-prod deploy
  variables:
    NEWRELIC_APP_ID: ${NEWRELIC_APP_ID_STAGING}
    NEWRELIC_API_KEY_ENV: ${NEWRELIC_API_KEY_NONPROD}
  before_script:
    - |
      infos=(${AWS_ACCOUNTS//,/ })
      info=${infos[2]}
      ac_num=${info#*:}
      ac=${info%:*}
  environment:
    name: staging
  only:
    - master
  when: manual
  allow_failure: true

deploy uat:
  <<: *deploy
  variables:
    NEWRELIC_APP_ID: $NEWRELIC_APP_ID_UAT
    NEWRELIC_API_KEY_ENV: $NEWRELIC_API_KEY_NONPROD
  before_script:
    - |
      infos=(${AWS_ACCOUNTS//,/ })
      info=${infos[1]}
      ac_num=${info#*:}
      ac=${info%:*}
  environment:
    name: uat

deploy uat:master: # workflow: production release
  <<: *deploy
  stage: non-prod deploy
  variables:
    NEWRELIC_APP_ID: ${NEWRELIC_APP_ID_UAT}
    NEWRELIC_API_KEY_ENV: ${NEWRELIC_API_KEY_NONPROD}
  before_script:
    - |
      infos=(${AWS_ACCOUNTS//,/ })
      info=${infos[1]}
      ac_num=${info#*:}
      ac=${info%:*}
  environment:
    name: uat
  only:
    - master
  when: manual
  allow_failure: true

deploy production:
  <<: *deploy
  variables:
    NEWRELIC_APP_ID: $NEWRELIC_APP_ID_PROD
    NEWRELIC_API_KEY_ENV: $NEWRELIC_API_KEY_PROD
  before_script:
    - |
      infos=(${AWS_ACCOUNTS//,/ })
      info=${infos[0]}
      ac_num=${info#*:}
      ac=${info%:*}
  environment:
    name: production
  only:
    - master
