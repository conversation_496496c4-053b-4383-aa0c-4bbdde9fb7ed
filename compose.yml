volumes:
  pgdata:
  esdata:
  media:
  static:
  proxy-ssl:

networks:
  racetracks:
    external: true

services:
  memcache:
    image: memcached
    restart: always
  db:
    build:
      context: ./docker-postgres/
    environment:
      RACETRACK_USER: racetrack
      RACETRACK_PASSWORD: racetrackPW
      OAUTH_KEY: longbeach-key
      OAUTH_SECRET: longbeach-secret
      POSTGRES_HOST_AUTH_METHOD: "trust"
    volumes:
      - "./docker-postgres-fixtures:/docker-entrypoint-initdb.d"
      - "pgdata:/var/lib/postgresql/data"
    restart: always
  elasticsearch:
    image: elasticsearch:7.10.1
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - "esdata:/usr/share/elasticsearch/data"
    restart: always
  proxy:
    build:
      context: ./docker-proxy/
    ports:
      - "9009:80"
      - "9449:443"
    environment:
      STACK_DOMAIN: longbeach.racetracks.docker
    volumes:
      - "media:/var/www/media"
      - "static:/var/www/static"
      - "proxy-ssl:/etc/nginx/ssl"
    networks:
      default:
        aliases:
          - app-proxy
      racetracks:
        aliases:
          - longbeach
          - longbeach.racetracks.docker
    restart: always
  app:
    build:
      context: .
      args:
        - GITLAB_ACCESS_USERNAME=$GITLAB_ACCESS_USERNAME
        - GITLAB_ACCESS_PASSWORD=$GITLAB_ACCESS_PASSWORD
    environment:
      PGHOST: db
      PGDATABASE: racetrack
      PGUSER: racetrack
      PGPASSWORD: racetrackPW
      ELASTICSEARCH_URL: http://elasticsearch:9200/
      ELASTICSEARCH_INDEX: racetrack
      GOOGLE_APPLICATION_CREDENTIALS: /racetrack/app/includes/secrets/google_analytics.json
      DJANGO_EMAIL_HOST: mail
      DJANGO_EMAIL_PORT: 1025
    env_file: ./compose.env
    volumes:
      - ".:/racetrack/app"
      - "media:/racetrack/media"
      - "static:/racetrack/static"
    networks:
      default:
        aliases:
          - backend
      racetracks:
    restart: always
    stdin_open: true
    tty: true
  mail:
    image: axllent/mailpit
    ports:
      - "8025:8025"
      - "1025:1025"
    restart: unless-stopped