#!/bin/bash

# Download and source configuration files
if [ "${AWS_CONFIG_STORAGE}" != "" ];then
  aws configure set s3.signature_version s3v4
  aws s3 cp ${AWS_CONFIG_STORAGE}/secrets-main.env ${BUILD_CONFIG_PATH}/.env
  aws s3 cp ${AWS_CONFIG_STORAGE}/newrelic.env ${BUILD_TEMPLATES_PATH}/newrelic.env
  source ${BUILD_TEMPLATES_PATH}/newrelic.env
  rm -f ${BUILD_TEMPLATES_PATH}/newrelic.env
  j2 ${BUILD_TEMPLATES_PATH}/newrelic.ini > ${BUILD_CONFIG_PATH}/newrelic.ini
fi

# Perform collectstatic and update timestamps
STATIC_ROOT=$(python3 -c 'from django.conf import settings; print(settings.STATIC_ROOT)')
if [[ ! -d "$STATIC_ROOT" ]]; then
    ./manage.py collectstatic --noinput -v0
    DEPLOY_TIMESTAMP=$(cat ${BUILD_TIMESTAMP_FILE} )
    if [[ -n "${DEPLOY_TIMESTAMP}" ]]; then
        find "$STATIC_ROOT" -type f -exec touch -m -d "1970-01-01 ${DEPLOY_TIMESTAMP} sec UTC" "{}" \;
    fi
fi

ARG1=$1
shift

case $ARG1 in
    "")
        if [[ ${DJANGO_RUNSERVER} = "True" ]]; then
            exec ./manage.py runserver -v3 0.0.0.0:8000
        else
            exec gunicorn ${DJANGO_PROJECT}.wsgi:application -c docker_gunicorn.py
        fi;;
    manage|manage.py)
        exec ./manage.py $@;;
    *)
        exec "${ARG1}" "$@";;
esac
