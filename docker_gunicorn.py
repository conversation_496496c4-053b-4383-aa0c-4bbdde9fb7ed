import os

TRUE_VALUES = (True, "True", "true", "y", "yes", "1")

DEBUG = os.environ.get("DJANGO_DEBUG", False) in TRUE_VALUES
NEWRELIC = os.environ.get("GUNICORN_NEWRELIC", False) in TRUE_VALUES

bind = [os.environ.get("GUNICORN_BIND", "unix:/racetrack/run/gunicorn.sock")]
proc_name = "%s_gunicorn" % os.environ["DJANGO_PROJECT"]

# Logging
accesslog = "-" if DEBUG else None
loglevel = "debug" if DEBUG else "warning"
errorlog = "-"

# Workers
worker_class = os.environ.get("GUNICORN_WORKER_CLASS", "sync")
workers = int(os.environ.get("GUNICORN_WORKERS", 1))
worker_connections = int(os.environ.get("GUNICORN_WORKER_CONNECTIONS", 2))
max_requests = int(os.environ.get("GUNICORN_MAX_REQUESTS", 0))
timeout = int(os.environ.get("GUNICORN_TIMEOUT", 60))
keepalive = int(os.environ.get("GUNICORN_KEEPALIVE", 0))

# Security
limit_request_line = int(os.environ.get("GUNICORN_LIMIT_REQUEST_LINE", 8190))

# Debugging
reload = DEBUG

# Server mechanics
secure_scheme_headers = {
    "X-FORWARDED-PROTOCOL": "https",
    "X-FORWARDED-PROTO": "https",
    "X-FORWARDED-SSL": "on",
}
# forwarded_allow_ips = * # uses ${FORWARDED_ALLOW_IPS}


def post_fork(server, worker):
    if NEWRELIC:
        import newrelic.agent

        newrelic.agent.initialize()


def post_worker_init(worker):
    if NEWRELIC:
        import newrelic.agent

        newrelic.agent.register_application(timeout=0.0)
